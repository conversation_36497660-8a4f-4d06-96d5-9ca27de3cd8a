# -*- coding: utf-8 -*-
#

from datetime import datetime
import os
from typing import Optional, Dict, Any
from mmd.PmxData import PmxModel
from utils.MLogger import MLogger # noqa
from utils.MException import MParseException

logger = MLogger(__name__)


class MExportOptions:
    """Export options for VRM to PMX conversion.

    This class holds all the configuration options needed for the conversion process.
    """

    def __init__(self, 
                 version_name: str = "", 
                 logging_level: int = 20, 
                 max_workers: int = 1, 
                 vrm_model: Optional[PmxModel] = None, 
                 physics_flg: bool = True, 
                 output_path: str = "", 
                 param_options: Optional[Dict[str, Any]] = None, 
                 monitor: Any = None, 
                 is_file: bool = False, 
                 outout_datetime: Optional[str] = None,
                 vrm_path: str = ""):
        """Initialize export options.

        Args:
            version_name (str): Version name of the converter
            logging_level (int): Logging level (default: 20 - INFO)
            max_workers (int): Maximum number of worker threads
            vrm_model (Optional[PmxModel]): VRM model data
            physics_flg (bool): Whether to include physics
            output_path (str): Output PMX file path
            param_options (Optional[Dict[str, Any]]): Additional parameters
            monitor: Progress monitor object
            is_file (bool): Whether to output to file
            outout_datetime (Optional[str]): Output datetime string
            vrm_path (str): Path to input VRM file
        """
        self.version_name = version_name
        self.logging_level = logging_level
        self.vrm_model = vrm_model
        self.physics_flg = physics_flg
        self.output_path = output_path
        self.param_options = param_options or {}
        self.monitor = monitor
        self.is_file = is_file
        self.outout_datetime = outout_datetime or datetime.now().strftime("%Y%m%d_%H%M%S")
        self.max_workers = max_workers
        self.vrm_path = vrm_path

    def validate(self) -> None:
        """Validate the export options.

        Raises:
            MParseException: If any validation fails
        """
        # Validate VRM path
        if not self.vrm_path:
            raise MParseException("VRM file path is required")
        if not os.path.exists(self.vrm_path):
            raise MParseException(f"VRM file not found: {self.vrm_path}")
        if not os.path.isfile(self.vrm_path):
            raise MParseException(f"VRM path is not a file: {self.vrm_path}")

        # Validate output path
        if not self.output_path:
            raise MParseException("Output path is required")
        
        output_dir = os.path.dirname(self.output_path)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except OSError as e:
                raise MParseException(f"Failed to create output directory: {str(e)}")

        # Validate version name
        if not self.version_name:
            self.version_name = "VRoid2Pmx"

        # Validate max workers
        if self.max_workers < 1:
            self.max_workers = 1

        # Validate logging level
        valid_levels = [10, 20, 30, 40, 50]  # DEBUG, INFO, WARNING, ERROR, CRITICAL
        if self.logging_level not in valid_levels:
            self.logging_level = 20  # Default to INFO

    @classmethod
    def create_for_auto_conversion(cls, vrm_path: str, output_path: str) -> 'MExportOptions':
        """Create export options configured for automatic conversion.

        Args:
            vrm_path (str): Path to input VRM file
            output_path (str): Path for output PMX file

        Returns:
            MExportOptions: Configured export options

        Raises:
            MParseException: If validation fails
        """
        options = cls(
            version_name="VRoid2Pmx Auto",
            logging_level=20,  # INFO
            max_workers=1,
            physics_flg=True,
            output_path=output_path,
            is_file=True,
            vrm_path=vrm_path
        )
        options.validate()
        return options


class MOptionsDataSet:
    """Dataset for options configuration."""
    
    def __init__(self):
        """Initialize options dataset."""
        pass
