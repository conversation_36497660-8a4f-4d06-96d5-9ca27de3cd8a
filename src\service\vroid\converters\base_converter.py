from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from module.MOptions import MExportOptions
from utils.MLogger import MLogger

class BaseConverter(ABC):
    def __init__(self, options: MExportOptions):
        """
        初始化基础转换器
        
        Args:
            options: 导出选项
        """
        self.options = options
        self.logger = MLogger(self.__class__.__name__)

    @abstractmethod
    def convert(self, data: Any) -> Any:
        """
        执行转换操作
        
        Args:
            data: 要转换的数据
            
        Returns:
            转换后的数据
        """
        pass

    def validate_input(self, data: Any) -> bool:
        """
        验证输入数据
        
        Args:
            data: 要验证的数据
            
        Returns:
            bool: 数据是否有效
        """
        return True

    def pre_process(self, data: Any) -> Any:
        """
        预处理数据
        
        Args:
            data: 要预处理的数据
            
        Returns:
            预处理后的数据
        """
        return data

    def post_process(self, data: Any) -> Any:
        """
        后处理数据
        
        Args:
            data: 要后处理的数据
            
        Returns:
            后处理后的数据
        """
        return data

    def handle_error(self, error: Exception) -> None:
        """
        处理转换过程中的错误
        
        Args:
            error: 发生的错误
        """
        self.logger.error(f"转换过程中发生错误: {str(error)}")
        raise error

    def get_metadata(self) -> Dict[str, Any]:
        """
        获取转换器元数据
        
        Returns:
            包含转换器元数据的字典
        """
        return {
            "converter_name": self.__class__.__name__,
            "version": getattr(self.options, "version_name", "unknown")
        }

    def log_progress(self, message: str, progress: Optional[float] = None) -> None:
        """
        记录转换进度
        
        Args:
            message: 进度消息
            progress: 进度百分比（0-100）
        """
        if progress is not None:
            self.logger.info(f"{message} - {progress:.1f}%")
        else:
            self.logger.info(message) 