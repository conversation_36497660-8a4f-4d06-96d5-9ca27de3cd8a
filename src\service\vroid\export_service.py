"""导出服务模块，处理所有与导出相关的功能。

此模块提供了将VRoid模型导出为PMX格式的功能，包括：
- PMX文件的导出
- PMX Tailor设置的导出
- 贴图和其他资源的导出
"""

import os
import json
import logging
from typing import Dict, List, Tuple, Any, Optional
from mmd.PmxData import PmxModel
from mmd.PmxWriter import PmxWriter
from module.MOptions import MExportOptions
from utils.MLogger import MLogger
from utils.MException import SizingException, MKilledException, MParseException
from .clothing_service import ClothingService
from .hair_service import HairService
from .vertex_service import VertexService
from .bone_group_service import BoneGroupService
from .pmx_tailor_service import PmxTailorService
from .model_service import ModelService
from .bone_converter import BoneConverter
from .mesh_converter import MeshConverter
from .morph_converter import MorphConverter
from .stance_service import StanceService
from .body_rigidbody_service import BodyRigidbodyService

logger = MLogger(__name__, level=1)

class ExportService:
    def __init__(self, options: MExportOptions):
        """初始化导出服务

        Args:
            options (MExportOptions): 导出选项
        """
        self.options = options
        self.model_service = ModelService(options)
        self.bone_converter = BoneConverter(options)
        self.mesh_converter = MeshConverter(options)
        self.logger = MLogger(__name__)

    def execute(self) -> bool:
        """Execute export operation"""
        logging.basicConfig(level=self.options.logging_level, format="%(message)s [%(module_name)s]")

        try:
            # Validate input file
            if not os.path.exists(self.options.vrm_model.path):
                raise MParseException(f"Input VRM file not found: {self.options.vrm_model.path}")

            # Log initial information
            service_data_txt = f"{logger.transtext('Starting Vroid2Pmx conversion')}\n------------------------\n{logger.transtext('Version')}: {self.options.version_name}\n"
            service_data_txt = f"{service_data_txt}　{logger.transtext('Source model')}: {os.path.basename(self.options.vrm_model.path)}\n"
            service_data_txt = f"{service_data_txt}　{logger.transtext('Output path')}: {self.options.output_path}\n"

            logger.info(service_data_txt, translate=False, decoration=MLogger.DECORATION_BOX)

            # Validate and create output directory
            output_dir = os.path.dirname(self.options.output_path)
            if not output_dir:
                raise MParseException("Invalid output path - no directory specified")

            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir)
                except OSError as e:
                    raise MParseException(f"Failed to create output directory: {str(e)}")
            elif os.path.exists(self.options.output_path):
                logger.warning("Output file already exists, it will be overwritten: %s", self.options.output_path)

            # Convert model
            logger.info("Starting VRM to PMX conversion", decoration=MLogger.DECORATION_LINE)
            model = self.vroid2pmx()
            if not model:
                raise MParseException("Failed to convert VRM to PMX")

            # Write output
            logger.info("Writing PMX file", decoration=MLogger.DECORATION_LINE)
            try:
                PmxWriter().write(model, self.options.output_path)
            except Exception as e:
                raise MParseException(f"Failed to write PMX file: {str(e)}")

            if not os.path.exists(self.options.output_path):
                raise MParseException("PMX file was not created")

            # Verify file size
            if os.path.getsize(self.options.output_path) == 0:
                raise MParseException("PMX file was created but is empty")

            logger.info(
                "Conversion completed successfully: %s", 
                os.path.basename(self.options.output_path), 
                decoration=MLogger.DECORATION_BOX, 
                title="Success"
            )

            return True

        except MKilledException:
            logger.error("Operation cancelled by user", decoration=MLogger.DECORATION_BOX)
            return False
        except SizingException as se:
            logger.error(
                "Conversion failed due to invalid data.\n\n%s\n%s",
                self.options.version_name,
                se.message,
                decoration=MLogger.DECORATION_BOX,
            )
            return False
        except MParseException as pe:
            logger.error(
                "Conversion failed.\n\n%s",
                str(pe),
                decoration=MLogger.DECORATION_BOX,
            )
            return False
        except Exception as e:
            import traceback
            logger.critical(
                "Conversion failed due to unexpected error.\n\n%s",
                traceback.format_exc(),
                decoration=MLogger.DECORATION_BOX,
            )
            return False
        finally:
            logging.shutdown()

    def vroid2pmx(self) -> Optional[PmxModel]:
        """Convert VRoid model to PMX model"""
        try:
            # Create model
            logger.info("Creating base model")
            model, tex_dir_path, setting_dir_path, is_vroid1 = self.model_service.create_model()
            if not model:
                raise MParseException("Failed to create base model")

            # Convert bones
            logger.info("Converting bones")
            model, bone_name_dict = self.bone_converter.convert_bone(model)
            if not model:
                raise MParseException("Failed to convert bones")

            # Convert mesh
            logger.info("Converting mesh")
            model = self.mesh_converter.convert_mesh(model, bone_name_dict, tex_dir_path)
            if not model:
                raise MParseException("Failed to convert mesh")

            # Reconvert bones
            logger.info("Reconverting bones")
            model = self.bone_converter.reconvert_bone(model)
            if not model:
                raise MParseException("Failed to reconvert bones")

            # Convert morphs
            logger.info("Converting morphs")
            morph_converter = MorphConverter(model)
            model = morph_converter.convert_morph(model, is_vroid1)
            if not model:
                raise MParseException("Failed to convert morphs")

            # Transfer stance
            logger.info("Transferring stance")
            stance_service = StanceService(model)
            model = stance_service.transfer_stance()
            if not model:
                raise MParseException("Failed to transfer stance")

            # Create rigid bodies
            logger.info("Creating rigid bodies")
            body_rigidbody_service = BodyRigidbodyService(model)
            body_rigidbody_service.prepare_bone_vertex_mapping()
            body_rigidbody_service.create_body_rigidbodies()

            # Export PMX Tailor settings
            logger.info("Exporting PMX Tailor settings")
            self.export_pmxtailor_setting(model, setting_dir_path)

            return model

        except MKilledException as ke:
            raise ke
        except SizingException as se:
            logger.error("Conversion failed due to invalid data.\n\n%s", se.message, decoration=MLogger.DECORATION_BOX)
            raise se
        except MParseException as pe:
            logger.error("Conversion failed.\n\n%s", str(pe), decoration=MLogger.DECORATION_BOX)
            raise pe
        except Exception as e:
            import traceback
            logger.critical(
                "Conversion failed due to unexpected error.\n\n%s",
                traceback.format_exc(),
                decoration=MLogger.DECORATION_BOX,
            )
            raise e

    @staticmethod
    def export_pmxtailor_setting(model: PmxModel, setting_dir_path: str) -> None:
        """导出PMX Tailor设置

        Args:
            model (PmxModel): PMX模型
            setting_dir_path (str): 设置文件输出目录
        """
        try:
            pmx_tailor_service = PmxTailorService(model)
            pmx_tailor_service.export_settings(setting_dir_path)
        except Exception as e:
            logger.error(f"Failed to export PMX Tailor settings: {str(e)}") 