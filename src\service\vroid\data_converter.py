"""数据转换服务模块，处理各种数据格式的转换。

此模块提供了在VRoid到PMX转换过程中需要的各种数据转换功能，包括：
- 顶点索引和材质的对应关系处理
- 骨骼和材质的映射关系处理
- 权重数据的转换和处理
"""

from typing import Dict, List, Tuple, Set
from mmd.PmxData import PmxModel, Vertex, Bdef1, Bdef2, Bdef4
import logging

logger = logging.getLogger(__name__)

class DataConverter:
    """数据转换服务类，提供各种数据转换功能"""
    
    @staticmethod
    def create_bone_material_mapping(
        model: PmxModel,
        weight_threshold: float = 0.3
    ) -> Tuple[Dict[str, List[Tuple[int, str]]], Dict[str, List[str]]]:
        """创建骨骼和材质的映射关系
        
        Args:
            model: PMX模型对象
            weight_threshold: 权重阈值，默认0.3
            
        Returns:
            bone_materials: 骨骼到材质的映射字典
            material_bones: 材质到骨骼的映射字典
        """
        bone_materials = {}
        material_bones = {}
        
        for bone_idx, bone_vidxs in model.vertices.items():
            bone_name = model.bone_indexes.get(bone_idx)
            if not bone_name:
                continue
                
            for material_name, vidxs in model.material_vertices.items():
                # 获取权重超过阈值的顶点
                weighted_vidxs = [
                    vidx
                    for vidx in list(set(vidxs) & set(bone_vidxs))
                    if bone_idx in model.vertex_dict[vidx].deform.get_idx_list(weight_threshold)
                ]
                
                if weighted_vidxs:
                    if bone_name not in bone_materials:
                        bone_materials[bone_name] = []
                    if material_name not in bone_materials[bone_name]:
                        bone_materials[bone_name].append((len(weighted_vidxs), material_name))
                        
                    if material_name not in material_bones:
                        material_bones[material_name] = []
                    if bone_name not in material_bones[material_name]:
                        material_bones[material_name].append(bone_name)
        
        return bone_materials, material_bones
    
    @staticmethod
    def get_vertex_material_groups(
        model: PmxModel,
        material_name: str
    ) -> Dict[str, Set[int]]:
        """获取指定材质的顶点分组
        
        Args:
            model: PMX模型对象
            material_name: 材质名称
            
        Returns:
            按骨骼分组的顶点索引集合
        """
        vertex_groups = {}
        
        if material_name in model.material_vertices:
            material_vidxs = set(model.material_vertices[material_name])
            
            for bone_idx, bone_vidxs in model.vertices.items():
                bone_name = model.bone_indexes.get(bone_idx)
                if not bone_name:
                    continue
                    
                # 获取该骨骼下属于指定材质的顶点
                bone_material_vidxs = material_vidxs & set(bone_vidxs)
                if bone_material_vidxs:
                    vertex_groups[bone_name] = bone_material_vidxs
        
        return vertex_groups
    
    @staticmethod
    def convert_vertex_weights(vertex: Vertex, bone_indexes: Dict[str, int]) -> None:
        """转换顶点的权重数据
        
        Args:
            vertex: 顶点对象
            bone_indexes: 骨骼名称到索引的映射
        """
        # 这个方法需要重新设计，因为权重对象没有get_weights方法
        # 暂时跳过权重转换，直接使用现有的权重对象
        # TODO: 实现正确的权重转换逻辑
        if vertex.deform is None:
            vertex.deform = Bdef1(0)
            return

        # 如果已经有权重对象，保持不变
        return

