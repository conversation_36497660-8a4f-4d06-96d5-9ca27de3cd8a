"""
Morph conversion functionality for VRoid to PMX
"""

from typing import Dict, List
from mmd.PmxData import PmxModel, Morph, VertexMorphOffset
from utils.MLogger import MLogger
from .constants import MORPH_PAIRS

logger = MLogger(__name__)

class MorphConverter:
    def __init__(self, options):
        self.options = options
        self.model = None
        
    def convert_morph(self, model: PmxModel, is_vroid1: bool) -> PmxModel:
        """Convert morphs from VRoid to PMX format"""
        logger.info("-- モーフを作成")
        self.model = model
        
        # Create morphs based on MORPH_PAIRS
        for morph_name, morph_data in MORPH_PAIRS.items():
            # Create base morph
            morph = Morph()
            morph.name = morph_data["name"]
            morph.english_name = morph_name
            morph.panel = morph_data["panel"]
            morph.morph_type = 1  # Vertex morph
            
            # Add vertex offsets
            if morph_name in self.model.json_data.get("morphs", {}):
                morph_info = self.model.json_data["morphs"][morph_name]
                for vertex_idx, offset in morph_info.items():
                    vertex_morph = VertexMorphOffset(
                        vertex_index=int(vertex_idx),
                        position=offset
                    )
                    morph.offsets.append(vertex_morph)
            
            # Handle special morphs
            if "material" in morph_data:
                # Add material reference
                morph.material_name = morph_data["material"]
                
            if "edge" in morph_data and morph_data["edge"]:
                # Handle edge morphs
                morph.edge_flag = True
            
            # Add to model
            self.model.morphs[morph.name] = morph
            
        return self.model 