# UPBGE MMD摄像机创建器使用说明

这是一个专门为UPBGE游戏引擎设计的MMD摄像机创建组件，使用正确的Blender API来避免"object must be in an inactive layer"错误。

## 核心特点

- **使用Blender API**: 参考`camera.py`的`convertToMMDCamera`方法，使用`bpy.data.objects.new()`创建对象
- **避免层级错误**: 不使用`scene.addObject()`，直接通过Blender API创建
- **完整MMD结构**: 创建标准的MMD父子摄像机（Empty父对象 + Camera子对象）
- **属性完备**: 设置所有必要的MMD摄像机属性，包括锁定、DOF等

## 快速开始

### 1. 基本使用

```python
# 方法1: 添加组件到任意游戏对象
# 在UPBGE中将 UPBGEMMDCameraCreator 组件添加到Cube或其他对象上
# 运行游戏，组件会自动创建MMD摄像机

# 方法2: 使用键盘控制
# 添加 UPBGECameraKeyboardControl 组件
# C键创建摄像机，D键销毁摄像机

# 方法3: 代码调用
from mmd_camera_creator_upbge import create_upbge_mmd_camera
result = create_upbge_mmd_camera()
```

### 2. 组件参数

- `parent_name`: 父对象名称（默认"MMD_Camera"）
- `camera_name`: 摄像机名称（默认"Camera"）
- `initial_position`: 父对象初始位置[x, y, z]（默认[0.0, 0.0, 10.0]）
- `initial_rotation`: 父对象初始旋转[rx, ry, rz]度（默认[0.0, 0.0, 0.0]）
- `camera_distance`: 摄像机距离（默认-45.0）
- `camera_lens`: 摄像机镜头焦距（默认35.0）
- `auto_create`: 是否自动创建（默认True）
- `set_as_active`: 是否设置为活动摄像机（默认True）
- `scale`: 缩放比例（默认1.0）

## 详细功能说明

### 创建过程

1. **检查现有对象**: 避免创建重名对象
2. **创建Empty父对象**: 使用`bpy.data.objects.new()`
3. **创建Camera对象**: 包含摄像机数据
4. **设置父子关系**: `camera_obj.parent = empty`
5. **配置属性**: 参考MMD标准设置所有属性
6. **链接到场景**: 自动处理Blender 2.8+的collection系统

### MMD标准属性

```python
# 摄像机数据属性
camera_data.sensor_fit = "VERTICAL"
camera_data.lens_unit = "MILLIMETERS" 
camera_data.ortho_scale = 25 * scale
camera_data.clip_end = 500 * scale
camera_data.display_size = 5 * scale

# 摄像机变换
camera_obj.location = (0, distance * scale, 0)
camera_obj.rotation_euler = (math.radians(90), 0, 0)

# 锁定设置
camera_obj.lock_location = (True, False, True)
camera_obj.lock_rotation = (True, True, True)
camera_obj.lock_scale = (True, True, True)

# DOF焦点
camera_data.dof.focus_object = empty

# Empty属性
empty.rotation_mode = "YXZ"
empty.empty_display_size = 5 * scale
empty.lock_scale = (True, True, True)
```

## API使用

### 基本控制

```python
# 获取组件
creator = obj['UPBGEMMDCameraCreator']

# 手动创建（如果auto_create=False）
success = creator.create_mmd_camera()

# 销毁摄像机
creator.destroy_camera()

# 获取对象信息
info = creator.get_camera_objects()
print(f"父对象: {info['parent']}")
print(f"摄像机: {info['camera']}")
print(f"创建状态: {info['created']}")
```

### 运行时创建

```python
from mmd_camera_creator_upbge import create_upbge_mmd_camera

# 创建标准摄像机
result = create_upbge_mmd_camera()

# 创建自定义摄像机
result = create_upbge_mmd_camera(
    parent_name="MyMMDCamera",
    camera_name="MyCamera", 
    position=(5, 5, 8),
    rotation=(15, 0, 45),
    distance=-30.0,
    scale=1.5
)

if result and result['created']:
    print("创建成功!")
    parent_obj = result['parent']
    camera_obj = result['camera']
else:
    print("创建失败!")
```

### 与控制器结合

```python
# 创建摄像机后，可以与MMDCameraController配合使用
from mmd_camera_controller import MMDCameraController

# 1. 创建摄像机
result = create_upbge_mmd_camera("AnimatedCamera", "AnimatedCam")

if result['created']:
    # 2. 添加控制器组件到某个对象上
    controller_args = {
        "camera_name": "AnimatedCam", 
        "parent_name": "AnimatedCamera",
        "auto_start": True,
    }
    # 在UPBGE界面中添加MMDCameraController组件并配置参数
```

## 高级用法

### 批量创建摄像机

```python
def create_multiple_cameras():
    """创建多个摄像机视角"""
    cameras = []
    
    configs = [
        {"name": "Front", "pos": (0, 0, 10), "rot": (0, 0, 0)},
        {"name": "Side", "pos": (15, 0, 8), "rot": (0, 0, 90)},
        {"name": "Top", "pos": (0, 0, 20), "rot": (90, 0, 0)},
    ]
    
    for config in configs:
        result = create_upbge_mmd_camera(
            parent_name=f"MMD_{config['name']}",
            camera_name=f"Camera_{config['name']}", 
            position=config['pos'],
            rotation=config['rot']
        )
        if result:
            cameras.append(result)
    
    return cameras
```

### 摄像机切换器

```python
class CameraSwitcher(bge.types.KX_PythonComponent):
    """摄像机切换组件"""
    
    args = {"switch_key": bge.events.TABKEY}
    
    def start(self, args):
        self.switch_key = args.get("switch_key", bge.events.TABKEY)
        self.keyboard = bge.logic.keyboard
        self.cameras = []
        self.current_index = 0
        
        # 收集所有摄像机
        scene = bge.logic.getCurrentScene()
        for obj in scene.objects:
            if hasattr(obj, 'camera') and obj.camera:
                self.cameras.append(obj)
        
        print(f"找到 {len(self.cameras)} 个摄像机")
    
    def update(self):
        if self.keyboard.events[self.switch_key] == bge.logic.KX_INPUT_JUST_ACTIVATED:
            if self.cameras:
                self.current_index = (self.current_index + 1) % len(self.cameras)
                scene = bge.logic.getCurrentScene()
                scene.active_camera = self.cameras[self.current_index]
                print(f"切换到: {self.cameras[self.current_index].name}")
```

## 故障排除

### 常见问题

1. **"object must be in an inactive layer"错误**
   - 这个组件已经解决了这个问题，使用Blender API而不是scene.addObject()

2. **对象创建失败**
   - 检查是否有重名对象
   - 确保Blender context可用

3. **摄像机不可见**
   - 确认摄像机设置为活动摄像机
   - 检查摄像机位置和方向

4. **属性设置失败**
   - 确保对象创建成功后再设置属性
   - 检查UPBGE版本兼容性

### 调试技巧

```python
# 启用详细调试信息
def debug_camera_creation():
    print("=== MMD摄像机创建调试 ===")
    
    # 检查Blender API可用性
    try:
        import bpy
        print(f"Blender API版本: {bpy.app.version}")
        print(f"场景数量: {len(bpy.data.scenes)}")
    except Exception as e:
        print(f"Blender API错误: {e}")
    
    # 检查游戏场景
    scene = bge.logic.getCurrentScene()
    print(f"游戏场景对象数量: {len(scene.objects)}")
    
    # 尝试创建摄像机
    result = create_upbge_mmd_camera("Debug_MMD", "Debug_Cam")
    if result:
        print("调试创建成功!")
    else:
        print("调试创建失败!")
```

## 性能考虑

1. **避免频繁创建**: 摄像机创建是相对重的操作
2. **及时清理**: 使用`destroy_camera()`销毁不需要的摄像机
3. **限制数量**: 不要同时创建过多摄像机

## 与MMD Tools集成

这个创建器创建的摄像机完全兼容MMD Tools的其他功能：

- 可以导入VMD摄像机动画
- 支持MMD驱动器和约束
- 兼容现有的MMD工作流

```python
# 示例：创建摄像机后导入VMD动画
result = create_upbge_mmd_camera()
if result['created']:
    # 使用MMD Tools的VMD导入器为摄像机添加动画
    # 具体方法需要参考VMD导入器的使用说明
    pass
```

这个新的组件解决了之前的layer错误问题，提供了稳定可靠的MMD摄像机创建功能！ 