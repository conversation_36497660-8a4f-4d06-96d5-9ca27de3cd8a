#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试已编译的Cython模块
"""

import os
import sys

# 添加src目录到路径
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_modules():
    print("🔍 测试编译后的Cython模块...")
    
    try:
        # 测试数学模块
        print("  📐 测试 module.MMath...")
        from module.MMath import MVector3D, MQuaternion, MMatrix4x4
        v1 = MVector3D(1, 2, 3)
        v2 = MVector3D(4, 5, 6)
        v3 = v1 + v2
        print(f"    ✅ MVector3D 加法: {v1} + {v2} = {v3}")
        
        # 测试四元数
        q = MQuaternion.fromEulerAngles(30, 45, 60)
        euler = q.toEulerAngles()
        print(f"    ✅ 四元数转换: 原始({30}, {45}, {60}) -> 转换后{euler}")
        
        # 测试参数模块
        print("  ⚙️  测试 module.MParams...")
        from module.MParams import BoneLinks
        links = BoneLinks()
        print(f"    ✅ BoneLinks 创建成功")
        
        # 测试工具模块
        print("  🛠️  测试 utils.MBezierUtils...")
        from utils.MBezierUtils import LINEAR_MMD_INTERPOLATION
        print(f"    ✅ 贝塞尔工具: LINEAR_MMD_INTERPOLATION = {LINEAR_MMD_INTERPOLATION}")
        
        # 测试MMD数据模块
        print("  💃 测试 mmd.VmdData...")
        from mmd.VmdData import VmdBoneFrame
        frame = VmdBoneFrame(100)
        frame.set_name("测试骨骼")
        print(f"    ✅ VmdBoneFrame 创建: {frame.name} at frame {frame.fno}")
        
        # 测试PMX数据模块  
        print("  🎭 测试 mmd.PmxData...")
        from mmd.PmxData import PmxModel
        model = PmxModel()
        print(f"    ✅ PmxModel 创建成功")
        
        print("\n🎉 所有模块测试通过！编译成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modules()
    if success:
        print("\n✨ Vroid2PMX Cython模块编译和测试完成！")
        print("📂 编译后的文件位置:")
        print("   📁 src/module/*.pyd")
        print("   📁 src/utils/*.pyd") 
        print("   📁 src/mmd/*.pyd")
        print("\n🚀 你现在可以运行主程序了!")
    else:
        print("\n⚠️  某些模块可能存在问题，请检查编译输出") 