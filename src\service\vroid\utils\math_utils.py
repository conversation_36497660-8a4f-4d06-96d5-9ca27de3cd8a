import numpy as np
import math
from typing import List, Tu<PERSON>, Union
from module.MMath import MVector2D, MVector3D, MVector4D, MQuaternion, MMatrix4x4

class MathUtils:
    @staticmethod
    def calculate_center_point(points: List[MVector3D]) -> MVector3D:
        """计算一组点的中心点"""
        if not points:
            return MVector3D()
        x = sum(p.x() for p in points) / len(points)
        y = sum(p.y() for p in points) / len(points)
        z = sum(p.z() for p in points) / len(points)
        return MVector3D(x, y, z)

    @staticmethod
    def calculate_distance(point1: MVector3D, point2: MVector3D) -> float:
        """计算两点之间的距离"""
        return math.sqrt(
            (point2.x() - point1.x()) ** 2 +
            (point2.y() - point1.y()) ** 2 +
            (point2.z() - point1.z()) ** 2
        )

    @staticmethod
    def interpolate_points(start: MVector3D, end: MVector3D, t: float) -> MVector3D:
        """在两点之间进行线性插值"""
        return MVector3D(
            start.x() + (end.x() - start.x()) * t,
            start.y() + (end.y() - start.y()) * t,
            start.z() + (end.z() - start.z()) * t
        )

    @staticmethod
    def calculate_normal(vertices: List[MVector3D]) -> MVector3D:
        """计算三角形的法线"""
        if len(vertices) < 3:
            return MVector3D(0, 1, 0)
        
        v1 = vertices[1] - vertices[0]
        v2 = vertices[2] - vertices[0]
        normal = v1.cross(v2)
        normal.normalize()
        return normal

    @staticmethod
    def transform_point(point: MVector3D, matrix: MMatrix4x4) -> MVector3D:
        """使用变换矩阵转换点的位置"""
        result = matrix.transform(point)
        return result

    @staticmethod
    def calculate_bone_direction(start: MVector3D, end: MVector3D) -> MQuaternion:
        """计算骨骼的方向四元数"""
        direction = end - start
        direction.normalize()
        
        # 计算从上方向到目标方向的旋转
        up = MVector3D(0, 1, 0)
        rotation_axis = up.cross(direction)
        
        if rotation_axis.length() < 0.000001:
            # 如果旋转轴太小，说明方向平行或反平行
            if direction.y() > 0:
                return MQuaternion()
            else:
                return MQuaternion.fromAxisAndAngle(MVector3D(1, 0, 0), math.pi)
        
        rotation_axis.normalize()
        angle = math.acos(up.dot(direction))
        return MQuaternion.fromAxisAndAngle(rotation_axis, angle)

    @staticmethod
    def calculate_bounding_box(points: List[MVector3D]) -> Tuple[MVector3D, MVector3D]:
        """计算一组点的包围盒（返回最小点和最大点）"""
        if not points:
            return MVector3D(), MVector3D()
        
        min_x = min(p.x() for p in points)
        min_y = min(p.y() for p in points)
        min_z = min(p.z() for p in points)
        max_x = max(p.x() for p in points)
        max_y = max(p.y() for p in points)
        max_z = max(p.z() for p in points)
        
        return MVector3D(min_x, min_y, min_z), MVector3D(max_x, max_y, max_z)

    @staticmethod
    def calculate_weighted_average(points: List[MVector3D], weights: List[float]) -> MVector3D:
        """计算加权平均点"""
        if not points or not weights or len(points) != len(weights):
            return MVector3D()
        
        total_weight = sum(weights)
        if total_weight == 0:
            return MVector3D()
        
        x = sum(p.x() * w for p, w in zip(points, weights)) / total_weight
        y = sum(p.y() * w for p, w in zip(points, weights)) / total_weight
        z = sum(p.z() * w for p, w in zip(points, weights)) / total_weight
        
        return MVector3D(x, y, z) 