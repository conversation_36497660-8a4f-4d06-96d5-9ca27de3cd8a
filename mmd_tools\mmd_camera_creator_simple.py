# import bge
# import mathutils
# import math

# class SimpleMMDCameraCreator(bge.types.KX_PythonComponent):
#     """
#     简化版MMD摄像机创建器组件
#     专门针对UPBGE组件系统优化
#     """
    
#     # 组件参数
#     args = {
#         "parent_name": "MMD_Camera",
#         "camera_name": "Camera", 
#         "auto_create": True,
#         "set_as_active": True,
#     }
    
#     def start(self, args):
#         """初始化组件"""
#         self.parent_name = args.get("parent_name", "MMD_Camera")
#         self.camera_name = args.get("camera_name", "Camera")
#         self.auto_create = args.get("auto_create", True)
#         self.set_as_active = args.get("set_as_active", True)
        
#         self.created = False
#         self.parent_obj = None
#         self.camera_obj = None
        
#         print(f"SimpleMMDCameraCreator初始化完成")
#         print(f"组件所属对象: {self.object.name if hasattr(self, 'object') else '未知'}")
        
#         if self.auto_create:
#             self.create_camera()
    
#     def create_camera(self):
#         """创建MMD摄像机结构"""
#         if self.created:
#             print("摄像机已经创建过了")
#             return True
        
#         scene = bge.logic.getCurrentScene()
        
#         try:
#             # 步骤1: 创建或找到父对象
#             if not self._setup_parent_object(scene):
#                 return False
            
#             # 步骤2: 创建或找到摄像机对象
#             if not self._setup_camera_object(scene):
#                 return False
            
#             # 步骤3: 设置关系和属性
#             self._setup_camera_system()
            
#             # 步骤4: 设置为活动摄像机
#             if self.set_as_active:
#                 scene.active_camera = self.camera_obj
            
#             self.created = True
#             print(f"MMD摄像机创建成功: {self.parent_name} -> {self.camera_name}")
#             return True
            
#         except Exception as e:
#             print(f"创建摄像机失败: {e}")
#             import traceback
#             traceback.print_exc()
#             return False
    
#     def _setup_parent_object(self, scene):
#         """设置父对象"""
#         # 检查是否已存在
#         existing_parent = scene.objects.get(self.parent_name)
#         if existing_parent:
#             print(f"使用现有父对象: {self.parent_name}")
#             self.parent_obj = existing_parent
#             return True
        
#         # 创建新的父对象
#         if not hasattr(self, 'object') or not self.object:
#             print("错误: 组件没有关联的游戏对象")
#             return False
        
#         try:
#             # 使用当前对象作为基础创建父对象
#             self.parent_obj = scene.addObject(self.object.name, self.object)
#             self.parent_obj.name = self.parent_name
            
#             # 隐藏网格（让它表现像Empty对象）
#             if hasattr(self.parent_obj, 'meshes'):
#                 for mesh in self.parent_obj.meshes:
#                     mesh.visible = False
            
#             # 设置初始位置
#             self.parent_obj.worldPosition = [0.0, 0.0, 10.0]
#             self.parent_obj.worldOrientation = mathutils.Euler([0, 0, 0], 'XYZ')
            
#             print(f"创建新父对象: {self.parent_name}")
#             return True
            
#         except Exception as e:
#             print(f"创建父对象失败: {e}")
#             return False
    
#     def _setup_camera_object(self, scene):
#         """设置摄像机对象"""
#         # 检查是否已存在
#         existing_camera = scene.objects.get(self.camera_name)
#         if existing_camera:
#             print(f"使用现有摄像机: {self.camera_name}")
#             self.camera_obj = existing_camera
#             return True
        
#         # 寻找摄像机模板
#         camera_template = None
        
#         # 首先尝试找专门的模板
#         camera_template = scene.objects.get("CameraTemplate")
        
#         # 如果没有模板，找任意摄像机
#         if not camera_template:
#             for obj in scene.objects:
#                 if hasattr(obj, 'camera') and obj.camera:
#                     camera_template = obj
#                     break
        
#         if not camera_template:
#             print("错误: 场景中没有摄像机对象可以作为模板")
#             return False
        
#         try:
#             # 创建摄像机对象
#             self.camera_obj = scene.addObject(camera_template.name, self.object)
#             self.camera_obj.name = self.camera_name
            
#             print(f"创建新摄像机: {self.camera_name}")
#             return True
            
#         except Exception as e:
#             print(f"创建摄像机失败: {e}")
#             return False
    
#     def _setup_camera_system(self):
#         """设置摄像机系统"""
#         if not self.parent_obj or not self.camera_obj:
#             return
        
#         # 设置父子关系
#         self.camera_obj.setParent(self.parent_obj)
        
#         # 设置摄像机本地变换
#         self.camera_obj.localPosition = [0.0, -45.0, 0.0]  # MMD标准距离
#         self.camera_obj.localOrientation = mathutils.Euler([math.radians(90), 0, 0], 'XYZ')
        
#         # 设置摄像机属性
#         if hasattr(self.camera_obj, 'camera'):
#             camera = self.camera_obj.camera
#             if hasattr(camera, 'lens'):
#                 camera.lens = 35.0
#             if hasattr(camera, 'clipStart'):
#                 camera.clipStart = 0.1
#             if hasattr(camera, 'clipEnd'):
#                 camera.clipEnd = 1000.0
        
#         print("摄像机系统设置完成")
    
#     def destroy_camera(self):
#         """销毁创建的摄像机"""
#         if not self.created:
#             return
        
#         scene = bge.logic.getCurrentScene()
        
#         # 切换活动摄像机
#         if scene.active_camera == self.camera_obj:
#             for obj in scene.objects:
#                 if hasattr(obj, 'camera') and obj.camera and obj != self.camera_obj:
#                     scene.active_camera = obj
#                     break
        
#         # 销毁对象
#         if self.camera_obj:
#             self.camera_obj.endObject()
#             self.camera_obj = None
        
#         if self.parent_obj:
#             self.parent_obj.endObject()
#             self.parent_obj = None
        
#         self.created = False
#         print("MMD摄像机已销毁")
    
#     def get_objects(self):
#         """获取创建的对象"""
#         return {
#             'parent': self.parent_obj,
#             'camera': self.camera_obj,
#             'created': self.created
#         }
    
#     def update(self):
#         """每帧更新"""
#         pass


# # 键盘控制示例
# class SimpleCameraKeyboardControl(bge.types.KX_PythonComponent):
#     """简化的键盘控制"""
    
#     args = {}
    
#     def start(self, args):
#         self.keyboard = bge.logic.keyboard
#         self.creator = None
#         print("键盘控制已启动: C键创建, D键销毁")
    
#     def update(self):
#         # C键创建摄像机
#         if self.keyboard.events[bge.events.CKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if not self.creator:
#                 self.creator = SimpleMMDCameraCreator()
#                 self.creator.object = self.object
#                 self.creator.start({
#                     "parent_name": "MMD_Camera",
#                     "camera_name": "Camera",
#                     "auto_create": False,
#                 })
            
#             self.creator.create_camera()
        
#         # D键销毁摄像机
#         if self.keyboard.events[bge.events.DKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if self.creator:
#                 self.creator.destroy_camera()


# # 简化的运行时创建函数
# def create_simple_mmd_camera(scene, parent_name="MMD_Camera", camera_name="Camera"):
#     """简化的运行时创建函数"""
#     # 找一个参考对象
#     reference_obj = None
#     for obj in scene.objects:
#         reference_obj = obj
#         break
    
#     if not reference_obj:
#         print("错误: 场景中没有对象")
#         return None
    
#     # 创建组件实例
#     creator = SimpleMMDCameraCreator()
#     creator.object = reference_obj
#     creator.start({
#         "parent_name": parent_name,
#         "camera_name": camera_name,
#         "auto_create": False,
#         "set_as_active": True,
#     })
    
#     # 创建摄像机
#     if creator.create_camera():
#         return creator.get_objects()
#     else:
#         return None


# # 使用示例函数
# def demo_create_camera():
#     """演示如何创建摄像机"""
#     scene = bge.logic.getCurrentScene()
    
#     result = create_simple_mmd_camera(scene, "Demo_MMD_Camera", "Demo_Camera")
    
#     if result:
#         print("摄像机创建成功!")
#         print(f"父对象: {result['parent'].name}")
#         print(f"摄像机: {result['camera'].name}")
#     else:
#         print("摄像机创建失败!")
    
#     return result 