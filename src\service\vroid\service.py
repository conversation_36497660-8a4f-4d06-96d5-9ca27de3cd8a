"""VRoid到PMX转换服务模块

此模块提供了VRoid模型到PMX模型的转换功能，包括：
- 模型创建和初始化
- 骨骼转换
- 网格转换
- 变形处理
- 姿势调整
- 物理设置
- PMX Tailor设置导出
"""

import os
import logging
import traceback
from typing import Optional, Tuple, Dict, Any

from module.MOptions import MExportOptions
from mmd.PmxData import PmxModel
from mmd.PmxWriter import PmxWriter
from utils.MLogger import MLogger
from utils.MException import SizingException, MKilledException

from .model_service import ModelService
from .bone_converter import BoneConverter
from .mesh_converter import MeshConverter
from .morph_converter import MorphConverter
from .stance_service import StanceService
from .body_rigidbody_service import BodyRigidbodyService
from .pmx_tailor_service import PmxTailorService

logger = MLogger(__name__, level=1)

class VroidExportService:
    """VRoid到PMX转换服务的主类"""

    def __init__(self, options: MExportOptions):
        """初始化VRoid导出服务

        Args:
            options (MExportOptions): 导出选项
        """
        self.options = options
        self.model_service = ModelService(options)
        self.bone_converter = BoneConverter(options)
        self.mesh_converter = MeshConverter(options)
        self.morph_converter = MorphConverter(options)
        self.stance_service = StanceService
        self.body_rigidbody_service = BodyRigidbodyService

    def execute(self) -> bool:
        """执行VRoid到PMX的转换

        Returns:
            bool: 转换是否成功
        """
        logging.basicConfig(level=self.options.logging_level, format="%(message)s [%(module_name)s]")

        try:
            # 输出服务信息
            service_data_txt = f"{logger.transtext('Vroid2Pmx処理実行')}\n------------------------\n"
            service_data_txt += f"{logger.transtext('exeバージョン')}: {self.options.version_name}\n"
            service_data_txt += f"{logger.transtext('元モデル')}: {os.path.basename(self.options.vrm_model.path)}\n"
            logger.info(service_data_txt, translate=False, decoration=MLogger.DECORATION_BOX)

            # 执行转换
            model = self.vroid2pmx()
            if not model:
                return False

            # 输出PMX文件
            logger.info("PMX出力開始", decoration=MLogger.DECORATION_LINE)
            os.makedirs(os.path.dirname(self.options.output_path), exist_ok=True)
            PmxWriter().write(model, self.options.output_path)
            logger.info(
                "出力終了: %s",
                os.path.basename(self.options.output_path),
                decoration=MLogger.DECORATION_BOX,
                title="成功"
            )

            return True

        except MKilledException:
            return False
        except SizingException as se:
            logger.error(
                "Vroid2Pmx処理が処理できないデータで終了しました。\n\n%s\n%s",
                "2.01.06",
                se.message,
                decoration=MLogger.DECORATION_BOX,
            )
            return False
        except Exception:
            logger.critical(
                "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s\n%s",
                "2.01.06",
                traceback.format_exc(),
                decoration=MLogger.DECORATION_BOX,
            )
            return False
        finally:
            logging.shutdown()

    def vroid2pmx(self) -> Optional[PmxModel]:
        """执行VRoid到PMX的转换过程

        Returns:
            Optional[PmxModel]: 转换后的PMX模型，如果转换失败则返回None
        """
        try:
            # 创建模型
            model, tex_dir_path, setting_dir_path, is_vroid1 = self.model_service.create_model()
            if not model:
                return None

            # 转换骨骼
            model, bone_name_dict = self.bone_converter.convert_bone(model)
            if not model:
                return None

            # 转换网格
            model = self.mesh_converter.convert_mesh(model, bone_name_dict, tex_dir_path)
            if not model:
                return None

            # 重新转换骨骼
            model = self.bone_converter.reconvert_bone(model)
            if not model:
                return None

            # 转换变形
            model = self.morph_converter.convert_morph(model, is_vroid1)
            if not model:
                return None

            # 调整姿势
            model = self.stance_service(model).transfer_stance()
            if not model:
                return None

            # 创建刚体
            model = self.body_rigidbody_service(model).create_body_rigidbodies()
            if not model:
                return None

            # 导出PMX Tailor设置
            pmx_tailor_service = PmxTailorService(model)
            pmx_tailor_service.export_pmxtailor_setting(setting_dir_path)

            return model

        except MKilledException as ke:
            raise ke
        except SizingException as se:
            logger.error(
                "Vroid2Pmx処理が処理できないデータで終了しました。\n\n%s",
                se.message,
                decoration=MLogger.DECORATION_BOX
            )
            return None
        except Exception as e:
            logger.critical(
                "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s",
                traceback.format_exc(),
                decoration=MLogger.DECORATION_BOX
            )
            raise e 