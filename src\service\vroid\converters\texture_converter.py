from typing import Dict, Any, List, Optional
from pathlib import Path
from PIL import Image
import numpy as np

from .base_converter import BaseConverter
from ..utils.file_utils import FileUtils
from mmd.PmxData import Material

class TextureConverter(BaseConverter):
    def __init__(self, options):
        super().__init__(options)
        self.texture_cache: Dict[str, Image.Image] = {}
        self.output_dir = Path(options.output_path).parent / "textures"
        FileUtils.ensure_directory(self.output_dir)

    def convert(self, texture_data: Dict[str, Any]) -> Dict[str, str]:
        """
        转换纹理数据
        
        Args:
            texture_data: 包含纹理信息的字典
            
        Returns:
            转换后的纹理路径映射
        """
        try:
            self.log_progress("开始转换纹理")
            
            # 预处理
            processed_data = self.pre_process(texture_data)
            
            # 验证输入
            if not self.validate_input(processed_data):
                raise ValueError("无效的纹理数据")
            
            # 转换纹理
            texture_paths = {}
            total = len(processed_data)
            
            for idx, (tex_name, tex_info) in enumerate(processed_data.items(), 1):
                output_path = self._convert_single_texture(tex_name, tex_info)
                if output_path:
                    texture_paths[tex_name] = str(output_path)
                self.log_progress("纹理转换进度", (idx / total) * 100)
            
            # 后处理
            result = self.post_process(texture_paths)
            
            self.log_progress("纹理转换完成")
            return result
            
        except Exception as e:
            self.handle_error(e)

    def _convert_single_texture(self, tex_name: str, tex_info: Dict[str, Any]) -> Optional[Path]:
        """
        转换单个纹理
        
        Args:
            tex_name: 纹理名称
            tex_info: 纹理信息
            
        Returns:
            转换后的纹理文件路径
        """
        try:
            # 获取源纹理路径
            source_path = tex_info.get("source")
            if not source_path or not Path(source_path).exists():
                self.logger.warning(f"纹理文件不存在: {source_path}")
                return None
            
            # 生成输出路径
            output_path = self.output_dir / f"{tex_name}.png"
            
            # 如果已经存在且不需要覆盖，则直接返回
            if output_path.exists() and not self.options.overwrite:
                return output_path
            
            # 加载和处理图像
            with Image.open(source_path) as img:
                # 转换为RGBA模式
                if img.mode != "RGBA":
                    img = img.convert("RGBA")
                
                # 应用纹理变换
                if tex_info.get("flip_y", False):
                    img = img.transpose(Image.FLIP_TOP_BOTTOM)
                
                # 调整大小（如果需要）
                if "size" in tex_info:
                    width, height = tex_info["size"]
                    img = img.resize((width, height), Image.LANCZOS)
                
                # 保存处理后的图像
                img.save(output_path, "PNG")
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"转换纹理 {tex_name} 时发生错误: {str(e)}")
            return None

    def validate_input(self, data: Dict[str, Any]) -> bool:
        """
        验证纹理数据
        
        Args:
            data: 要验证的纹理数据
            
        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, dict):
            return False
        
        for tex_name, tex_info in data.items():
            if not isinstance(tex_info, dict):
                return False
            if "source" not in tex_info:
                return False
        
        return True

    def pre_process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理纹理数据
        
        Args:
            data: 要预处理的纹理数据
            
        Returns:
            预处理后的纹理数据
        """
        # 清理缓存
        self.texture_cache.clear()
        
        # 标准化路径
        processed_data = {}
        for tex_name, tex_info in data.items():
            processed_info = tex_info.copy()
            if "source" in processed_info:
                processed_info["source"] = str(Path(processed_info["source"]))
            processed_data[tex_name] = processed_info
        
        return processed_data

    def post_process(self, data: Dict[str, str]) -> Dict[str, str]:
        """
        后处理转换后的纹理路径
        
        Args:
            data: 转换后的纹理路径
            
        Returns:
            后处理后的纹理路径
        """
        # 转换为相对路径
        processed_data = {}
        for tex_name, tex_path in data.items():
            rel_path = FileUtils.get_relative_path(tex_path, Path(self.options.output_path).parent)
            processed_data[tex_name] = rel_path
        
        return processed_data

    def get_metadata(self) -> Dict[str, Any]:
        """
        获取转换器元数据
        
        Returns:
            包含转换器元数据的字典
        """
        metadata = super().get_metadata()
        metadata.update({
            "output_directory": str(self.output_dir),
            "supported_formats": ["png", "jpg", "jpeg", "bmp", "tga"]
        })
        return metadata 