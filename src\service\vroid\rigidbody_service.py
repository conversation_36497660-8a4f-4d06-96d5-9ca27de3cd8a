from typing import Optional, List
from mmd.PmxData import PmxModel, RigidBody, RigidBodyParam, Bone
from module.MMath import MVector3D
from utils.MLogger import MLogger
from .constants import MIKU_METER

logger = MLogger(__name__, level=1)

class RigidBodyService:
    """刚体服务，用于处理模型的物理刚体"""

    def create_body_rigidbody(self, model: PmxModel) -> Optional[PmxModel]:
        """创建模型的物理刚体

        为模型的主要部位（头部、身体、手臂、腿部）创建物理刚体。
        所有创建的刚体都是运动学类型（Kinematic），用于控制骨骼运动。

        Args:
            model (PmxModel): 要处理的PMX模型

        Returns:
            Optional[PmxModel]: 处理后的模型，如果处理失败则返回None
        """
        try:
            logger.info("创建刚体", decoration=MLogger.DECORATION_LINE)

            # 创建头部刚体
            head_bone = self._find_bone(model, "頭")
            if head_bone:
                self._create_head_rigidbody(model, head_bone)

            # 创建身体刚体
            body_bone = self._find_bone(model, "上半身")
            if body_bone:
                self._create_body_rigidbody(model, body_bone)

            # 创建手臂刚体
            arm_bones = self._find_bones(model, "腕")
            for arm_bone in arm_bones:
                self._create_arm_rigidbody(model, arm_bone)

            # 创建腿部刚体
            leg_bones = self._find_bones(model, "足")
            for leg_bone in leg_bones:
                self._create_leg_rigidbody(model, leg_bone)

            return model
        except Exception as e:
            logger.error("创建刚体失败: %s", str(e))
            return None

    def _find_bone(self, model: PmxModel, name: str) -> Optional[Bone]:
        """查找指定名称的骨骼

        Args:
            model (PmxModel): PMX模型
            name (str): 骨骼名称（部分匹配）

        Returns:
            Optional[Bone]: 找到的骨骼，如果未找到则返回None
        """
        for bone in model.bones.values():
            if name in bone.name:
                return bone
        return None

    def _find_bones(self, model: PmxModel, name: str) -> List[Bone]:
        """查找所有包含指定名称的骨骼

        Args:
            model (PmxModel): PMX模型
            name (str): 骨骼名称（部分匹配）

        Returns:
            List[Bone]: 找到的骨骼列表
        """
        return [bone for bone in model.bones.values() if name in bone.name]

    def _create_head_rigidbody(self, model: PmxModel, bone: Bone) -> None:
        """创建头部刚体

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 头部骨骼
        """
        rigidbody = RigidBody(
            name="頭接続",
            english_name="head_joint",
            bone_index=bone.index,
            collision_group=0,
            no_collision_group=0,
            shape_type=0,  # 球形
            shape_size=MVector3D(2 / MIKU_METER, 2 / MIKU_METER, 2 / MIKU_METER),
            shape_position=bone.position + MVector3D(0, 1 / MIKU_METER, 0),
            shape_rotation=MVector3D(),
            param=RigidBodyParam(mass=1, linear_damping=0.5, angular_damping=0.5, restitution=0.5, friction=0.5),
            physics_calc_type=0  # 运动学
        )
        model.rigidbodies[rigidbody.name] = rigidbody

    def _create_body_rigidbody(self, model: PmxModel, bone: Bone) -> None:
        """创建身体刚体

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 身体骨骼
        """
        rigidbody = RigidBody(
            name="胴接続",
            english_name="body_joint",
            bone_index=bone.index,
            collision_group=0,
            no_collision_group=0,
            shape_type=2,  # 胶囊体
            shape_size=MVector3D(3 / MIKU_METER, 4 / MIKU_METER, 3 / MIKU_METER),
            shape_position=bone.position,
            shape_rotation=MVector3D(),
            param=RigidBodyParam(mass=1, linear_damping=0.5, angular_damping=0.5, restitution=0.5, friction=0.5),
            physics_calc_type=0  # 运动学
        )
        model.rigidbodies[rigidbody.name] = rigidbody

    def _create_arm_rigidbody(self, model: PmxModel, bone: Bone) -> None:
        """创建手臂刚体

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 手臂骨骼
        """
        rigidbody = RigidBody(
            name=f"{bone.name}接続",
            english_name=f"{bone.english_name}_joint",
            bone_index=bone.index,
            collision_group=0,
            no_collision_group=0,
            shape_type=2,  # 胶囊体
            shape_size=MVector3D(2 / MIKU_METER, 3 / MIKU_METER, 2 / MIKU_METER),
            shape_position=bone.position,
            shape_rotation=MVector3D(),
            param=RigidBodyParam(mass=1, linear_damping=0.5, angular_damping=0.5, restitution=0.5, friction=0.5),
            physics_calc_type=0  # 运动学
        )
        model.rigidbodies[rigidbody.name] = rigidbody

    def _create_leg_rigidbody(self, model: PmxModel, bone: Bone) -> None:
        """创建腿部刚体

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 腿部骨骼
        """
        rigidbody = RigidBody(
            name=f"{bone.name}接続",
            english_name=f"{bone.english_name}_joint",
            bone_index=bone.index,
            collision_group=0,
            no_collision_group=0,
            shape_type=2,  # 胶囊体
            shape_size=MVector3D(2 / MIKU_METER, 4 / MIKU_METER, 2 / MIKU_METER),
            shape_position=bone.position,
            shape_rotation=MVector3D(),
            param=RigidBodyParam(mass=1, linear_damping=0.5, angular_damping=0.5, restitution=0.5, friction=0.5),
            physics_calc_type=0  # 运动学
        )
        model.rigidbodies[rigidbody.name] = rigidbody 