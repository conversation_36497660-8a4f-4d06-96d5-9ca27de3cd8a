# import bge
# import mathutils
# import math

# class MMDCameraCreator(bge.types.KX_PythonComponent):
#     """
#     MMD摄像机创建器组件
#     用于在UPBGE游戏中动态创建MMD父子摄像机结构
#     创建后可以与MMDCameraController配合使用
#     """
    
#     # 组件参数
#     args = {
#         "parent_name": "MMD_Camera",        # 父对象名称
#         "camera_name": "Camera",            # 摄像机名称
#         "initial_position": [0.0, 0.0, 10.0],  # 父对象初始位置
#         "initial_rotation": [0.0, 0.0, 0.0],   # 父对象初始旋转（度）
#         "camera_distance": -45.0,           # 摄像机距离
#         "camera_lens": 35.0,                # 摄像机镜头焦距
#         "auto_create": True,                # 是否自动创建
#         "set_as_active": True,              # 是否设置为活动摄像机
#         "create_controller": True,          # 是否同时创建控制器组件
#     }
    
#     def start(self, args):
#         """初始化组件"""
#         self.parent_name = args.get("parent_name", "MMD_Camera")
#         self.camera_name = args.get("camera_name", "Camera")
#         self.initial_position = args.get("initial_position", [0.0, 0.0, 10.0])
#         self.initial_rotation = args.get("initial_rotation", [0.0, 0.0, 0.0])
#         self.camera_distance = args.get("camera_distance", -45.0)
#         self.camera_lens = args.get("camera_lens", 35.0)
#         self.auto_create = args.get("auto_create", True)
#         self.set_as_active = args.get("set_as_active", True)
#         self.create_controller = args.get("create_controller", True)
        
#         # 创建状态
#         self.created = False
#         self.parent_obj = None
#         self.camera_obj = None
        
#         if self.auto_create:
#             self.create_mmd_camera()
    
#     def create_mmd_camera(self):
#         """创建MMD父子摄像机结构"""
#         if self.created:
#             print("MMD摄像机已经创建过了")
#             return False
        
#         scene = bge.logic.getCurrentScene()
        
#         # 检查是否已存在同名对象
#         if scene.objects.get(self.parent_name):
#             print(f"警告: 场景中已存在名为 '{self.parent_name}' 的对象")
#             self.parent_obj = scene.objects.get(self.parent_name)
#             # return False
#         else:
#             print(f"场景中不存在名为 '{self.parent_name}' 的对象")
        
#         if scene.objects.get(self.camera_name):
#             print(f"警告: 场景中已存在名为 '{self.camera_name}' 的对象")
#             self.camera_obj = scene.objects.get(self.camera_name)
#             # return False
#         else:
#             print(f"场景中不存在名为 '{self.camera_name}' 的对象")
        
#         try:
#             # 创建父对象（Empty）- 只有在没有找到现有对象时才创建
#             if not self.parent_obj:
#                 self.parent_obj = self._create_empty_object(scene)
#                 if not self.parent_obj:
#                     print("创建父对象失败")
#                     return False
            
#             if not self.camera_obj:
#                 # 创建摄像机对象
#                 self.camera_obj = self._create_camera_object(scene)
                
#             if not self.camera_obj:
#                 print("创建摄像机对象失败")
#                 self._cleanup()
#                 return False
            
#             # 设置父子关系和变换
#             self._setup_parent_child_relationship()
#             self._setup_transforms()
#             self._setup_camera_properties()
            
#             # 设置为活动摄像机
#             if self.set_as_active:
#                 scene.active_camera = self.camera_obj
            
#             # 可选：创建控制器组件
#             if self.create_controller:
#                 self._add_controller_component()
            
#             self.created = True
#             print(f"MMD摄像机创建成功: 父对象={self.parent_name}, 摄像机={self.camera_name}")
#             return True
            
#         except Exception as e:
#             print(f"创建MMD摄像机时发生错误: {e}")
#             self._cleanup()
#             return False
    
#     def _create_empty_object(self, scene):
#         """创建Empty父对象"""
#         try:
#             # 在UPBGE中，我们可以通过复制一个隐形的Empty模板来创建新对象
#             # 或者使用已有的对象作为模板
            
#             # 方法1: 尝试从模板创建（如果有的话）
#             template_empty = scene.objects.get("EmptyTemplate")
#             if template_empty:
#                 parent_obj = scene.addObject(template_empty.name, self.object)
#                 parent_obj.name = self.parent_name
#                 return parent_obj
            
#             # 方法2: 使用当前对象作为基础创建一个新的Empty
#             # 这需要在Blender中预先准备一个Empty对象
#             empty_template = None
#             for obj in scene.objects:
#                 if hasattr(obj, 'meshes') and len(obj.meshes) == 0:  # 找到一个Empty对象
#                     empty_template = obj
#                     break
            
#             if empty_template:
#                 parent_obj = scene.addObject(empty_template.name, self.object)
#                 parent_obj.name = self.parent_name
#                 return parent_obj
            
#             # 方法3: 使用当前对象但修改其属性
#             if hasattr(self.object, 'name'):
#                 parent_obj = scene.addObject(self.object.name, self.object)
#                 parent_obj.name = self.parent_name
#                 # 隐藏所有网格
#                 if hasattr(parent_obj, 'meshes'):
#                     for mesh in parent_obj.meshes:
#                         mesh.visible = False
#                 return parent_obj
            
#             print("错误: 无法找到合适的对象来创建Empty父对象")
#             return None
            
#         except Exception as e:
#             print(f"创建Empty对象失败: {e}")
#             import traceback
#             traceback.print_exc()
#             return None
    
#     def _create_camera_object(self, scene):
#         """创建摄像机对象"""
#         try:
#             # 尝试从模板创建摄像机
#             camera_template = scene.objects.get("CameraTemplate")
#             if camera_template:
#                 camera_obj = scene.addObject(camera_template.name, self.object)
#                 camera_obj.name = self.camera_name
#                 return camera_obj
            
#             # 寻找场景中已有的摄像机作为模板
#             template_camera = None
#             for obj in scene.objects:
#                 if hasattr(obj, 'camera') and obj.camera:
#                     template_camera = obj
#                     break
            
#             if template_camera:
#                 camera_obj = scene.addObject(template_camera.name, self.object)
#                 camera_obj.name = self.camera_name
#                 return camera_obj
            
#             # 如果没有找到摄像机模板，创建一个基础对象并尝试转换
#             print("警告: 未找到摄像机模板，请在场景中添加一个摄像机对象作为模板")
#             return None
            
#         except Exception as e:
#             print(f"创建摄像机对象失败: {e}")
#             import traceback
#             traceback.print_exc()
#             return None
    
#     def _setup_parent_child_relationship(self):
#         """设置父子关系"""
#         if self.parent_obj and self.camera_obj:
#             self.camera_obj.setParent(self.parent_obj)
#             print("已设置父子关系")
    
#     def _setup_transforms(self):
#         """设置变换属性"""
#         if not self.parent_obj or not self.camera_obj:
#             return
        
#         # 设置父对象位置和旋转
#         self.parent_obj.worldPosition = self.initial_position
        
#         # 将度转换为弧度
#         rotation_rad = [math.radians(angle) for angle in self.initial_rotation]
#         self.parent_obj.worldOrientation = mathutils.Euler(rotation_rad, 'XYZ')
        
#         # 设置摄像机本地位置（距离）
#         self.camera_obj.localPosition = [0.0, self.camera_distance, 0.0]
        
#         # 设置摄像机旋转（向前看）
#         self.camera_obj.localOrientation = mathutils.Euler([math.radians(90), 0, 0], 'XYZ')
        
#         print(f"变换设置完成: 位置={self.initial_position}, 距离={self.camera_distance}")
    
#     def _setup_camera_properties(self):
#         """设置摄像机属性"""
#         if not self.camera_obj or not hasattr(self.camera_obj, 'camera'):
#             return
        
#         camera = self.camera_obj.camera
        
#         # 设置镜头焦距
#         if hasattr(camera, 'lens'):
#             camera.lens = self.camera_lens
        
#         # 设置其他摄像机属性
#         if hasattr(camera, 'clipStart'):
#             camera.clipStart = 0.1
#         if hasattr(camera, 'clipEnd'):
#             camera.clipEnd = 1000.0
        
#         print(f"摄像机属性设置完成: 焦距={self.camera_lens}")
    
#     def _add_controller_component(self):
#         """添加控制器组件"""
#         try:
#             # 尝试导入并添加控制器组件
#             # 注意：这个方法在UPBGE中可能需要根据实际的组件系统进行调整
            
#             # 方法1: 如果控制器组件在同一个脚本中
#             from mmd_camera_controller0 import MMDCameraController
            
#             # 创建控制器实例（这部分可能需要根据UPBGE的组件系统调整）
#             controller_args = {
#                 "camera_name": self.camera_name,
#                 "parent_name": self.parent_name,
#                 "auto_start": False,  # 创建后不自动开始
#             }
            
#             # 将控制器附加到父对象或者当前对象
#             # 实际实现可能需要根据UPBGE的具体API调整
#             print("控制器组件添加功能需要根据UPBGE具体API实现")
            
#         except Exception as e:
#             print(f"添加控制器组件失败: {e}")
    
#     def _cleanup(self):
#         """清理创建失败的对象"""
#         scene = bge.logic.getCurrentScene()
        
#         if self.camera_obj:
#             self.camera_obj.endObject()
#             self.camera_obj = None
        
#         if self.parent_obj:
#             self.parent_obj.endObject()
#             self.parent_obj = None
    
#     def destroy_camera(self):
#         """销毁创建的摄像机"""
#         if not self.created:
#             print("没有创建的摄像机可以销毁")
#             return
        
#         scene = bge.logic.getCurrentScene()
        
#         # 如果是活动摄像机，先切换到其他摄像机
#         if scene.active_camera == self.camera_obj:
#             # 寻找其他摄像机
#             other_camera = None
#             for obj in scene.objects:
#                 if hasattr(obj, 'camera') and obj.camera and obj != self.camera_obj:
#                     other_camera = obj
#                     break
            
#             if other_camera:
#                 scene.active_camera = other_camera
#             else:
#                 print("警告: 没有其他摄像机可以切换")
        
#         self._cleanup()
#         self.created = False
#         print("MMD摄像机已销毁")
    
#     def get_camera_objects(self):
#         """获取创建的摄像机对象"""
#         return {
#             'parent': self.parent_obj,
#             'camera': self.camera_obj,
#             'created': self.created
#         }
    
#     def update(self):
#         """每帧更新（可用于检查状态）"""
#         # 检查创建的对象是否仍然有效
#         if self.created:
#             if not self.parent_obj or not self.parent_obj.invalid:
#                 if self.parent_obj and self.parent_obj.invalid:
#                     print("父对象已失效")
#                     self.created = False
            
#             if not self.camera_obj or not self.camera_obj.invalid:
#                 if self.camera_obj and self.camera_obj.invalid:
#                     print("摄像机对象已失效")
#                     self.created = False


# # 辅助函数
# def create_mmd_camera_runtime(scene, parent_name="MMD_Camera", camera_name="Camera", 
#                              position=(0, 0, 10), rotation=(0, 0, 0), distance=-45.0):
#     """
#     运行时创建MMD摄像机的辅助函数
#     可以在其他脚本中直接调用
#     """
#     creator_args = {
#         "parent_name": parent_name,
#         "camera_name": camera_name,
#         "initial_position": list(position),
#         "initial_rotation": list(rotation),
#         "camera_distance": distance,
#         "auto_create": False,
#         "set_as_active": True,
#     }
    
#     # 寻找场景中任意一个对象作为参考
#     reference_obj = None
#     for obj in scene.objects:
#         reference_obj = obj
#         break
    
#     if not reference_obj:
#         print("错误: 场景中没有任何对象可以作为参考")
#         return None
    
#     # 创建一个临时对象来承载组件（或者直接使用已有对象）
#     try:
#         temp_obj = scene.addObject(reference_obj, reference_obj)
#         temp_obj.name = "MMDCameraCreator_Temp"
#     except:
#         # 如果创建临时对象失败，直接使用参考对象
#         temp_obj = reference_obj
    
#     # 创建组件实例
#     creator = MMDCameraCreator()
#     creator.object = temp_obj
#     creator.start(creator_args)
    
#     # 创建摄像机
#     success = creator.create_mmd_camera()
    
#     # 获取创建的对象
#     camera_objects = creator.get_camera_objects()
    
#     # 清理临时对象（如果创建了的话）
#     if temp_obj != reference_obj:
#         temp_obj.endObject()
    
#     return camera_objects if success else None


# # 键盘控制示例
# class CameraCreatorKeyboardControl(bge.types.KX_PythonComponent):
#     """
#     键盘控制摄像机创建的示例组件
#     """
    
#     args = {}
    
#     def start(self, args):
#         self.keyboard = bge.logic.keyboard
#         self.creator = None
        
#     def update(self):
#         # C键创建摄像机
#         if self.keyboard.events[bge.events.CKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if not self.creator:
#                 # 创建creator组件
#                 self.creator = MMDCameraCreator()
#                 self.creator.object = self.object
#                 self.creator.start({
#                     "parent_name": "MMD_Camera",
#                     "camera_name": "Camera",
#                     "auto_create": False,
#                 })
            
#             self.creator.create_mmd_camera()
        
#         # D键销毁摄像机
#         if self.keyboard.events[bge.events.DKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if self.creator:
#                 self.creator.destroy_camera() 