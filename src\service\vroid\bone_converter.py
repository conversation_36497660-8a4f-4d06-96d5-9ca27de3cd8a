"""
Bone conversion functionality for VRoid to PMX
"""

from typing import Di<PERSON>, <PERSON><PERSON>, Optional
import numpy as np
import struct
from module.MMath import MVector3D, MMatrix4x4, MQuaternion
from mmd.PmxData import PmxModel, <PERSON>
from utils.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .constants import BONE_PAIRS

logger = MLogger(__name__)

class BoneConverter:
    def __init__(self, options):
        self.options = options
        
    def _get_accessor_data(self, model: PmxModel, accessor_idx: int) -> np.ndarray:
        """Get data from an accessor with proper type conversion"""
        if accessor_idx >= len(model.accessors):
            return None
            
        accessor = model.accessors[accessor_idx]
        buffer_view = model.buffer_views[accessor["bufferView"]]
        data = buffer_view["data"]
        
        # Get format string based on component type
        fmt = {
            5120: 'b',  # BYTE
            5121: 'B',  # UNSIGNED_BYTE
            5122: 'h',  # SHORT
            5123: 'H',  # UNSIGNED_SHORT
            5125: 'I',  # UNSIGNED_INT
            5126: 'f',  # FLOAT
        }[accessor["componentType"]]
        
        # Get number of components based on type
        count = {
            "SCALAR": 1,
            "VEC2": 2,
            "VEC3": 3,
            "VEC4": 4,
            "MAT2": 4,
            "MAT3": 9,
            "MAT4": 16,
        }[accessor["type"]]
        
        # Calculate total size
        size = struct.calcsize(fmt)
        total_size = size * count * accessor["count"]
        
        # Unpack data
        try:
            values = []
            offset = 0
            for _ in range(accessor["count"]):
                components = []
                for _ in range(count):
                    value = struct.unpack_from(fmt, data, offset)[0]
                    components.append(value)
                    offset += size
                values.append(components)
            return np.array(values)
        except Exception as e:
            logger.error(f"Error unpacking accessor data: {str(e)}")
            return None

    def convert_bone(self, model: PmxModel) -> Tuple[PmxModel, Dict[str, str]]:
        """Convert bones from VRoid to PMX format"""
        logger.info("-- VRoid骨構造をPMX用に変換")
        
        bone_name_dict = {}
        
        # Process nodes from glTF data
        nodes = model.json_data.get("nodes", [])
        node_dict = {}
        
        for node_idx, node in enumerate(nodes):
            if "name" not in node:
                continue
                
            name = node["name"]
            node_data = {
                "translation": [0, 0, 0],
                "rotation": [0, 0, 0, 1],
                "scale": [1, 1, 1]
            }
            
            # Get translation
            if "translation" in node:
                node_data["translation"] = node["translation"]
            elif "translationAccessor" in node:
                trans_data = self._get_accessor_data(model, node["translationAccessor"])
                if trans_data is not None and len(trans_data) > 0:
                    node_data["translation"] = trans_data[0]
            
            # Get rotation
            if "rotation" in node:
                node_data["rotation"] = node["rotation"]
            elif "rotationAccessor" in node:
                rot_data = self._get_accessor_data(model, node["rotationAccessor"])
                if rot_data is not None and len(rot_data) > 0:
                    node_data["rotation"] = rot_data[0]
            
            # Get scale
            if "scale" in node:
                node_data["scale"] = node["scale"]
            elif "scaleAccessor" in node:
                scale_data = self._get_accessor_data(model, node["scaleAccessor"])
                if scale_data is not None and len(scale_data) > 0:
                    node_data["scale"] = scale_data[0]
            
            node_dict[name] = node_data
        
        # Create bones based on BONE_PAIRS
        for bone_name, bone_data in BONE_PAIRS.items():
            # Get node data if available
            node_data = node_dict.get(bone_name, {
                "translation": [0, 0, 0],
                "rotation": [0, 0, 0, 1],
                "scale": [1, 1, 1]
            })
            
            # Create position vector
            position = MVector3D(
                node_data["translation"][0],
                node_data["translation"][1],
                node_data["translation"][2]
            )
            
            # Create bone
            bone = Bone()
            bone.name = bone_data["name"]
            bone.english_name = bone_name
            bone.position = position
            bone.parent_index = -1  # Parent index will be set later
            bone.layer = 0
            bone.flag = bone_data["flag"]
            
            # Set rotation and scale
            rotation = MQuaternion(
                node_data["rotation"][0],
                node_data["rotation"][1],
                node_data["rotation"][2],
                node_data["rotation"][3]
            )
            scale = MVector3D(
                node_data["scale"][0],
                node_data["scale"][1],
                node_data["scale"][2]
            )
            
            # Create transformation matrix
            bone.matrix = MMatrix4x4()
            bone.matrix.setToIdentity()
            bone.matrix.scale(scale)
            bone.matrix.rotate(rotation)
            bone.matrix.translate(position)
            
            # Set parent bone
            if bone_data["parent"]:
                parent_bone = model.bones.get(bone_data["parent"])
                if parent_bone:
                    bone.parent_index = parent_bone.index
                    # Apply parent transformation
                    bone.position = parent_bone.matrix * bone.position
            
            # Set tail bone
            if bone_data["tail"]:
                tail_bone = model.bones.get(bone_data["tail"])
                if tail_bone:
                    bone.tail_position = tail_bone.position - bone.position
            
            # Add to model
            model.bones[bone.name] = bone
            bone_name_dict[bone_name] = bone.name
            
        return model, bone_name_dict
        
    def calc_bone_position(self, model: PmxModel, node_dict: dict, node_param: dict) -> PmxModel:
        """Calculate bone positions"""
        logger.info("-- ボーンの位置を計算")
        
        for bone in model.bones.values():
            if bone.english_name in node_dict:
                node = node_dict[bone.english_name]
                matrix = MMatrix4x4()
                matrix.setToIdentity()
                
                # Apply transformations in TRS order (Scale -> Rotate -> Translate)
                if "scale" in node:
                    scale = node["scale"]
                    matrix.scale(MVector3D(scale[0], scale[1], scale[2]))
                
                if "rotation" in node:
                    rot = node["rotation"]
                    quat = MQuaternion(rot[0], rot[1], rot[2], rot[3])
                    matrix.rotate(quat)
                
                if "translation" in node:
                    trans = node["translation"]
                    matrix.translate(MVector3D(trans[0], trans[1], trans[2]))
                
                bone.matrix = matrix
                
                # Update position
                if bone.parent_index >= 0:
                    parent = model.bones[bone.parent_index]
                    bone.position = parent.matrix * bone.position
                else:
                    bone.position = matrix * MVector3D()
        
        return model
        
    def reconvert_bone(self, model: PmxModel) -> PmxModel:
        """Reconvert bones after initial conversion"""
        logger.info("-- ボーンの再変換")
        
        # Recalculate bone positions and orientations
        for bone in model.bones.values():
            if bone.parent_index >= 0:
                parent = model.bones[bone.parent_index]
                bone.position = bone.position + parent.position
                
            if bone.tail_position:
                bone.tail_position = bone.tail_position + bone.position
        
        return model
        
    def calc_normal(self, bone_mat: MMatrix4x4, normal: MVector3D) -> MVector3D:
        """Calculate normal vector for bone"""
        # Extract rotation matrix (3x3)
        rot_mat = np.array([
            [bone_mat.m00, bone_mat.m01, bone_mat.m02],
            [bone_mat.m10, bone_mat.m11, bone_mat.m12],
            [bone_mat.m20, bone_mat.m21, bone_mat.m22]
        ])
        
        # Convert normal to numpy array
        normal_vec = np.array([normal.x, normal.y, normal.z])
        
        # Apply rotation
        rotated = np.dot(rot_mat, normal_vec)
        
        return MVector3D(rotated[0], rotated[1], rotated[2]) 