from typing import Optional, <PERSON><PERSON>
import os
import json
from pathlib import Path
import shutil
import struct
from mmd.PmxData import PmxModel
from module.MOptions import MExportOptions
from utils.MLogger import <PERSON><PERSON><PERSON>ger
from utils.MException import SizingException, MParseException

logger = MLogger(__name__, level=1)

class ModelService:
    """Model service for creating and processing PMX models"""

    def __init__(self, options: MExportOptions):
        """Initialize model service

        Args:
            options (MExportOptions): Export options
        """
        self.options = options
        self.logger = MLogger(__name__)

    def create_model(self) -> Tuple[Optional[PmxModel], Optional[str], Optional[str], bool]:
        """Create PMX model

        Returns:
            Tuple[Optional[PmxModel], Optional[str], Optional[str], bool]: Returns a tuple containing:
                - PmxModel: Created PMX model object, or None if creation fails
                - str: Texture directory path, or None if creation fails
                - str: Settings directory path, or None if creation fails
                - bool: Whether it's a VRoid1.x version
        """
        try:
            # Validate input file
            if not os.path.exists(self.options.vrm_model.path):
                raise MParseException(f"VRM file not found: {self.options.vrm_model.path}")

            # Create output directory
            output_dir = os.path.dirname(self.options.output_path)
            if not output_dir:
                raise MParseException("Invalid output path - no directory specified")

            try:
                os.makedirs(output_dir, exist_ok=True)
            except OSError as e:
                raise MParseException(f"Failed to create output directory: {str(e)}")

            # Create texture directory
            tex_dir_path = os.path.join(output_dir, "tex")
            try:
                os.makedirs(tex_dir_path, exist_ok=True)
            except OSError as e:
                raise MParseException(f"Failed to create texture directory: {str(e)}")

            # Create settings directory
            setting_dir_path = os.path.join(output_dir, "settings")
            try:
                os.makedirs(setting_dir_path, exist_ok=True)
            except OSError as e:
                raise MParseException(f"Failed to create settings directory: {str(e)}")

            # Create PMX model
            model = PmxModel()
            model.name = "VRoid Model"
            model.english_name = "VRoid Model"
            model.comment = "Created by VRoid2Pmx"
            model.english_comment = "Created by VRoid2Pmx"

            # Read VRM file JSON data
            with open(self.options.vrm_model.path, "rb") as f:
                # Read GLB header
                magic = f.read(4)
                if magic != b'glTF':
                    raise MParseException("Invalid GLB/VRM file - incorrect magic number")
                
                version = struct.unpack('<I', f.read(4))[0]
                length = struct.unpack('<I', f.read(4))[0]
                
                # Read JSON chunk
                chunk_length = struct.unpack('<I', f.read(4))[0]
                chunk_type = f.read(4)
                if chunk_type != b'JSON':
                    raise MParseException("Invalid GLB/VRM file - JSON chunk not found")
                
                json_data = f.read(chunk_length)
                try:
                    model.json_data = json.loads(json_data.decode('utf-8'))
                except UnicodeDecodeError:
                    # Try other encodings if UTF-8 fails
                    self.logger.debug("UTF-8 decode failed, trying other encodings")
                    encodings = ['shift-jis', 'cp932', 'utf-8-sig']
                    for encoding in encodings:
                        try:
                            self.logger.debug(f"Trying encoding: {encoding}")
                            model.json_data = json.loads(json_data.decode(encoding))
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        # If all encodings fail, try with ignore option
                        self.logger.warning("All encodings failed, using UTF-8 with ignore option")
                        model.json_data = json.loads(json_data.decode('utf-8', errors='ignore'))

                # Validate VRM data
                if not isinstance(model.json_data, dict):
                    raise MParseException("Invalid VRM format - JSON root must be an object")

                if 'extensions' not in model.json_data or 'VRM' not in model.json_data['extensions']:
                    raise MParseException("Invalid VRM format - missing VRM extension data")

                # Read BIN chunk
                bin_chunk_length = struct.unpack('<I', f.read(4))[0]
                bin_chunk_type = f.read(4)
                if bin_chunk_type != b'BIN\x00':
                    raise MParseException("Invalid GLB/VRM file - BIN chunk not found")
                
                bin_data = f.read(bin_chunk_length)
                if len(bin_data) != bin_chunk_length:
                    raise MParseException(f"Incomplete BIN chunk - expected {bin_chunk_length} bytes, got {len(bin_data)}")

                model.buffers = [bin_data]

                # Process buffer views
                model.buffer_views = []
                for idx, buffer_view in enumerate(model.json_data.get("bufferViews", [])):
                    if not isinstance(buffer_view, dict):
                        raise MParseException(f"Invalid bufferView at index {idx}")

                    offset = buffer_view.get("byteOffset", 0)
                    length = buffer_view.get("byteLength", 0)

                    if offset + length > len(bin_data):
                        raise MParseException(f"Buffer view {idx} extends beyond buffer data")

                    model.buffer_views.append(bin_data[offset:offset + length])

                # Process accessors
                model.accessors = []
                for idx, accessor in enumerate(model.json_data.get("accessors", [])):
                    if not isinstance(accessor, dict):
                        raise MParseException(f"Invalid accessor at index {idx}")

                    if "bufferView" not in accessor:
                        raise MParseException(f"Accessor {idx} missing bufferView")

                    buffer_view_idx = accessor["bufferView"]
                    if buffer_view_idx >= len(model.buffer_views):
                        raise MParseException(f"Accessor {idx} references non-existent bufferView")

                    buffer_view = model.buffer_views[buffer_view_idx]
                    component_type = accessor.get("componentType")
                    count = accessor.get("count")
                    type_str = accessor.get("type")
                    byte_offset = accessor.get("byteOffset", 0)

                    if not all([component_type, count, type_str]):
                        raise MParseException(f"Accessor {idx} missing required fields")

                    # Calculate component size
                    component_size = {
                        5120: 1,  # BYTE
                        5121: 1,  # UNSIGNED_BYTE
                        5122: 2,  # SHORT
                        5123: 2,  # UNSIGNED_SHORT
                        5125: 4,  # UNSIGNED_INT
                        5126: 4,  # FLOAT
                    }.get(component_type)

                    if not component_size:
                        raise MParseException(f"Accessor {idx} has invalid componentType: {component_type}")

                    # Calculate element size
                    type_size = {
                        "SCALAR": 1,
                        "VEC2": 2,
                        "VEC3": 3,
                        "VEC4": 4,
                        "MAT2": 4,
                        "MAT3": 9,
                        "MAT4": 16,
                    }.get(type_str)

                    if not type_size:
                        raise MParseException(f"Accessor {idx} has invalid type: {type_str}")

                    stride = component_size * type_size
                    if byte_offset + count * stride > len(buffer_view):
                        raise MParseException(f"Accessor {idx} extends beyond buffer view data")

                    data = buffer_view[byte_offset:byte_offset + count * stride]
                    model.accessors.append(data)

                # Process mesh data
                model.meshes = []
                for idx, mesh in enumerate(model.json_data.get("meshes", [])):
                    if not isinstance(mesh, dict):
                        raise MParseException(f"Invalid mesh at index {idx}")

                    mesh_data = {
                        "name": mesh.get("name", f"mesh_{idx}"),
                        "primitives": []
                    }

                    for prim_idx, primitive in enumerate(mesh.get("primitives", [])):
                        if not isinstance(primitive, dict):
                            raise MParseException(f"Invalid primitive at index {prim_idx} in mesh {idx}")

                        primitive_data = {
                            "attributes": {},
                            "indices": None,
                            "material": primitive.get("material", -1)
                        }
                        
                        # Process vertex attributes
                        for attr_name, accessor_idx in primitive.get("attributes", {}).items():
                            if accessor_idx >= len(model.json_data["accessors"]):
                                raise MParseException(f"Primitive {prim_idx} in mesh {idx} references non-existent accessor")

                            accessor = model.json_data["accessors"][accessor_idx]
                            data = model.accessors[accessor_idx]
                            primitive_data["attributes"][attr_name] = {
                                "data": data,
                                "count": accessor["count"],
                                "type": accessor["type"],
                                "componentType": accessor["componentType"]
                            }
                        
                        # Process indices
                        if "indices" in primitive:
                            indices_idx = primitive["indices"]
                            if indices_idx >= len(model.json_data["accessors"]):
                                raise MParseException(f"Primitive {prim_idx} in mesh {idx} references non-existent indices accessor")

                            accessor = model.json_data["accessors"][indices_idx]
                            data = model.accessors[indices_idx]
                            primitive_data["indices"] = {
                                "data": data,
                                "count": accessor["count"],
                                "type": accessor["type"],
                                "componentType": accessor["componentType"]
                            }
                        
                        mesh_data["primitives"].append(primitive_data)
                    
                    model.meshes.append(mesh_data)

            # Check if it's VRoid1.x version
            is_vroid1 = False
            if (
                "extensions" in model.json_data
                and "VRM" in model.json_data["extensions"]
                and "exporterVersion" in model.json_data["extensions"]["VRM"]
                and "1." in model.json_data["extensions"]["VRM"]["exporterVersion"]
            ):
                is_vroid1 = True
                self.logger.info("Detected VRoid1.x model")

            return model, tex_dir_path, setting_dir_path, is_vroid1

        except MParseException as pe:
            self.logger.error("Failed to create model: %s", str(pe))
            raise pe
        except Exception as e:
            self.logger.error("Failed to create model: %s", str(e))
            raise MParseException(f"Unexpected error while creating model: {str(e)}") 