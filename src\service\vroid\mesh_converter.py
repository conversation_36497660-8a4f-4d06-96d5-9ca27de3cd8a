"""
Mesh conversion functionality for VRoid to PMX
"""

from typing import Dict, List, <PERSON><PERSON>
import os
import numpy as np
from module.MMath import MVector3D, MVector4D, MVector2D
from mmd.PmxData import PmxModel, Vertex, Material, Bdef1, Bdef2, Bdef4, Deform
from utils.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .utils import calc_ratio
from .constants import MIKU_METER
import struct

logger = MLogger(__name__)

class MeshConverter:
    def __init__(self, options):
        self.options = options
        self.model = None
        
    def convert_mesh(self, model: PmxModel, bone_name_dict: dict, tex_dir_path: str) -> PmxModel:
        """Convert mesh from VRoid to PMX format"""
        logger.info("-- メッシュを変換")
        self.model = model
        
        vertex_offset = 0
        # Convert vertices and faces from each mesh
        for mesh in model.meshes:
            for primitive in mesh["primitives"]:
                # Get vertex attributes
                position_data = primitive["attributes"].get("POSITION", {})
                normal_data = primitive["attributes"].get("NORMAL", {})
                uv_data = primitive["attributes"].get("TEXCOORD_0", {})
                joints_data = primitive["attributes"].get("JOINTS_0", {})
                weights_data = primitive["attributes"].get("WEIGHTS_0", {})
                indices_data = primitive.get("indices", {})

                if not position_data:
                    logger.warning("Skipping primitive without position data")
                    continue

                # Convert vertex data
                vertex_count = position_data["count"]
                for i in range(vertex_count):
                    # Get position (3 floats)
                    pos_offset = i * 12  # 3 * 4 bytes (float)
                    pos = struct.unpack_from("<fff", position_data["data"], pos_offset)
                    # Convert position using MIKU_METER and coordinate system conversion
                    # 分步进行运算，确保每一步都返回正确的MVector3D对象
                    temp_pos = MVector3D(pos[0], pos[1], pos[2])
                    temp_pos = temp_pos * MIKU_METER
                    position = MVector3D(temp_pos.x() * -1, temp_pos.y() * 1, temp_pos.z() * 1)

                    # Get normal (3 floats)
                    normal = MVector3D(0, 1, 0)  # Default normal
                    if normal_data:
                        norm_offset = i * 12  # 3 * 4 bytes (float)
                        norm = struct.unpack_from("<fff", normal_data["data"], norm_offset)
                        # 确保normal向量有效，并进行坐标系转换
                        normal = MVector3D(norm[0] * -1, norm[1] * 1, norm[2] * 1)
                        normal.effective()  # 清理NaN和无穷大值

                    # Get UV (2 floats)
                    uv = MVector2D(0, 0)  # Default UV
                    if uv_data:
                        uv_offset = i * 8  # 2 * 4 bytes (float)
                        uv_coords = struct.unpack_from("<ff", uv_data["data"], uv_offset)
                        uv = MVector2D(uv_coords[0], uv_coords[1])
                        uv.effective()  # 清理NaN和无穷大值

                    # Create vertex
                    vertex = Vertex()
                    vertex.position = position
                    vertex.normal = normal
                    vertex.uv = uv
                    vertex.extended_uvs = []
                    vertex.edge_factor = 0.0
                    vertex.index = i + vertex_offset  # 设置顶点索引
                    vertex.deform = Bdef1(0)  # 设置默认的deform属性为Bdef1

                    # Set bone weights
                    if joints_data and weights_data:
                        # Get joint indices (4 bytes or shorts)
                        joint_size = 2 if joints_data["componentType"] == 5123 else 1
                        joint_offset = i * 4 * joint_size
                        if joints_data["componentType"] == 5123:  # UNSIGNED_SHORT
                            joints = struct.unpack_from("<HHHH", joints_data["data"], joint_offset)
                        else:  # UNSIGNED_BYTE
                            joints = struct.unpack_from("<BBBB", joints_data["data"], joint_offset)

                        # Get weights (4 floats)
                        weight_offset = i * 16  # 4 * 4 bytes (float)
                        weights = struct.unpack_from("<ffff", weights_data["data"], weight_offset)

                        # Create bone deform
                        valid_indices = []
                        valid_weights = []
                        for j in range(4):
                            if weights[j] > 0:
                                bone_name = bone_name_dict.get(str(joints[j]), None)
                                if bone_name and bone_name in self.model.bones:
                                    bone = self.model.bones[bone_name]
                                    valid_indices.append(bone.index)
                                    valid_weights.append(weights[j])

                        # Normalize weights
                        if valid_weights:
                            total = sum(valid_weights)
                            valid_weights = [w/total for w in valid_weights]

                            if len(valid_indices) == 1:
                                vertex.deform = Bdef1(valid_indices[0])
                            elif len(valid_indices) == 2:
                                vertex.deform = Bdef2(valid_indices[0], valid_indices[1], valid_weights[0])
                            else:
                                # Pad to 4 weights if needed
                                while len(valid_indices) < 4:
                                    valid_indices.append(0)
                                    valid_weights.append(0)
                                vertex.deform = Bdef4(
                                    valid_indices[0],
                                    valid_indices[1],
                                    valid_indices[2],
                                    valid_indices[3],
                                    valid_weights[0],
                                    valid_weights[1],
                                    valid_weights[2],
                                    valid_weights[3]
                                )

                    # 将顶点添加到vertex_dict
                    model.vertex_dict[i + vertex_offset] = vertex
                    model.vertices[i + vertex_offset] = vertex

                # Convert face indices
                if indices_data:
                    # Get index data size and format based on component type
                    if indices_data["componentType"] == 5123:  # UNSIGNED_SHORT
                        index_size = 2
                        index_format = "<H"
                    elif indices_data["componentType"] == 5125:  # UNSIGNED_INT
                        index_size = 4
                        index_format = "<I"
                    else:
                        logger.warning(f"Unsupported index component type: {indices_data['componentType']}")
                        continue
                    
                    # Read indices in groups of 3 (triangles)
                    for i in range(0, indices_data["count"], 3):
                        offset = i * index_size
                        if offset + (index_size * 3) <= len(indices_data["data"]):
                            # Read one index at a time
                            idx1 = struct.unpack_from(index_format, indices_data["data"], offset)[0]
                            idx2 = struct.unpack_from(index_format, indices_data["data"], offset + index_size)[0]
                            idx3 = struct.unpack_from(index_format, indices_data["data"], offset + (index_size * 2))[0]
                            
                            face_idx = len(model.indices)
                            model.indices[face_idx] = [
                                idx1 + vertex_offset,
                                idx2 + vertex_offset,
                                idx3 + vertex_offset
                            ]

                vertex_offset += vertex_count

        # Convert materials
        for material_data in model.json_data.get("materials", []):
            # Get material properties
            name = material_data.get("name", "")
            english_name = material_data.get("name", "")
            
            # Get base color factor
            base_color_factor = material_data.get("pbrMetallicRoughness", {}).get("baseColorFactor", [1, 1, 1, 1])
            diffuse = MVector4D(*base_color_factor)
            alpha = base_color_factor[3]
            
            # Get metallic and roughness factors
            metallic_factor = material_data.get("pbrMetallicRoughness", {}).get("metallicFactor", 0)
            roughness_factor = material_data.get("pbrMetallicRoughness", {}).get("roughnessFactor", 1)
            specular = MVector3D(metallic_factor, metallic_factor, metallic_factor)
            specular_factor = 5.0 * (1 - roughness_factor)  # Convert roughness to specular power
            
            # Get emissive factor
            emissive_factor = material_data.get("emissiveFactor", [0, 0, 0])
            ambient = MVector3D(*emissive_factor)
            
            flag = 1  # Default flag
            edge_color = MVector4D(0, 0, 0, 1)  # Default edge color
            edge_size = 1.0
            texture_index = -1
            sphere_texture_index = -1
            sphere_mode = 0
            toon_sharing_flag = 0
            toon_texture_index = 0
            comment = ""

            # Create material
            material = Material()
            material.name = name
            material.english_name = english_name
            material.diffuse = diffuse
            material.specular = specular
            material.specular_factor = specular_factor
            material.ambient = ambient
            material.flag = flag
            material.edge_color = edge_color
            material.edge_size = edge_size
            material.texture_index = texture_index
            material.sphere_texture_index = sphere_texture_index
            material.sphere_mode = sphere_mode
            material.toon_sharing_flag = toon_sharing_flag
            material.toon_texture_index = toon_texture_index
            material.comment = comment

            # 计算材质的顶点数量
            material_vertex_count = 0
            for primitive in mesh["primitives"]:
                if primitive.get("material") == material_data.get("index"):
                    indices_data = primitive.get("indices", {})
                    if indices_data:
                        material_vertex_count += indices_data.get("count", 0)
            material.vertex_count = material_vertex_count

            # 添加材质到模型
            model.materials[material.name] = material
            model.material_indices[material.name] = len(model.materials) - 1

            # 记录材质的顶点索引
            material_vertices = []
            for primitive in mesh["primitives"]:
                if primitive.get("material") == material_data.get("index"):
                    indices_data = primitive.get("indices", {})
                    if indices_data:
                        for i in range(0, indices_data["count"], 3):
                            offset = i * index_size
                            if offset + (index_size * 3) <= len(indices_data["data"]):
                                idx1 = struct.unpack_from(index_format, indices_data["data"], offset)[0]
                                idx2 = struct.unpack_from(index_format, indices_data["data"], offset + index_size)[0]
                                idx3 = struct.unpack_from(index_format, indices_data["data"], offset + (index_size * 2))[0]
                                material_vertices.extend([idx1 + vertex_offset, idx2 + vertex_offset, idx3 + vertex_offset])
            model.material_vertices[material.name] = material_vertices

            # Handle textures
            if "baseColorTexture" in material_data.get("pbrMetallicRoughness", {}):
                texture_info = material_data["pbrMetallicRoughness"]["baseColorTexture"]
                texture_index = texture_info["index"]
                logger.debug(f"Processing texture for material {material.name}, texture_index: {texture_index}")

                if texture_index < len(model.json_data.get("textures", [])):
                    texture = model.json_data["textures"][texture_index]
                    logger.debug(f"Found texture data: {texture}")

                    if "source" in texture:
                        image = model.json_data["images"][texture["source"]]
                        logger.debug(f"Found image data: {image}")

                        if "uri" in image:
                            texture_path = os.path.join(tex_dir_path, image["uri"])
                            logger.debug(f"Checking texture path: {texture_path}")

                            if os.path.exists(texture_path):
                                material.texture_index = len(model.textures)
                                model.textures.append(texture_path)
                                logger.info(f"Added texture: {texture_path} for material {material.name}")
                            else:
                                logger.warning(f"Texture file not found: {texture_path}")
                        else:
                            logger.warning(f"No 'uri' in image data for material {material.name}")
                    else:
                        logger.warning(f"No 'source' in texture data for material {material.name}")
                else:
                    logger.warning(f"Texture index {texture_index} out of range for material {material.name}")
            else:
                logger.debug(f"No baseColorTexture found for material {material.name}")
            
        return model
        
    def get_deform_index(
        self,
        vertex_idx: int,
        vertex_pos: MVector3D,
        joint: MVector4D,
        skin_joints: list,
        node_weight: MVector4D,
        bone_name_dict: Dict[str, dict]
    ) -> Tuple[List[int], List[float]]:
        """Get deformation index for vertex - improved implementation based on original"""
        import numpy as np

        # Get non-zero joint indices
        valid_joints = np.where(joint.data() > 0)[0].tolist()
        if not valid_joints:
            return [0], [1.0]  # Default to root bone

        # Get weights for valid joints
        org_weights = node_weight.data()[np.where(joint.data() > 0)]
        # Get joint indices (convert float to int)
        org_joint_idxs = joint.data()[valid_joints].astype(np.int32)

        # Convert to destination bone indices
        dest_joint_list = []
        dest_weights = []

        for i, jidx in enumerate(org_joint_idxs.tolist()):
            if jidx < len(skin_joints):
                skin_joint_idx = skin_joints[jidx]
                # Find corresponding bone
                for node_name, bone_param in bone_name_dict.items():
                    if bone_param.get("node_index") == skin_joint_idx:
                        bone_name = bone_param.get("name")
                        if bone_name and bone_name in self.model.bones:
                            bone_idx = self.model.bones[bone_name].index
                            dest_joint_list.append(bone_idx)
                            dest_weights.append(org_weights[i])
                            break

        if not dest_joint_list:
            return [0], [1.0]  # Default to root bone

        # Normalize weights
        if dest_weights:
            total = sum(dest_weights)
            if total > 0:
                dest_weights = [w/total for w in dest_weights]
            else:
                dest_weights = [1.0/len(dest_weights)] * len(dest_weights)

        # Limit to 4 bones maximum
        if len(dest_joint_list) > 4:
            # Sort by weight and keep top 4
            combined = list(zip(dest_joint_list, dest_weights))
            combined.sort(key=lambda x: x[1], reverse=True)
            dest_joint_list = [x[0] for x in combined[:4]]
            dest_weights = [x[1] for x in combined[:4]]
            # Renormalize
            total = sum(dest_weights)
            if total > 0:
                dest_weights = [w/total for w in dest_weights]

        return dest_joint_list, dest_weights