"""刚体参数服务模块

此模块提供了处理刚体参数的功能，包括：
- 刚体参数的创建和配置
- 刚体参数的验证
- 刚体参数的优化
"""

from typing import Dict, List, Tuple, Any, Optional
import math
import numpy as np
from mmd.PmxData import PmxModel, RigidBody, Bone, RigidBodyParam
from module.MMath import MVector3D, MQuaternion, MMatrix4x4
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class RigidBodyParamService:
    """刚体参数服务，用于处理模型的物理刚体参数"""

    @staticmethod
    def process_rigidbody_params(
        model: PmxModel,
        bone_vertices: Dict[str, List[int]],
        bone_weights: Dict[str, Dict[int, float]],
        rigidbody_pairs: Dict[str, Dict[str, Any]]
    ) -> PmxModel:
        """处理刚体参数

        Args:
            model (PmxModel): PMX模型
            bone_vertices (Dict[str, List[int]]): 骨骼顶点映射
            bone_weights (Dict[str, Dict[int, float]]): 骨骼权重映射
            rigidbody_pairs (Dict[str, Dict[str, Any]]): 刚体参数对

        Returns:
            PmxModel: 处理后的模型
        """
        logger.info("-- 身体剛体準備終了")

        for rigidbody_name, rigidbody_param in rigidbody_pairs.items():
            # 计算无碰撞组
            no_collision_group = RigidBodyParamService._calculate_no_collision_group(rigidbody_param)

            # 获取骨骼
            bone = model.bones[rigidbody_param["bone"]]

            # 获取尾部位置
            tail_position = RigidBodyParamService._get_tail_position(model, bone)

            # 计算方向向量和旋转矩阵
            x_direction_pos, y_direction_pos, z_direction_pos, bone_shape_qq = \
                RigidBodyParamService._calculate_directions(rigidbody_param["direction"], bone.position, tail_position)

            # 创建初始变换矩阵
            mat = MMatrix4x4()
            mat.setToIdentity()
            mat.translate(bone.position)
            mat.rotate(bone_shape_qq)

            # 获取顶点位置和权重
            vposes, vweights = RigidBodyParamService._get_vertex_data(
                model, bone_vertices, bone_weights, rigidbody_name, bone.name
            )

            if not vposes:
                continue

            # 处理上下范围
            target_vposes, target_vweights = RigidBodyParamService._process_range(
                rigidbody_param, mat, vposes, vweights
            )

            # 计算形状位置和大小
            shape_position, shape_size = RigidBodyParamService._calculate_shape(
                model, rigidbody_name, rigidbody_param, target_vposes, target_vweights, bone, mat
            )

            # 调整特殊位置
            shape_position = RigidBodyParamService._adjust_special_positions(
                model, rigidbody_name, shape_position, shape_size, bone, mat
            )

            # 计算最终方向和旋转
            shape_rotation_radians = RigidBodyParamService._calculate_final_rotation(
                rigidbody_param["direction"], bone.position, tail_position
            )

            # 创建刚体
            rigidbody = RigidBody(
                rigidbody_name,
                rigidbody_param["english"],
                bone.index,
                rigidbody_param["group"],
                no_collision_group,
                rigidbody_param["shape"],
                shape_size,
                shape_position,
                shape_rotation_radians,
                10,
                0.5,
                0.5,
                0,
                0,
                0,
            )
            rigidbody.index = len(model.rigidbodies)
            model.rigidbodies[rigidbody.name] = rigidbody

            logger.info("-- -- 身体剛体[%s]", rigidbody_name)

        logger.info("-- 身体剛体設定終了")

        return model

    @staticmethod
    def _calculate_no_collision_group(rigidbody_param: Dict[str, Any]) -> int:
        """计算无碰撞组

        Args:
            rigidbody_param (Dict[str, Any]): 刚体参数

        Returns:
            int: 无碰撞组位掩码
        """
        no_collision_group = 0
        for nc in range(16):
            if nc not in rigidbody_param["no_collision_group"]:
                no_collision_group |= 1 << nc
        return no_collision_group

    @staticmethod
    def _get_tail_position(model: PmxModel, bone: Bone) -> MVector3D:
        """获取骨骼尾部位置

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 骨骼

        Returns:
            MVector3D: 尾部位置
        """
        if "手首" in bone.name:
            # 手腕使用中指尖作为方向
            return model.bones[f"{bone.name[0]}中指先"].position
        else:
            if bone.tail_index > 0:
                tail_bone = [b for b in model.bones.values() if bone.tail_index == b.index][0]
                return tail_bone.position
            else:
                return bone.tail_position + bone.position

    @staticmethod
    def _calculate_directions(
        direction: str,
        bone_position: MVector3D,
        tail_position: MVector3D
    ) -> Tuple[MVector3D, MVector3D, MVector3D, MQuaternion]:
        """计算方向向量和旋转四元数

        Args:
            direction (str): 方向类型
            bone_position (MVector3D): 骨骼位置
            tail_position (MVector3D): 尾部位置

        Returns:
            Tuple[MVector3D, MVector3D, MVector3D, MQuaternion]: X方向、Y方向、Z方向和旋转四元数
        """
        if direction == "horizonal":
            x_direction_pos = MVector3D(1, 0, 0)
            y_direction_pos = MVector3D(0, 1, 0)
        else:
            x_direction_pos = (tail_position - bone_position).normalized()
            y_direction_pos = MVector3D(1, 0, 0)

        z_direction_pos = MVector3D.crossProduct(x_direction_pos, y_direction_pos)
        bone_shape_qq = MQuaternion.fromDirection(z_direction_pos, x_direction_pos)

        return x_direction_pos, y_direction_pos, z_direction_pos, bone_shape_qq

    @staticmethod
    def _get_vertex_data(
        model: PmxModel,
        bone_vertices: Dict[str, List[int]],
        bone_weights: Dict[str, Dict[int, float]],
        rigidbody_name: str,
        bone_name: str
    ) -> Tuple[List[List[float]], List[float]]:
        """获取顶点位置和权重数据

        Args:
            model (PmxModel): PMX模型
            bone_vertices (Dict[str, List[int]]): 骨骼顶点映射
            bone_weights (Dict[str, Dict[int, float]]): 骨骼权重映射
            rigidbody_name (str): 刚体名称
            bone_name (str): 骨骼名称

        Returns:
            Tuple[List[List[float]], List[float]]: 顶点位置和权重列表
        """
        vposes = []
        vweights = []
        if "尻" in rigidbody_name:
            for vidx in bone_vertices.get("下半身", []):
                if ("右" in rigidbody_name and model.vertex_dict[vidx].position.x() <= 0) or (
                    "左" in rigidbody_name and model.vertex_dict[vidx].position.x() >= 0
                ):
                    vposes.append(model.vertex_dict[vidx].position.data())
                    vweights.append(bone_weights["下半身"][vidx])
        else:
            for vidx in bone_vertices.get(bone_name, []):
                vposes.append(model.vertex_dict[vidx].position.data())
                vweights.append(bone_weights[bone_name][vidx])
        return vposes, vweights

    @staticmethod
    def _process_range(
        rigidbody_param: Dict[str, Any],
        mat: MMatrix4x4,
        vposes: List[List[float]],
        vweights: List[float]
    ) -> Tuple[List[List[float]], List[float]]:
        """处理上下范围

        Args:
            rigidbody_param (Dict[str, Any]): 刚体参数
            mat (MMatrix4x4): 变换矩阵
            vposes (List[List[float]]): 顶点位置列表
            vweights (List[float]): 权重列表

        Returns:
            Tuple[List[List[float]], List[float]]: 处理后的顶点位置和权重列表
        """
        if rigidbody_param["range"] in ["upper", "lower"]:
            # 重心
            gravity_pos = MVector3D(np.average(vposes, axis=0, weights=vweights))

            mat = MMatrix4x4()
            mat.setToIdentity()
            mat.translate(gravity_pos)
            mat.rotate(bone_shape_qq)

            # 上下分割使用局部坐标
            local_vposes = np.array([(mat.inverted() * MVector3D(vpos)).data() for vpos in vposes])

            # 中值
            mean_y = np.mean(local_vposes, axis=0)[1]

            target_vposes = []
            target_vweights = []
            for vpos, vweight in zip(local_vposes, vweights):
                if (vpos[1] >= mean_y and rigidbody_param["range"] == "upper") or (
                    vpos[1] <= mean_y and rigidbody_param["range"] == "lower"
                ):
                    target_vposes.append((mat * MVector3D(vpos)).data())
                    target_vweights.append(vweight)
            return target_vposes, target_vweights
        else:
            return vposes, vweights

    @staticmethod
    def _calculate_shape(
        model: PmxModel,
        rigidbody_name: str,
        rigidbody_param: Dict[str, Any],
        target_vposes: List[List[float]],
        target_vweights: List[float],
        bone: Bone,
        mat: MMatrix4x4
    ) -> Tuple[MVector3D, MVector3D]:
        """计算形状位置和大小

        Args:
            model (PmxModel): PMX模型
            rigidbody_name (str): 刚体名称
            rigidbody_param (Dict[str, Any]): 刚体参数
            target_vposes (List[List[float]]): 目标顶点位置列表
            target_vweights (List[float]): 目标权重列表
            bone (Bone): 骨骼
            mat (MMatrix4x4): 变换矩阵

        Returns:
            Tuple[MVector3D, MVector3D]: 形状位置和大小
        """
        # 重心
        shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))

        mat = MMatrix4x4()
        mat.setToIdentity()
        mat.translate(shape_position)
        mat.rotate(bone_shape_qq)

        target_local_vposes = np.array([(mat.inverted() * MVector3D(vpos)).data() for vpos in target_vposes])
        local_vpos_diff = np.max(target_local_vposes, axis=0) - np.min(target_local_vposes, axis=0)

        if rigidbody_param["shape"] == 0:
            x_size = y_size = np.mean(local_vpos_diff) / 2
        else:
            x_size = np.mean(local_vpos_diff[0::2]) / 2
            y_size = local_vpos_diff[1] - x_size * 0.7

        if rigidbody_name in ["上半身2", "上半身3"]:
            # 稍微向后偏移
            shape_position.setZ(shape_position.z() + (x_size * 0.5))

        shape_size = MVector3D(x_size, y_size, x_size) * rigidbody_param.get("ratio", MVector3D(1, 1, 1))

        if rigidbody_param["shape"] == 0:
            # 球形刚体使用包围盒中心
            shape_position = RigidBodyParamService._adjust_sphere_position(
                model, rigidbody_name, target_vposes, target_vweights, x_size
            )

        return shape_position, shape_size

    @staticmethod
    def _adjust_sphere_position(
        model: PmxModel,
        rigidbody_name: str,
        target_vposes: List[List[float]],
        target_vweights: List[float],
        x_size: float
    ) -> MVector3D:
        """调整球形刚体位置

        Args:
            model (PmxModel): PMX模型
            rigidbody_name (str): 刚体名称
            target_vposes (List[List[float]]): 目标顶点位置列表
            target_vweights (List[float]): 目标权重列表
            x_size (float): X轴大小

        Returns:
            MVector3D: 调整后的位置
        """
        shape_position = MVector3D(np.mean([np.max(target_vposes, axis=0), np.min(target_vposes, axis=0)], axis=0))

        if "尻" in rigidbody_name:
            shape_position = MVector3D(
                np.average(
                    [model.bones["下半身"].position.data(), model.bones[f"{rigidbody_name[0]}足"].position.data()],
                    axis=0,
                    weights=[0.3, 0.7],
                )
            )
        elif "胸" in rigidbody_name:
            shape_position = MVector3D(
                np.average(
                    [shape_position.data(), model.bones[f"{rigidbody_name[0]}胸"].position.data()],
                    axis=0,
                    weights=[0.3, 0.7],
                )
            )
        elif "後頭部" in rigidbody_name:
            shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))
            shape_position.setZ(shape_position.z() + (x_size * 0.3))
        elif "頭" in rigidbody_name:
            shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))
            shape_position.setY(shape_position.y() + (x_size * 0.3))
            shape_position.setZ(shape_position.z() - (x_size * 0.1))

        return shape_position

    @staticmethod
    def _adjust_special_positions(
        model: PmxModel,
        rigidbody_name: str,
        shape_position: MVector3D,
        shape_size: MVector3D,
        bone: Bone,
        mat: MMatrix4x4
    ) -> MVector3D:
        """调整特殊位置

        Args:
            model (PmxModel): PMX模型
            rigidbody_name (str): 刚体名称
            shape_position (MVector3D): 形状位置
            shape_size (MVector3D): 形状大小
            bone (Bone): 骨骼
            mat (MMatrix4x4): 变换矩阵

        Returns:
            MVector3D: 调整后的位置
        """
        x_size = shape_size.x()
        y_size = shape_size.y()

        if "足首" in rigidbody_name or "首" == rigidbody_name or "太もも" in rigidbody_name:
            mat = MMatrix4x4()
            mat.setToIdentity()
            mat.translate(shape_position)
            mat.rotate(bone_shape_qq)

            if "足首" in rigidbody_name:
                shape_position = mat * MVector3D(0, -y_size * 0.15, x_size * 0.2)
            elif "首" == rigidbody_name:
                shape_position = mat * MVector3D(0, 0, x_size * 0.3)
            elif "太もも" in rigidbody_name:
                shape_position = mat * MVector3D(x_size * 0.1 * np.sign(bone.position.x()), 0, -x_size * 0.1)

        return shape_position

    @staticmethod
    def _calculate_final_rotation(
        direction: str,
        bone_position: MVector3D,
        tail_position: MVector3D
    ) -> MVector3D:
        """计算最终旋转

        Args:
            direction (str): 方向类型
            bone_position (MVector3D): 骨骼位置
            tail_position (MVector3D): 尾部位置

        Returns:
            MVector3D: 旋转弧度
        """
        if direction == "horizonal":
            x_direction_pos = MVector3D(1, 0, 0)
            y_direction_pos = MVector3D(0, 1, 0)
        elif direction == "vertical":
            x_direction_pos = (tail_position - bone_position).normalized()
            y_direction_pos = MVector3D(1, 0, 0)
        else:
            x_direction_pos = (bone_position - tail_position).normalized()
            y_direction_pos = MVector3D(-1, 0, 0)

        z_direction_pos = MVector3D.crossProduct(x_direction_pos, y_direction_pos)
        shape_qq = MQuaternion.fromDirection(z_direction_pos, x_direction_pos)
        shape_euler = shape_qq.toEulerAngles()

        return MVector3D(
            math.radians(shape_euler.x()),
            math.radians(shape_euler.y()),
            math.radians(shape_euler.z())
        )

    @staticmethod
    def create_param(param_type: str, **kwargs) -> RigidBodyParam:
        """创建刚体参数

        Args:
            param_type (str): 参数类型，可以是：
                - "default": 默认参数
                - "heavy": 重型参数
                - "light": 轻型参数
                - "custom": 自定义参数
            **kwargs: 自定义参数值

        Returns:
            RigidBodyParam: 创建的刚体参数
        """
        if param_type == "default":
            return RigidBodyParamService._create_default_param()
        elif param_type == "heavy":
            return RigidBodyParamService._create_heavy_param()
        elif param_type == "light":
            return RigidBodyParamService._create_light_param()
        elif param_type == "custom":
            return RigidBodyParamService._create_custom_param(**kwargs)
        else:
            logger.warning(f"未知的参数类型: {param_type}，使用默认参数")
            return RigidBodyParamService._create_default_param()

    @staticmethod
    def _create_default_param() -> RigidBodyParam:
        """创建默认刚体参数

        Returns:
            RigidBodyParam: 默认刚体参数
        """
        return RigidBodyParam(
            mass=1.0,
            linear_damping=0.5,
            angular_damping=0.5,
            restitution=0.5,
            friction=0.5
        )

    @staticmethod
    def _create_heavy_param() -> RigidBodyParam:
        """创建重型刚体参数

        Returns:
            RigidBodyParam: 重型刚体参数
        """
        return RigidBodyParam(
            mass=2.0,
            linear_damping=0.7,
            angular_damping=0.7,
            restitution=0.3,
            friction=0.7
        )

    @staticmethod
    def _create_light_param() -> RigidBodyParam:
        """创建轻型刚体参数

        Returns:
            RigidBodyParam: 轻型刚体参数
        """
        return RigidBodyParam(
            mass=0.5,
            linear_damping=0.3,
            angular_damping=0.3,
            restitution=0.7,
            friction=0.3
        )

    @staticmethod
    def _create_custom_param(
        mass: float = 1.0,
        linear_damping: float = 0.5,
        angular_damping: float = 0.5,
        restitution: float = 0.5,
        friction: float = 0.5
    ) -> RigidBodyParam:
        """创建自定义刚体参数

        Args:
            mass (float): 质量
            linear_damping (float): 线性阻尼
            angular_damping (float): 角度阻尼
            restitution (float): 弹性
            friction (float): 摩擦力

        Returns:
            RigidBodyParam: 自定义刚体参数
        """
        return RigidBodyParam(
            mass=mass,
            linear_damping=linear_damping,
            angular_damping=angular_damping,
            restitution=restitution,
            friction=friction
        )

    @staticmethod
    def validate_param(param: RigidBodyParam) -> bool:
        """验证刚体参数是否有效

        Args:
            param (RigidBodyParam): 要验证的刚体参数

        Returns:
            bool: 参数是否有效
        """
        try:
            # 检查参数范围
            if param.mass <= 0:
                logger.warning("质量必须大于0")
                return False

            if not (0 <= param.linear_damping <= 1):
                logger.warning("线性阻尼必须在0到1之间")
                return False

            if not (0 <= param.angular_damping <= 1):
                logger.warning("角度阻尼必须在0到1之间")
                return False

            if not (0 <= param.restitution <= 1):
                logger.warning("弹性必须在0到1之间")
                return False

            if not (0 <= param.friction <= 1):
                logger.warning("摩擦力必须在0到1之间")
                return False

            return True
        except Exception as e:
            logger.error(f"验证刚体参数时发生错误: {str(e)}")
            return False

    @staticmethod
    def optimize_param(param: RigidBodyParam, target_type: str) -> RigidBodyParam:
        """优化刚体参数

        Args:
            param (RigidBodyParam): 要优化的刚体参数
            target_type (str): 目标类型，可以是：
                - "stability": 稳定性优化
                - "performance": 性能优化
                - "physics": 物理表现优化

        Returns:
            RigidBodyParam: 优化后的刚体参数
        """
        if target_type == "stability":
            # 增加阻尼，减少弹性，提高稳定性
            param.linear_damping = min(param.linear_damping * 1.2, 1.0)
            param.angular_damping = min(param.angular_damping * 1.2, 1.0)
            param.restitution = max(param.restitution * 0.8, 0.0)
        elif target_type == "performance":
            # 简化物理计算，提高性能
            param.mass = round(param.mass, 2)
            param.linear_damping = round(param.linear_damping, 2)
            param.angular_damping = round(param.angular_damping, 2)
            param.restitution = round(param.restitution, 2)
            param.friction = round(param.friction, 2)
        elif target_type == "physics":
            # 减少阻尼，增加弹性，提高物理表现
            param.linear_damping = max(param.linear_damping * 0.8, 0.0)
            param.angular_damping = max(param.angular_damping * 0.8, 0.0)
            param.restitution = min(param.restitution * 1.2, 1.0)

        return param 