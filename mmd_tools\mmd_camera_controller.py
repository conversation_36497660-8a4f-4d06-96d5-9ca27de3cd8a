# import bge
# import bpy
# import mathutils
# import math

# class MMDCameraController(bge.types.KX_PythonComponent):
     
#     """
#     MMD摄像机控制器组件
#     直接在Blender数据中查找和控制MMD父子摄像机运动
#     避免游戏引擎对象同步问题
#     """
    
#     # 组件参数
#     args = {
#         "animation_speed": 1.0,      # 动画播放速度
#         "loop_animation": True,      # 是否循环播放
#         "auto_start": True,          # 是否自动开始播放
#         "camera_name": "Camera",     # 摄像机对象名称
#         "parent_name": "MMD_Camera", # 父对象名称
#         "init_delay": 25,            # 初始化延迟帧数
#     }
    
#     def start(self, args):
#         """初始化组件"""
#         print(f"[MMDCameraController] 开始初始化组件...")
        
#         self.animation_speed = args.get("animation_speed", 1.0)
#         self.loop_animation = args.get("loop_animation", True)
#         self.auto_start = args.get("auto_start", True)
#         self.camera_name = args.get("camera_name", "Camera")
#         self.parent_name = args.get("parent_name", "MMD_Camera")
#         self.init_delay = args.get("init_delay", 30)
        
#         print(f"[MMDCameraController] 参数设置完成:")
#         print(f"  - 动画速度: {self.animation_speed}")
#         print(f"  - 循环播放: {self.loop_animation}")
#         print(f"  - 自动开始: {self.auto_start}")
#         print(f"  - 摄像机名称: {self.camera_name}")
#         print(f"  - 父对象名称: {self.parent_name}")
#         print(f"  - 初始化延迟: {self.init_delay} 帧")
        
#         # 动画状态
#         self.current_frame = 0.0
#         self.is_playing = False  # 延迟开始
#         self.animation_data = []
#         self.total_frames = 0
        
#         # 摄像机对象（延迟获取）
#         self.camera_blender_obj = None
#         self.parent_blender_obj = None
#         self.objects_initialized = False
#         self.frame_count = 0
        
#         print(f"[MMDCameraController] 将在 {self.init_delay} 帧后开始查找摄像机对象...")
        
#         # 生成模拟动画数据
#         self._generate_sample_animation()
        
#         print(f"[MMDCameraController] 组件初始化完成，总帧数: {self.total_frames}")
    
#     def _find_mmd_camera_in_data(self):
#         """在Blender数据中查找MMD摄像机结构"""
#         print(f"[MMDCameraController] 在Blender数据中查找MMD摄像机结构...")
        
#         # 打印所有Blender对象
#         print(f"[MMDCameraController] Blender数据中的所有对象:")
#         for i, obj in enumerate(bpy.data.objects):
#             print(f"  {i+1}. {obj.name} (类型: {obj.type}, 父对象: {obj.parent.name if obj.parent else 'None'})")
        
#         # 1. 查找指定名称的父对象
#         parent_obj = bpy.data.objects.get(self.parent_name)
#         if parent_obj:
#             print(f"[MMDCameraController] ✅ 找到父对象: {parent_obj.name}")
#             self.parent_blender_obj = parent_obj
#         else:
#             print(f"[MMDCameraController] ❌ 未找到指定父对象: {self.parent_name}")
            
#             # 查找可能的MMD摄像机父对象
#             for obj in bpy.data.objects:
#                 if obj.type == 'EMPTY' and "mmd" in obj.name.lower() and "camera" in obj.name.lower():
#                     print(f"[MMDCameraController] 🔍 发现可能的MMD摄像机父对象: {obj.name}")
#                     self.parent_blender_obj = obj
#                     self.parent_name = obj.name  # 更新名称
#                     break
        
#         # 2. 查找指定名称的摄像机
#         camera_obj = bpy.data.objects.get(self.camera_name)
#         if camera_obj and camera_obj.type == 'CAMERA':
#             print(f"[MMDCameraController] ✅ 找到摄像机: {camera_obj.name}")
#             self.camera_blender_obj = camera_obj
#         else:
#             print(f"[MMDCameraController] ❌ 未找到指定摄像机: {self.camera_name}")
            
#             # 查找MMD结构的摄像机（有Empty父对象的摄像机）
#             for obj in bpy.data.objects:
#                 if obj.type == 'CAMERA' and obj.parent and obj.parent.type == 'EMPTY':
#                     print(f"[MMDCameraController] 🔍 发现MMD结构的摄像机: {obj.name} (父对象: {obj.parent.name})")
#                     self.camera_blender_obj = obj
#                     self.camera_name = obj.name  # 更新名称
                    
#                     # 如果还没找到父对象，使用这个摄像机的父对象
#                     if not self.parent_blender_obj:
#                         self.parent_blender_obj = obj.parent
#                         self.parent_name = obj.parent.name
#                         print(f"[MMDCameraController] ✅ 同时找到父对象: {self.parent_name}")
#                     break
        
#         # 检查是否成功找到
#         if self.camera_blender_obj and self.parent_blender_obj:
#             self.objects_initialized = True
#             print(f"[MMDCameraController] ✅ MMD摄像机结构查找成功!")
#             print(f"  - 父对象: {self.parent_blender_obj.name}")
#             print(f"  - 摄像机: {self.camera_blender_obj.name}")
            
#             # 如果设置了自动开始，现在开始播放
#             if self.auto_start:
#                 self.is_playing = True
#                 print(f"[MMDCameraController] ✅ 开始播放动画")
            
#             return True
#         else:
#             print(f"[MMDCameraController] ❌ MMD摄像机结构查找失败")
#             if not self.parent_blender_obj:
#                 print(f"[MMDCameraController] 缺失父对象")
#             if not self.camera_blender_obj:
#                 print(f"[MMDCameraController] 缺失摄像机对象")
#             return False
    
#     def _generate_sample_animation(self):
#         """生成示例动画数据"""
#         print(f"[MMDCameraController] 开始生成示例动画数据...")
        
#         # 模拟一个360度环绕的摄像机动画，持续10秒（300帧 @ 30fps）
#         self.total_frames = 300
#         self.animation_data = []
        
#         for frame in range(self.total_frames):
#             # 计算时间进度 (0.0 到 1.0)
#             progress = frame / self.total_frames
            
#             # 环绕运动参数
#             radius = 15.0  # 环绕半径
#             height = 5.0 + math.sin(progress * math.pi * 4) * 2.0  # 上下波动
#             angle = progress * math.pi * 2  # 完整的360度旋转
            
#             # 计算位置 (环绕目标点)
#             target_pos = mathutils.Vector((0, 0, 3))  # 目标注视点
#             camera_pos = mathutils.Vector((
#                 target_pos.x + math.cos(angle) * radius,
#                 target_pos.y + math.sin(angle) * radius,
#                 target_pos.z + height
#             ))
            
#             # 计算旋转 (始终看向目标点)
#             direction = (target_pos - camera_pos).normalized()
            
#             # 计算欧拉角
#             # Yaw (绕Z轴旋转)
#             yaw = math.atan2(direction.y, direction.x)
#             # Pitch (绕X轴旋转)
#             pitch = math.asin(-direction.z)
#             # Roll保持为0
#             roll = 0.0
            
#             # 摄像机距离 (子对象的Y位置)
#             distance = -45.0 + math.sin(progress * math.pi * 2) * 10.0
            
#             # 视野角度变化
#             fov = math.radians(30 + math.sin(progress * math.pi * 6) * 15)
            
#             # 透视/正交切换 (可选)
#             is_perspective = True
            
#             frame_data = {
#                 'frame': frame,
#                 'location': camera_pos,
#                 'rotation': mathutils.Euler((pitch, roll, yaw), 'XYZ'),
#                 'distance': distance,
#                 'fov': fov,
#                 'is_perspective': is_perspective
#             }
            
#             self.animation_data.append(frame_data)
        
#         print(f"[MMDCameraController] 示例动画数据生成完成，共{self.total_frames}帧")
    
#     def update(self):
#         """每帧更新"""
#         self.frame_count += 1
        
#         # 如果还没初始化且到了延迟时间，尝试查找摄像机
#         if not self.objects_initialized and self.frame_count >= self.init_delay:
#             print(f"[MMDCameraController] 第{self.frame_count}帧，开始查找摄像机对象...")
#             self._find_mmd_camera_in_data()
#             return
        
#         # 如果还没初始化，跳过
#         if not self.objects_initialized:
#             return
        
#         if not self.is_playing or not self.animation_data:
#             return
            
#         # 更新当前帧
#         self.current_frame += self.animation_speed
        
#         # 处理循环
#         if self.current_frame >= self.total_frames:
#             if self.loop_animation:
#                 self.current_frame = 0.0
#                 print(f"[MMDCameraController] 动画循环，重置到第0帧")
#             else:
#                 self.is_playing = False
#                 print(f"[MMDCameraController] 动画播放完成，停止播放")
#                 return
        
#         # 获取当前帧数据
#         frame_index = int(self.current_frame) % len(self.animation_data)
#         current_data = self.animation_data[frame_index]
        
#         # 可选：插值到下一帧实现平滑动画
#         if frame_index + 1 < len(self.animation_data):
#             next_data = self.animation_data[frame_index + 1]
#             blend_factor = self.current_frame - int(self.current_frame)
#             interpolated_data = self._interpolate_frame_data(current_data, next_data, blend_factor)
#         else:
#             interpolated_data = current_data
        
#         # 应用动画数据到摄像机
#         self._apply_camera_data(interpolated_data)
    
#     def _interpolate_frame_data(self, data1, data2, factor):
#         """在两帧数据之间进行插值"""
#         result = {}
        
#         # 位置插值
#         result['location'] = data1['location'].lerp(data2['location'], factor)
        
#         # 旋转插值 (欧拉角)
#         rot1 = data1['rotation']
#         rot2 = data2['rotation']
#         # 简单的线性插值，实际应用中可能需要更复杂的插值
#         result['rotation'] = mathutils.Euler((
#             rot1.x + (rot2.x - rot1.x) * factor,
#             rot1.y + (rot2.y - rot1.y) * factor,
#             rot1.z + (rot2.z - rot1.z) * factor
#         ), 'XYZ')
        
#         # 距离插值
#         result['distance'] = data1['distance'] + (data2['distance'] - data1['distance']) * factor
        
#         # 视野角度插值
#         result['fov'] = data1['fov'] + (data2['fov'] - data1['fov']) * factor
        
#         # 透视模式 (不插值，使用第一帧的值)
#         result['is_perspective'] = data1['is_perspective']
        
#         return result
    
#     def _apply_camera_data(self, data):
#         """将动画数据应用到摄像机对象 - 直接操作Blender对象"""
#         if not self.parent_blender_obj or not self.camera_blender_obj:
#             return
            
#         # 设置父对象位置和旋转
#         self.parent_blender_obj.location = data['location']
#         self.parent_blender_obj.rotation_euler = data['rotation']
        
#         # 设置子对象距离 (通过Y轴位置)
#         local_pos = self.camera_blender_obj.location
#         self.camera_blender_obj.location = (local_pos.x, data['distance'], local_pos.z)
        
#         # 如果摄像机有lens属性，设置视野
#         camera_data = self.camera_blender_obj.data
#         if hasattr(camera_data, 'lens'):
#             # 将FOV转换为lens值 (简化公式)
#             # 假设sensor_height = 32mm
#             sensor_height = 32.0
#             lens = sensor_height / (2 * math.tan(data['fov'] / 2))
#             camera_data.lens = lens

#     def play(self):
#         """开始播放动画"""
#         self.is_playing = True
#         print(f"[MMDCameraController] 开始播放动画")
    
#     def pause(self):
#         """暂停动画"""
#         self.is_playing = False
#         print(f"[MMDCameraController] 暂停动画")
    
#     def stop(self):
#         """停止动画并重置到开始"""
#         self.is_playing = False
#         self.current_frame = 0.0
#         print(f"[MMDCameraController] 停止动画并重置")
    
#     def set_frame(self, frame):
#         """设置到指定帧"""
#         self.current_frame = max(0, min(frame, self.total_frames - 1))
#         print(f"[MMDCameraController] 设置到第{self.current_frame}帧")
#         if self.animation_data and self.objects_initialized:
#             frame_index = int(self.current_frame)
#             self._apply_camera_data(self.animation_data[frame_index])
    
#     def set_speed(self, speed):
#         """设置播放速度"""
#         self.animation_speed = speed
#         print(f"[MMDCameraController] 设置播放速度: {speed}")
    
#     def load_animation_data(self, animation_data):
#         """加载自定义动画数据"""
#         self.animation_data = animation_data
#         self.total_frames = len(animation_data)
#         self.current_frame = 0.0
#         print(f"[MMDCameraController] 已加载动画数据，总帧数: {self.total_frames}")


# # VMD数据结构示例 (可用于加载真实的VMD动画数据)
# class VMDCameraFrame:
#     """VMD摄像机帧数据结构"""
#     def __init__(self, frame_number, location, rotation, distance, fov, is_perspective=True):
#         self.frame_number = frame_number
#         self.location = location        # mathutils.Vector (x, y, z)
#         self.rotation = rotation        # mathutils.Euler (rx, ry, rz)
#         self.distance = distance        # float
#         self.fov = fov                 # float (弧度)
#         self.is_perspective = is_perspective  # bool


# # 使用示例函数
# def create_sample_vmd_data():
#     """创建示例VMD摄像机数据"""
#     frames = []
#     for i in range(100):
#         frame_data = {
#             'frame': i,
#             'location': mathutils.Vector((math.sin(i * 0.1) * 10, math.cos(i * 0.1) * 10, 5)),
#             'rotation': mathutils.Euler((0, 0, i * 0.1), 'XYZ'),
#             'distance': -45.0,
#             'fov': math.radians(30),
#             'is_perspective': True
#         }
#         frames.append(frame_data)
#     return frames 