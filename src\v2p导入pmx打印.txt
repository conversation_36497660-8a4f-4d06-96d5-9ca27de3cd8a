INFO:root:****************************************
INFO:root: mmd_tools.pmx module
INFO:root:----------------------------------------
INFO:root: Start to load model data form a pmx file
INFO:root:            by the mmd_tools.pmx modlue.
INFO:root:
INFO:root:----------------------------
INFO:root:pmx header information
INFO:root:----------------------------
INFO:root:pmx version: 2.0
INFO:root:encoding: <Encoding charset utf-16-le>
INFO:root:number of uvs: 0
INFO:root:vertex index size: 4 byte(s)
INFO:root:texture index: 1 byte(s)
INFO:root:material index: 1 byte(s)
INFO:root:bone index: 1 byte(s)
INFO:root:morph index: 1 byte(s)
INFO:root:rigid index: 1 byte(s)
INFO:root:----------------------------
INFO:root:Model name: VRoid Model
INFO:root:Model name(english): VRoid Model
INFO:root:Comment:Created by VRoid2Pmx
INFO:root:Comment(english):Created by VRoid2Pmx
INFO:root:
INFO:root:------------------------------
INFO:root:Load Vertices
INFO:root:------------------------------
INFO:root:----- Loaded 216750 vertices
INFO:root:
INFO:root:------------------------------
INFO:root: Load Faces
INFO:root:------------------------------
INFO:root: Load 131824 faces
INFO:root:
INFO:root:------------------------------
INFO:root: Load Textures
INFO:root:------------------------------
INFO:root: ----- Loaded 0 textures
INFO:root:
INFO:root:------------------------------
INFO:root: Load Materials
INFO:root:------------------------------
INFO:root:Material 0: Mat_NYM_Skin
INFO:root:Material 1: Mat_NYM_AccessoryBoots
INFO:root:Material 2: Mat_NYM_Accessory
INFO:root:Material 3: Mat_NYM_Hair
INFO:root:Material 4: Mat_NYM_Blow
INFO:root:Material 5: Mat_NYM_Face
INFO:root:Material 6: Mat_NYM_Face_Transparent
INFO:root:Material 7: Mat_NYM_Eye
INFO:root:Material 8: Mat_NYM_EyeHighLight
INFO:root:Material 9: Mat_NYM_FacialEff_Face
INFO:root:Material 10: Mat_NYM_FacialEff_Eye
INFO:root:Material 11: Mat_NYM_FacialEff_Head
INFO:root:Material 12: Mat_NYM_FacialEff_Multi
INFO:root:Material 13: Mat_NYM_FacialEff_Other
INFO:root:Material 14: Mat_NYM_Hair_Shadow
INFO:root:Material 15: Mat_NYM_Wear_B
INFO:root:Material 16: Mat_NYM_Wear_A
INFO:root:Material 17: Mat_NYM_Doll
INFO:root:Material 18: Mat_NYM_Wear_A_Transparent
INFO:root:----- Loaded 19  materials.
INFO:root:
INFO:root:------------------------------
INFO:root: Load Bones
INFO:root:------------------------------
INFO:root:Bone 0: 全ての親
INFO:root:Bone 1: センター
INFO:root:Bone 2: グルーブ
INFO:root:Bone 3: 腰
INFO:root:Bone 4: 下半身
INFO:root:Bone 5: 上半身
INFO:root:Bone 6: 上半身2
INFO:root:Bone 7: 上半身3
INFO:root:Bone 8: 首
INFO:root:Bone 9: 頭
INFO:root:Bone 10: 両目
INFO:root:Bone 11: 左目
INFO:root:Bone 12: 右目
INFO:root:Bone 13: 両目光
INFO:root:Bone 14: 左目光
INFO:root:Bone 15: 右目光
INFO:root:Bone 16: 舌1
ERROR:root: * Corrupted file: unpack requires a buffer of 6357062 bytes
INFO:root: Finished loading.
INFO:root:----------------------------------------
INFO:root: mmd_tools.pmx module
INFO:root:****************************************
INFO:root:****************************************
INFO:root: mmd_tools.import_pmx module
INFO:root:----------------------------------------
INFO:root: Start to load model data form a pmx file
INFO:root:            by the mmd_tools.pmx modlue.
INFO:root:
INFO:root:Cleaning PMX data...
WARNING:root:   - removed 131824 faces
WARNING:root:   - removed 216750 vertices
INFO:root:   - Done!!
INFO:root:Setting custom normals...
INFO:root:   - Marked 0/0 (0.00%) sharp edges with angle: 179.0 degrees
INFO:root:   - Done!!
INFO:root: Finished importing the model in 0.096544 seconds.
INFO:root:----------------------------------------
INFO:root: mmd_tools.import_pmx module
INFO:root:****************************************