from setuptools import Extension
from numpy import get_include   # cimport numpy を使うため
import numpy as np
import os

# bezier_path = 'C:/Development/Anaconda3/envs/vmdsizing_cython/Lib/site-packages/bezier/include'
# 使用相对路径或尝试自动检测bezier路径
try:
    import bezier
    import os
    bezier_path = os.path.join(os.path.dirname(bezier.__file__), 'include')
    if not os.path.exists(bezier_path):
        bezier_path = ''
except ImportError:
    bezier_path = ''

kwargs = {
    "package_data": {
        "module": ["*.pxd", "*.pyx", "*.c", "*.cpp", "*.h", "*.pyd"],
        "mmd": ["*.pxd", "*.pyx", "*.c", "*.cpp", "*.h", "*.pyd"],
        "utils": ["*.pxd", "*.pyx", "*.c", "*.cpp", "*.h", "*.pyd"]
    }
}

def get_ext():
    ext = []
    
    # 首先编译基础模块
    base_modules = [
        ("module.MMath", "module/MMath.pyx"),
        ("module.MParams", "module/MParams.pyx")
    ]
    
    for module_name, source in base_modules:
        include_dirs = ['.', get_include()]
        if bezier_path:
            include_dirs.append(bezier_path)
        ext.append(Extension(
            module_name,
            sources=[source],
            include_dirs=include_dirs,
            language="c++",
            extra_compile_args=["/EHsc"] if os.name == 'nt' else [],
            define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')]
        ))

    # 其他Cython源文件
    cython_sources = [
        ("utils.MBezierUtils", "utils/MBezierUtils.pyx"),
        ("utils.MServiceUtils", "utils/MServiceUtils.pyx"),
        ("mmd.VmdData", "mmd/VmdData.pyx"),
        ("mmd.PmxData", "mmd/PmxData.pyx")
    ]
    
    for module_name, source in cython_sources:
        include_dirs = ['.', get_include()]
        if bezier_path:
            include_dirs.append(bezier_path)
        ext.append(Extension(
            module_name,
            sources=[source],
            include_dirs=include_dirs,
            language="c++",
            extra_compile_args=["/EHsc"] if os.name == 'nt' else [],
            define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')]
        ))
    
    # Python源文件
    python_sources = [
        ("utils.MLogger", "utils/MLogger.py"),
        ("mmd.VmdReader", "mmd/VmdReader.py"),
        ("mmd.PmxReader", "mmd/PmxReader.py"),
        ("mmd.PmxWriter", "mmd/PmxWriter.py")
    ]

    for module_name, source in python_sources:
        include_dirs = ['.', get_include()]
        if bezier_path:
            include_dirs.append(bezier_path)
        ext.append(Extension(
            module_name,
            sources=[source],
            include_dirs=include_dirs
        ))
    
    return ext

# ext = [Extension("module.MMath", sources=["module/MMath.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("module.MMath", sources=["module/MMath.py"], include_dirs=['.', bezier_path, get_include()], define_macros=[('CYTHON_TRACE', '1')]), \
#        Extension("module.MParams", sources=["module/MParams.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("module.MOptions", sources=["module/MOptions.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("utils.MBezierUtils", sources=["utils/MBezierUtils.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("utils.MLogger", sources=["utils/MLogger.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("utils.MServiceUtils", sources=["utils/MServiceUtils.py"], include_dirs=['.', bezier_path, get_include()], define_macros=[('CYTHON_TRACE', '1')]), \
#        Extension("utils.MServiceUtils", sources=["utils/MServiceUtils.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("mmd.PmxData", sources=["mmd/PmxData.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("mmd.PmxReader", sources=["mmd/PmxReader.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("mmd.VmdData", sources=["mmd/VmdData.py"], include_dirs=['.', bezier_path, get_include()], define_macros=[('CYTHON_TRACE', '1')]), \
#        Extension("mmd.VmdData", sources=["mmd/VmdData.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("mmd.VmdReader", sources=["mmd/VmdReader.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("mmd.VpdReader", sources=["mmd/VpdReader.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("mmd.VmdWriter", sources=["mmd/VmdWriter.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("service.parts.MoveService", sources=["service/parts/MoveService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("service.parts.CameraService", sources=["service/parts/CameraService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("service.parts.MorphService", sources=["service/parts/MorphService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("service.parts.StanceService", sources=["service/parts/StanceService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("service.parts.ArmAvoidanceService", sources=["service/parts/ArmAvoidanceService.py"], include_dirs=['.', bezier_path, get_include()], define_macros=[('CYTHON_TRACE', '1')]), \
#        Extension("service.parts.ArmAvoidanceService", sources=["service/parts/ArmAvoidanceService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("service.parts.ArmAlignmentService", sources=["service/parts/ArmAlignmentService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        Extension("service.SizingService", sources=["service/SizingService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        # Extension("service.ConvertSmoothService", sources=["service/ConvertSmoothService.py"], include_dirs=['.', bezier_path, get_include()]), \
#        ]


