# MMD摄像机创建器使用说明

这是一个用于UPBGE游戏引擎的MMD摄像机创建组件，可以在游戏运行时动态创建MMD父子摄像机结构。

## 功能特点

- 运行时动态创建MMD父子摄像机结构（Empty父对象 + Camera子对象）
- 自动设置正确的父子关系和变换属性
- 支持自定义位置、旋转、距离等参数
- 可与MMDCameraController组件无缝配合使用
- 提供摄像机销毁和管理功能
- 包含键盘控制示例

## 前置准备

### 1. 在Blender场景中准备模板对象

为了让创建器能够正常工作，需要在Blender场景中准备以下模板对象：

```python
# 在Blender中运行此脚本创建模板对象
import bpy

# 创建Empty模板（可选，但推荐）
empty_template = bpy.data.objects.new(name="EmptyTemplate", object_data=None)
bpy.context.collection.objects.link(empty_template)
empty_template.location = (1000, 1000, 1000)  # 放到远离场景的位置
empty_template.hide_viewport = True  # 在视口中隐藏

# 创建Camera模板（必需）
camera_data = bpy.data.cameras.new("CameraTemplateData")
camera_template = bpy.data.objects.new("CameraTemplate", camera_data)
bpy.context.collection.objects.link(camera_template)
camera_template.location = (1000, 1000, 1000)  # 放到远离场景的位置
camera_template.hide_viewport = True  # 在视口中隐藏

# 设置摄像机基本属性
camera_data.lens = 35.0
camera_data.clip_start = 0.1
camera_data.clip_end = 1000.0
```

### 2. 简化版本（如果没有模板）

如果场景中已经有任何摄像机对象，创建器会自动使用它作为模板：

```python
# 确保场景中至少有一个摄像机对象
# 创建器会自动找到并使用它作为模板
```

## 使用方法

### 1. 基本使用

将组件添加到任意游戏对象：

1. 将`mmd_camera_creator.py`复制到UPBGE项目文件夹
2. 在UPBGE中选择任意游戏对象
3. 在Logic Properties中添加`MMDCameraCreator`组件
4. 配置参数（见下面的参数说明）
5. 运行游戏，组件会自动创建摄像机

### 2. 组件参数

- `parent_name`: 父对象名称（默认"MMD_Camera"）
- `camera_name`: 摄像机名称（默认"Camera"）
- `initial_position`: 父对象初始位置[x, y, z]（默认[0.0, 0.0, 10.0]）
- `initial_rotation`: 父对象初始旋转[rx, ry, rz]度（默认[0.0, 0.0, 0.0]）
- `camera_distance`: 摄像机距离（默认-45.0）
- `camera_lens`: 摄像机镜头焦距（默认35.0）
- `auto_create`: 是否自动创建（默认True）
- `set_as_active`: 是否设置为活动摄像机（默认True）
- `create_controller`: 是否同时创建控制器组件（默认True）

## API使用说明

### 基本控制方法

```python
# 获取创建器组件
creator = obj['MMDCameraCreator']

# 手动创建摄像机（如果auto_create为False）
success = creator.create_mmd_camera()

# 销毁摄像机
creator.destroy_camera()

# 获取创建的摄像机对象
camera_info = creator.get_camera_objects()
print(f"父对象: {camera_info['parent']}")
print(f"摄像机: {camera_info['camera']}")
print(f"创建状态: {camera_info['created']}")
```

### 运行时函数调用

```python
# 使用辅助函数直接创建摄像机
from mmd_camera_creator import create_mmd_camera_runtime

scene = bge.logic.getCurrentScene()
camera_objects = create_mmd_camera_runtime(
    scene=scene,
    parent_name="MyMMDCamera",
    camera_name="MyCamera",
    position=(5, 5, 8),
    rotation=(0, 0, 45),  # 45度旋转
    distance=-30.0
)

if camera_objects:
    print("摄像机创建成功!")
    parent_obj = camera_objects['parent']
    camera_obj = camera_objects['camera']
else:
    print("摄像机创建失败!")
```

### 键盘控制示例

```python
# 使用内置的键盘控制组件
# 将CameraCreatorKeyboardControl组件添加到任意对象
# C键: 创建摄像机
# D键: 销毁摄像机
```

## 与控制器组件配合使用

创建摄像机后，可以与`MMDCameraController`组件配合使用：

```python
# 方法1: 通过create_controller参数自动创建
creator = MMDCameraCreator()
creator.start({
    "create_controller": True,  # 自动创建控制器
    "auto_create": True
})

# 方法2: 手动添加控制器
# 1. 先创建摄像机
creator.create_mmd_camera()

# 2. 手动添加控制器组件到任意对象
controller_args = {
    "camera_name": creator.camera_name,
    "parent_name": creator.parent_name,
    "auto_start": True
}
# 然后将MMDCameraController组件添加到对象
```

## 高级用法

### 1. 批量创建多个摄像机

```python
def create_multiple_cameras():
    """创建多个摄像机用于不同视角"""
    scene = bge.logic.getCurrentScene()
    
    camera_configs = [
        {"name": "FrontView", "pos": (0, -20, 5), "rot": (0, 0, 0)},
        {"name": "SideView", "pos": (20, 0, 5), "rot": (0, 0, 90)},
        {"name": "TopView", "pos": (0, 0, 30), "rot": (90, 0, 0)},
    ]
    
    cameras = []
    for config in camera_configs:
        camera_obj = create_mmd_camera_runtime(
            scene=scene,
            parent_name=f"MMD_{config['name']}",
            camera_name=config['name'],
            position=config['pos'],
            rotation=config['rot']
        )
        if camera_obj:
            cameras.append(camera_obj)
    
    return cameras
```

### 2. 动态摄像机切换

```python
class CameraSwitcher(bge.types.KX_PythonComponent):
    """摄像机切换组件"""
    
    args = {
        "switch_key": bge.events.TABKEY,
    }
    
    def start(self, args):
        self.switch_key = args.get("switch_key", bge.events.TABKEY)
        self.keyboard = bge.logic.keyboard
        self.cameras = []
        self.current_index = 0
        
        # 收集所有MMD摄像机
        scene = bge.logic.getCurrentScene()
        for obj in scene.objects:
            if hasattr(obj, 'camera') and obj.camera:
                if obj.name.startswith("Camera") or "MMD" in obj.parent.name if obj.parent else False:
                    self.cameras.append(obj)
    
    def update(self):
        if self.keyboard.events[self.switch_key] == bge.logic.KX_INPUT_JUST_ACTIVATED:
            if self.cameras:
                self.current_index = (self.current_index + 1) % len(self.cameras)
                scene = bge.logic.getCurrentScene()
                scene.active_camera = self.cameras[self.current_index]
                print(f"切换到摄像机: {self.cameras[self.current_index].name}")
```

### 3. 摄像机预设管理

```python
class CameraPresetManager:
    """摄像机预设管理器"""
    
    PRESETS = {
        "close_up": {
            "position": (0, 0, 8),
            "rotation": (15, 0, 0),
            "distance": -25.0,
            "lens": 85.0
        },
        "wide_shot": {
            "position": (0, 0, 12),
            "rotation": (0, 0, 0),
            "distance": -60.0,
            "lens": 35.0
        },
        "bird_eye": {
            "position": (0, 0, 25),
            "rotation": (60, 0, 0),
            "distance": -30.0,
            "lens": 28.0
        }
    }
    
    @classmethod
    def create_preset_camera(cls, preset_name, scene):
        """根据预设创建摄像机"""
        if preset_name not in cls.PRESETS:
            print(f"未知预设: {preset_name}")
            return None
        
        preset = cls.PRESETS[preset_name]
        return create_mmd_camera_runtime(
            scene=scene,
            parent_name=f"MMD_{preset_name}",
            camera_name=f"Camera_{preset_name}",
            position=preset["position"],
            rotation=preset["rotation"],
            distance=preset["distance"]
        )
```

## 故障排除

### 常见问题

1. **创建失败: "未找到摄像机模板"**
   - 确保场景中至少有一个摄像机对象
   - 或者添加名为"CameraTemplate"的摄像机模板

2. **父子关系设置失败**
   - 检查UPBGE版本兼容性
   - 确保对象创建成功后再设置父子关系

3. **摄像机不可见**
   - 检查摄像机位置是否正确
   - 确认摄像机设置为活动摄像机

4. **内存泄漏**
   - 及时调用`destroy_camera()`销毁不需要的摄像机
   - 避免重复创建同名摄像机

### 调试技巧

```python
# 启用详细日志
def debug_camera_creation(creator):
    """调试摄像机创建过程"""
    print(f"创建状态: {creator.created}")
    if creator.created:
        objects = creator.get_camera_objects()
        print(f"父对象: {objects['parent'].name if objects['parent'] else 'None'}")
        print(f"摄像机: {objects['camera'].name if objects['camera'] else 'None'}")
        
        # 检查位置和旋转
        if objects['parent']:
            print(f"父对象位置: {objects['parent'].worldPosition}")
            print(f"父对象旋转: {objects['parent'].worldOrientation}")
        
        if objects['camera']:
            print(f"摄像机本地位置: {objects['camera'].localPosition}")
            print(f"摄像机世界位置: {objects['camera'].worldPosition}")
```

## 性能注意事项

1. **避免频繁创建销毁**: 摄像机创建和销毁是相对昂贵的操作
2. **限制摄像机数量**: 不要同时创建过多摄像机
3. **及时清理**: 不需要的摄像机应及时销毁
4. **使用对象池**: 对于需要频繁切换的场景，考虑使用对象池模式

## 扩展建议

这个创建器可以进一步扩展以支持：

- 摄像机动画轨道的自动绑定
- 多种摄像机类型（正交、透视等）
- 摄像机组的批量管理
- 与场景光照的联动
- 摄像机参数的实时调节界面 