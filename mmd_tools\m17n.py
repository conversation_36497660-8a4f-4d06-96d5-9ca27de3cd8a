# Copyright 2021 MMD Tools authors
# This file is part of MMD Tools.
# ##### BEGIN AUTOGENERATED I18N SECTION #####
# NOTE: You can safely move around this auto-generated block (with the begin/end markers!),
#       and edit the translations by hand.
#       Just carefully respect the format of the tuple!

# Tuple of tuples:
# ((msgctxt, msgid), (sources, gen_comments), (lang, translation, (is_fuzzy, comments)), ...)
translations_tuple = (
    (
        ("*", ""),
        ((), ()),
        (
            "ja_JP",
            "Project-Id-Version: MMD Tools 4.3.3 (0)\n",
            (
                False,
                (
                    "Blender's translation file (po format).",
                    "Copyright (C) 2024 The Blender Authors.",
                    "This file is distributed under the same license as the Blender package.",
                    "FIRST AUTHOR <EMAIL@ADDRESS>, YEAR.",
                ),
            ),
        ),
        (
            "zh_HANS",
            "Project-Id-Version: MMD Tools 4.3.3 (0)\n",
            (
                False,
                (
                    "<PERSON>len<PERSON>'s translation file (po format).",
                    "Copyright (C) 2024 The Blender Authors.",
                    "This file is distributed under the same license as the Blender package.",
                    "FIRST AUTHOR <EMAIL@ADDRESS>, YEAR.",
                ),
            ),
        ),
    ),
    (
        ("*", "Base Texture Folder"),
        (("bpy.types.MMDToolsAddonPreferences.base_texture_folder",), ()),
        ("ja_JP", "ベーステクスチャフォルダ", (False, ())),
        ("zh_HANS", "基础纹理文件夹", (False, ())),
    ),
    (
        ("*", "Path for textures shared between models"),
        (("bpy.types.MMDToolsAddonPreferences.base_texture_folder",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "模型间共用纹理的路径", (False, ())),
    ),
    (
        ("*", "Dictionary Folder"),
        (("bpy.types.MMDToolsAddonPreferences.dictionary_folder",), ()),
        ("ja_JP", "辞書フォルダ", (False, ())),
        ("zh_HANS", "词典文件夹", (False, ())),
    ),
    (
        ("*", "Path for searching csv dictionaries"),
        (("bpy.types.MMDToolsAddonPreferences.dictionary_folder",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "搜索 CSV 词典的路径", (False, ())),
    ),
    (
        ("*", "Enable MMD Model Production Features"),
        (
            (
                "bpy.types.MMDToolsAddonPreferences.enable_mmd_model_production_features",
            ),
            (),
        ),
        ("ja_JP", "MMDモデル製作機能を有効化", (False, ())),
        ("zh_HANS", "开启MMD模型制作功能", (False, ())),
    ),
    (
        ("*", "Shared Toon Texture Folder"),
        (("bpy.types.MMDToolsAddonPreferences.shared_toon_folder",), ()),
        ("ja_JP", "共有トゥーンテクスチャフォルダ", (False, ())),
        ("zh_HANS", "共用的卡通纹理文件夹", (False, ())),
    ),
    (
        (
            "*",
            'Directory path to toon textures. This is normally the "Data" directory within of your MikuMikuDance directory',
        ),
        (("bpy.types.MMDToolsAddonPreferences.shared_toon_folder",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "卡通纹理路径. 通常是 MikuMikuDance 安装路径下的 Data 目录",
            (False, ()),
        ),
    ),
    (
        ("*", "Internal MMD type of this object (DO NOT CHANGE IT DIRECTLY)"),
        (("bpy.types.Object.mmd_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该物体的内部 MMD 类型（切勿直接改动）", (False, ())),
    ),
    (
        ("*", "Rigid Body Grp Empty"),
        (("bpy.types.Object.mmd_type:'RIGID_GRP_OBJ'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体组空物体", (False, ())),
    ),
    (
        ("*", "Joint Grp Empty"),
        (("bpy.types.Object.mmd_type:'JOINT_GRP_OBJ'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "关节组空物体", (False, ())),
    ),
    (
        ("*", "Temporary Grp Empty"),
        (("bpy.types.Object.mmd_type:'TEMPORARY_GRP_OBJ'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "临时组空物体", (False, ())),
    ),
    (
        ("*", "Place Holder"),
        (("bpy.types.Object.mmd_type:'PLACEHOLDER'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "占位符", (False, ())),
    ),
    (
        ("*", "Joint"),
        (
            (
                "bpy.types.Object.mmd_type:'JOINT'",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:53",
            ),
            (),
        ),
        ("ja_JP", "ジョイント", (False, ())),
        ("zh_HANS", "关节", (False, ())),
    ),
    (
        ("*", "Rigid body"),
        (("bpy.types.Object.mmd_type:'RIGID_BODY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体", (False, ())),
    ),
    (
        ("*", "Track Target"),
        (("bpy.types.Object.mmd_type:'TRACK_TARGET'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "追踪目标", (False, ())),
    ),
    (
        ("*", "Non Collision Constraint"),
        (("bpy.types.Object.mmd_type:'NON_COLLISION_CONSTRAINT'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "非碰撞约束", (False, ())),
    ),
    (
        ("*", "Spring Constraint"),
        (("bpy.types.Object.mmd_type:'SPRING_CONSTRAINT'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "弹簧约束", (False, ())),
    ),
    (
        ("*", "Spring Goal"),
        (("bpy.types.Object.mmd_type:'SPRING_GOAL'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "弹簧目标", (False, ())),
    ),
    (
        ("*", "Bone Order Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_bone_order_menu",), ()),
        ("ja_JP", "ボーン順序", (True, ())),
        ("zh_HANS", "骨骼顺序", (True, ())),
    ),
    (
        ("*", "Display Item Frame Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_display_item_frame_menu",), ()),
        ("ja_JP", "表示項目フレームを追加", (True, ())),
        ("zh_HANS", "添加表示枠", (True, ())),
    ),
    (
        ("*", "Display Item Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_display_item_menu",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "表示枠项目", (True, ())),
    ),
    (
        ("*", "Joint Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_joint_menu",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Morph Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_morph_menu",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Rigidbody Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_rigidbody_menu",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体", (True, ())),
    ),
    (
        ("*", "Rigidbody Select Menu"),
        (("bpy.types.OBJECT_MT_mmd_tools_rigidbody_select_menu",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "MMD UuuNyaa"),
        (
            (
                "bpy.types.TOPBAR_MT_mmd_file_export",
                "bpy.types.TOPBAR_MT_mmd_file_import",
                "bpy.types.VIEW3D_MT_mmd_armature_add",
                "bpy.types.VIEW3D_MT_mmd_object",
                "bpy.types.VIEW3D_MT_mmd_pose",
                "bpy.types.VIEW3D_MT_mmd_select_object",
                "bpy.types.VIEW3D_PT_mmd_shading",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("Operator", "Add Missing Vertex Groups from Bones"),
        (("bpy.types.MMD_TOOLS_OT_add_missing_vertex_groups_from_bones",), ()),
        ("ja_JP", "ボーンから不足している頂点グループを追加", (False, ())),
        ("zh_HANS", "从骨骼中添加缺失的顶点组", (False, ())),
    ),
    (
        ("*", "Add the missing vertex groups to the selected mesh"),
        (("bpy.types.MMD_TOOLS_OT_add_missing_vertex_groups_from_bones",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将丢失的顶点组添加至选中的网格", (False, ())),
    ),
    (
        ("*", "Search in all meshes"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_add_missing_vertex_groups_from_bones.search_in_all_meshes",
            ),
            (),
        ),
        ("ja_JP", "全てのメッシュで検索", (False, ())),
        ("zh_HANS", "在所有网格中搜索", (False, ())),
    ),
    (
        ("*", "Search for vertex groups in all meshes"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_add_missing_vertex_groups_from_bones.search_in_all_meshes",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在所有网格中搜索顶点组", (False, ())),
    ),
    (
        ("Operator", "Apply Additional Transform"),
        (("bpy.types.MMD_TOOLS_OT_apply_additional_transform",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用额外变换", (False, ())),
    ),
    (
        ("*", "Translate appended bones of selected object for Blender"),
        (("bpy.types.MMD_TOOLS_OT_apply_additional_transform",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中物体的附加骨骼转译为适合 Blender 的形式", (False, ())),
    ),
    (
        ("Operator", "Apply Bone Morph"),
        (("bpy.types.MMD_TOOLS_OT_apply_bone_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用骨骼变形", (False, ())),
    ),
    (
        ("*", "Apply current pose to active bone morph"),
        (("bpy.types.MMD_TOOLS_OT_apply_bone_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将当前姿态应用于活动骨骼形变", (False, ())),
    ),
    (
        ("Operator", "Apply Bone Morph Offset"),
        (("bpy.types.MMD_TOOLS_OT_apply_bone_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用骨骼形变偏移", (False, ())),
    ),
    (
        ("*", "Stores the current bone location and rotation into this offset"),
        (("bpy.types.MMD_TOOLS_OT_apply_bone_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将当前的骨骼位置与旋转存入此偏移中", (False, ())),
    ),
    (
        ("Operator", "Apply Material Offset"),
        (("bpy.types.MMD_TOOLS_OT_apply_material_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用材质偏移", (False, ())),
    ),
    (
        (
            "*",
            "Calculates the offsets and apply them, then the temporary material is removed",
        ),
        (("bpy.types.MMD_TOOLS_OT_apply_material_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "计算并应用偏移, 然后移除临时材质", (False, ())),
    ),
    (
        ("Operator", "Apply UV Morph"),
        (("bpy.types.MMD_TOOLS_OT_apply_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用 UV 形变", (False, ())),
    ),
    (
        (
            "*",
            "Calculate the UV offsets of selected vertices and apply to active UV morph",
        ),
        (("bpy.types.MMD_TOOLS_OT_apply_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "计算选中顶点的 UV 偏移并应用至活动的 UV 形变", (False, ())),
    ),
    (
        ("Operator", "Assemble All"),
        (("bpy.types.MMD_TOOLS_OT_assemble_all",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "全部装配", (False, ())),
    ),
    (
        ("Operator", "Attach Meshes to Model"),
        (("bpy.types.MMD_TOOLS_OT_attach_meshes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将网格附加至模型", (False, ())),
    ),
    (
        ("*", "Finds existing meshes and attaches them to the selected MMD model"),
        (("bpy.types.MMD_TOOLS_OT_attach_meshes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "寻找现存的网格, 并将其附加到选中的 MMD 模型上", (False, ())),
    ),
    (
        ("Operator", "Setup Bone Fixed Axis"),
        (("bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup",), ()),
        ("ja_JP", "ボーン修正回転軸を適用", (True, ())),
        ("zh_HANS", "设置骨骼固定轴", (False, ())),
    ),
    (
        ("*", "Setup fixed axis of selected bones"),
        (("bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为选中的骨骼设置固定坐标轴", (False, ())),
    ),
    (
        ("*", "Select type"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup.type",
                "bpy.types.MMD_TOOLS_OT_bone_local_axes_setup.type",
                "bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type",
                "bpy.types.MMD_TOOLS_OT_morph_slider_setup.type",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择类型", (False, ())),
    ),
    (
        ("*", "Disable MMD fixed axis of selected bones"),
        (("bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup.type:'DISABLE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为选中的骨骼禁用MMD固定坐标轴", (False, ())),
    ),
    (
        ("*", "Load"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup.type:'LOAD'",
                "bpy.types.MMD_TOOLS_OT_bone_local_axes_setup.type:'LOAD'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "加载", (False, ())),
    ),
    (
        (
            "*",
            "Load/Enable MMD fixed axis of selected bones from their Y-axis or the only rotatable axis",
        ),
        (("bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup.type:'LOAD'",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "以Y轴或唯一的可旋转轴为选中的骨骼加载或启用MMD固定坐标轴",
            (False, ()),
        ),
    ),
    (
        ("*", "Align bone axes to MMD fixed axis of each bone"),
        (("bpy.types.MMD_TOOLS_OT_bone_fixed_axis_setup.type:'APPLY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼与MMD固定坐标轴对齐", (False, ())),
    ),
    (
        ("Operator", "Move Bone ID to Bottom"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_bottom",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼 ID 移至底部", (False, ())),
    ),
    (
        ("*", "Move active bone to the bottom of bone order"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_bottom",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中的骨骼移动至骨骼排序的底部", (False, ())),
    ),
    (
        ("Operator", "Move Bone ID Down"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_down",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼 ID 下移", (False, ())),
    ),
    (
        ("*", "Move active bone down in bone order"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_down",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中的骨骼在骨骼排序中下移", (False, ())),
    ),
    (
        ("Operator", "Move Bone ID to Top"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_top",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼 ID 移至顶部", (False, ())),
    ),
    (
        ("*", "Move active bone to the top of bone order"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_top",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中的骨骼移动至骨骼排序的顶部", (False, ())),
    ),
    (
        ("Operator", "Move Bone ID Up"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_up",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼 ID 上移", (False, ())),
    ),
    (
        ("*", "Move active bone up in bone order"),
        (("bpy.types.MMD_TOOLS_OT_bone_id_move_up",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中的骨骼在骨骼排序中上移", (False, ())),
    ),
    (
        ("Operator", "Setup Bone Local Axes"),
        (("bpy.types.MMD_TOOLS_OT_bone_local_axes_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "设置骨骼局部轴", (False, ())),
    ),
    (
        ("*", "Setup local axes of each bone"),
        (("bpy.types.MMD_TOOLS_OT_bone_local_axes_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为每根骨骼设置局部坐标轴", (False, ())),
    ),
    (
        ("*", "Disable MMD local axes of selected bones"),
        (("bpy.types.MMD_TOOLS_OT_bone_local_axes_setup.type:'DISABLE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为选中的骨骼禁用MMD局部坐标轴", (False, ())),
    ),
    (
        ("*", "Load/Enable MMD local axes of selected bones from their bone axes"),
        (("bpy.types.MMD_TOOLS_OT_bone_local_axes_setup.type:'LOAD'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "以骨骼轴为选中的骨骼加载或启用MMD局部坐标轴", (False, ())),
    ),
    (
        ("*", "Align bone axes to MMD local axes of each bone"),
        (("bpy.types.MMD_TOOLS_OT_bone_local_axes_setup.type:'APPLY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼与MMD局部坐标轴对齐", (False, ())),
    ),
    (
        ("Operator", "Build Rig"),
        (("bpy.types.MMD_TOOLS_OT_build_rig",), ()),
        ("ja_JP", "ビルドリグ", (False, ())),
        ("zh_HANS", "建立骨架", (False, ())),
    ),
    (
        ("*", "Translate physics of selected object into format usable by Blender"),
        (("bpy.types.MMD_TOOLS_OT_build_rig",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中物体的物理转译为 Blender 可用的形式", (False, ())),
    ),
    (
        (
            "*",
            "The collision margin between rigid bodies. If 0, the default value for each shape is adopted.",
        ),
        (("bpy.types.MMD_TOOLS_OT_build_rig.collision_margin",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体之间的碰撞裕度 若为 0, 则使用每个形状的默认值", (False, ())),
    ),
    (
        ("*", "Non-Collision Distance Scale"),
        (("bpy.types.MMD_TOOLS_OT_build_rig.non_collision_distance_scale",), ()),
        ("ja_JP", "非衝突距離スケール", (False, ())),
        ("zh_HANS", "非碰撞距离比例", (False, ())),
    ),
    (
        (
            "*",
            "The distance scale for creating extra non-collision constraints while building physics",
        ),
        (("bpy.types.MMD_TOOLS_OT_build_rig.non_collision_distance_scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "装配物理时创建的额外非碰撞约束的距离比例", (False, ())),
    ),
    (
        ("Operator", "Change MMD IK Loop Factor"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_change_mmd_ik_loop_factor",
                "extensions/user_default/mmd_tools/panels/prop_object.py:34",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:161",
            ),
            (),
        ),
        ("ja_JP", "MMD IK反復係数を変更", (False, ())),
        ("zh_HANS", "改变MMD IK循环系数", (False, ())),
    ),
    (
        ("*", "Multiplier for all bones' IK iterations in Blender"),
        (("bpy.types.MMD_TOOLS_OT_change_mmd_ik_loop_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "Blender 中所有骨骼的逆向运动学迭代数乘数", (False, ())),
    ),
    (
        ("*", "MMD IK Loop Factor"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_change_mmd_ik_loop_factor.mmd_ik_loop_factor",
                "bpy.types.MMDRoot.ik_loop_factor",
            ),
            (),
        ),
        ("ja_JP", "MMD IK反復係数", (False, ())),
        ("zh_HANS", "MMD IK循环系数", (False, ())),
    ),
    (
        ("*", "Scaling factor of MMD IK loop"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_change_mmd_ik_loop_factor.mmd_ik_loop_factor",
                "bpy.types.MMD_TOOLS_OT_import_model.ik_loop_factor",
                "bpy.types.MMDRoot.ik_loop_factor",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MMD 逆运动学循环比例系数", (False, ())),
    ),
    (
        ("Operator", "Clean Additional Transform"),
        (("bpy.types.MMD_TOOLS_OT_clean_additional_transform",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理额外变换", (False, ())),
    ),
    (
        (
            "*",
            "Delete shadow bones of selected object and revert bones to default MMD state",
        ),
        (("bpy.types.MMD_TOOLS_OT_clean_additional_transform",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除选中物体的影子骨骼并将骨骼复原至默认 MMD 状态", (False, ())),
    ),
    (
        ("Operator", "Clean Duplicated Material Morphs"),
        (("bpy.types.MMD_TOOLS_OT_clean_duplicated_material_morphs",), ()),
        ("ja_JP", "重複マテリアルモーフを掃除", (False, ())),
        ("zh_HANS", "清理重复材质变形", (False, ())),
    ),
    (
        ("*", "Clean duplicated material morphs"),
        (("bpy.types.MMD_TOOLS_OT_clean_duplicated_material_morphs",), ()),
        ("ja_JP", "重複マテリアルモーフを掃除", (False, ())),
        ("zh_HANS", "清理重复的材质变形", (False, ())),
    ),
    (
        ("Operator", "Clean Rig"),
        (("bpy.types.MMD_TOOLS_OT_clean_rig",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理物理", (False, ())),
    ),
    (
        (
            "*",
            "Delete temporary physics objects of selected object and revert physics to default MMD state",
        ),
        (("bpy.types.MMD_TOOLS_OT_clean_rig",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "删除选中物体的临时物理物体并将物理复原为 MMD 默认状态",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Clean Shape Keys"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_clean_shape_keys",
                "extensions/user_default/mmd_tools/menus.py:87",
            ),
            (),
        ),
        ("ja_JP", "シェイプキーをクリーン", (False, ())),
        ("zh_HANS", "清理形态键", (False, ())),
    ),
    (
        ("*", "Remove unused shape keys of selected mesh objects"),
        (("bpy.types.MMD_TOOLS_OT_clean_shape_keys",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除选中网格物体未使用的形态键", (False, ())),
    ),
    (
        ("Operator", "Clear Bone Morph View"),
        (("bpy.types.MMD_TOOLS_OT_clear_bone_morph_view",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理骨骼变形视图", (False, ())),
    ),
    (
        ("*", "Reset transforms of all bones to their default values"),
        (("bpy.types.MMD_TOOLS_OT_clear_bone_morph_view",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将所有骨骼变换重设为默认值", (False, ())),
    ),
    (
        ("Operator", "Clear Temp Materials"),
        (("bpy.types.MMD_TOOLS_OT_clear_temp_materials",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理临时材质", (False, ())),
    ),
    (
        ("*", "Clears all the temporary materials"),
        (("bpy.types.MMD_TOOLS_OT_clear_temp_materials",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除所有临时材质", (False, ())),
    ),
    (
        ("Operator", "Clear UV Morph View"),
        (("bpy.types.MMD_TOOLS_OT_clear_uv_morph_view",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理 UV 变形视图", (False, ())),
    ),
    (
        ("*", "Clear all temporary data of UV morphs"),
        (("bpy.types.MMD_TOOLS_OT_clear_uv_morph_view",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除所有 UV 变形的临时数据", (False, ())),
    ),
    (
        ("Operator", "Convert Blender Materials"),
        (("bpy.types.MMD_TOOLS_OT_convert_bsdf_materials",), ()),
        ("ja_JP", "マテリアルノードを変換", (True, ())),
        ("zh_HANS", "转换 Blender 材质", (False, ())),
    ),
    (
        ("*", "Convert materials of selected objects."),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_bsdf_materials",
                "bpy.types.MMD_TOOLS_OT_convert_materials",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "转换选中物体的材质", (False, ())),
    ),
    (
        ("Operator", "Convert Materials"),
        (("bpy.types.MMD_TOOLS_OT_convert_materials",), ()),
        ("ja_JP", "マテリアルノードを変換", (True, ())),
        ("zh_HANS", "转换材质", (False, ())),
    ),
    (
        ("*", "Clean Nodes"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_materials.clean_nodes",
                "bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles.clean_nodes",
            ),
            (),
        ),
        ("ja_JP", "ノードをクリーン", (False, ())),
        ("zh_HANS", "清理节点", (False, ())),
    ),
    (
        (
            "*",
            "Remove redundant nodes as well if enabled. Disable it to keep node data.",
        ),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_materials.clean_nodes",
                "bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles.clean_nodes",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "启用时，移除冗余的节点 禁用以保留节点数据", (False, ())),
    ),
    (
        ("*", "Subsurface"),
        (("bpy.types.MMD_TOOLS_OT_convert_materials.subsurface",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "次表面", (False, ())),
    ),
    (
        ("*", "Convert to Principled BSDF"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_materials.use_principled",
                "bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles.use_principled",
            ),
            (),
        ),
        ("ja_JP", "プリンシプルBSDFへ変換", (False, ())),
        ("zh_HANS", "转换为原理化BSDF", (False, ())),
    ),
    (
        ("*", "Convert MMD shader nodes to Principled BSDF as well if enabled"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_materials.use_principled",
                "bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles.use_principled",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "启用时，也将MMD着色器节点转换为原理化BSDF", (False, ())),
    ),
    (
        ("Operator", "Convert Materials For Cycles"),
        (("bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles",), ()),
        ("ja_JP", "マテリアルノードを変換", (True, ())),
        ("zh_HANS", "为 Cycles 转换材质", (False, ())),
    ),
    (
        ("*", "Convert materials of selected objects for Cycles."),
        (("bpy.types.MMD_TOOLS_OT_convert_materials_for_cycles",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "转换选中物体的材质以供 Cycles 使用", (False, ())),
    ),
    (
        ("Operator", "Convert to MMD Camera"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera",), ()),
        ("ja_JP", "MMDライトへ変換", (False, ())),
        ("zh_HANS", "转换为MMD摄像机", (False, ())),
    ),
    (
        ("*", "Create a camera rig for MMD"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为 MMD 创建相机机架", (False, ())),
    ),
    (
        ("*", "Bake Animation"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.bake_animation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "烘焙动画", (False, ())),
    ),
    (
        ("*", "Bake camera animation to a new MMD camera rig"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.bake_animation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将相机动画烘培之新的MMD机架", (False, ())),
    ),
    (
        ("*", "Camera Source"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.camera_source",), ()),
        ("ja_JP", "カメラソース", (False, ())),
        ("zh_HANS", "摄像机源", (False, ())),
    ),
    (
        (
            "*",
            "Select camera source to bake animation (camera target is the selected or DoF object)",
        ),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.camera_source",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择烘焙动画使用的摄像机源", (False, ())),
    ),
    (
        ("*", "Current active camera object"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.camera_source:'CURRENT'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "当前活动的相机对象", (False, ())),
    ),
    (
        ("*", "Scene camera object"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.camera_source:'SCENE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "场景相机对象", (False, ())),
    ),
    (
        ("*", "Min Distance"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.min_distance",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "最小距离", (False, ())),
    ),
    (
        ("*", "Minimum distance to camera target when baking animation"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.min_distance",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "烘焙动画时至摄像机源的最小距离", (False, ())),
    ),
    (
        ("*", "Scaling factor for initializing the camera"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_camera.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "初始化相机的比例系数", (False, ())),
    ),
    (
        ("Operator", "Convert to MMD Light"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_lamp",), ()),
        ("ja_JP", "MMDライトへ変換", (False, ())),
        ("zh_HANS", "转换为MMD灯光", (False, ())),
    ),
    (
        ("*", "Create a light rig for MMD"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_lamp",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建 MMD 光源绑定", (False, ())),
    ),
    (
        ("*", "Scaling factor for initializing the light"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_lamp.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "初始化灯光的比例系数", (False, ())),
    ),
    (
        ("Operator", "Convert to a MMD Model"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model",), ()),
        ("ja_JP", "MMDモデルへ変換", (False, ())),
        ("zh_HANS", "转换为MMD模型", (False, ())),
    ),
    (
        ("*", "Convert active armature with its meshes to a MMD model (experimental)"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将活动骨架及其网格转换为 MMD 模型 (实验性)", (False, ())),
    ),
    (
        ("*", "Ambient Color Source"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.ambient_color_source",), ()),
        ("ja_JP", "アンビエントカラーソース", (False, ())),
        ("zh_HANS", "环境色源", (False, ())),
    ),
    (
        ("*", "Select ambient color source"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.ambient_color_source",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择环境色源", (False, ())),
    ),
    (
        ("*", "Diffuse color"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.ambient_color_source:'DIFFUSE'",
                "bpy.types.MMDMaterial.diffuse_color",
                "bpy.types.MaterialMorphData.diffuse_color",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "漫射色", (False, ())),
    ),
    (
        ("*", 'Mirror color (if property "mirror_color" is available)'),
        (
            (
                "bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.ambient_color_source:'MIRROR'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '镜像色 (若"mirror_color"属性可用)', (False, ())),
    ),
    (
        ("*", "Convert Material Nodes"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.convert_material_nodes",), ()),
        ("ja_JP", "マテリアルノードを変換", (False, ())),
        ("zh_HANS", "转换材质节点", (False, ())),
    ),
    (
        ("*", "Minimum Edge Alpha"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.edge_alpha_min",), ()),
        ("ja_JP", "最小輪郭アルファ", (False, ())),
        ("zh_HANS", "最小边缘Alpha", (False, ())),
    ),
    (
        ("*", "Minimum alpha of MMD toon edge color"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.edge_alpha_min",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MMD卡通边缘颜色的最小 Alpha", (False, ())),
    ),
    (
        ("*", "Edge Threshold"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.edge_threshold",), ()),
        ("ja_JP", "輪郭しきい値", (False, ())),
        ("zh_HANS", "边缘阈值", (False, ())),
    ),
    (
        (
            "*",
            "MMD toon edge will not be enabled if freestyle line color alpha less than this value",
        ),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.edge_threshold",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "若 Freestyle 线条颜色小于此值，则MMD卡通边缘不会启用",
            (False, ()),
        ),
    ),
    (
        ("*", "Middle Joint Bones Lock"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.middle_joint_bones_lock",), ()),
        ("ja_JP", "中間関節ボーンをロック", (False, ())),
        ("zh_HANS", "中间关节骨锁定", (False, ())),
    ),
    (
        ("*", "Lock specific bones for backward compatibility."),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.middle_joint_bones_lock",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为向后兼容而锁定指定的骨骼", (False, ())),
    ),
    (
        ("*", "Scaling factor for converting the model"),
        (("bpy.types.MMD_TOOLS_OT_convert_to_mmd_model.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "转换该模型时的缩放比例系数", (False, ())),
    ),
    (
        ("Operator", "Create a MMD Model Root Object"),
        (("bpy.types.MMD_TOOLS_OT_create_mmd_model_root_object",), ()),
        ("ja_JP", "MMDモデルルートオブジェクトを作成", (False, ())),
        ("zh_HANS", "创建一个MMD模型的根物体", (False, ())),
    ),
    (
        ("*", "Create a MMD model root object with a basic armature"),
        (("bpy.types.MMD_TOOLS_OT_create_mmd_model_root_object",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建 MMD 模型根物体以及基本的骨架", (False, ())),
    ),
    (
        ("*", "Name(Eng)"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_create_mmd_model_root_object.name_e",
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.name_e",
                "bpy.types.BoneMorph.name_e",
                "bpy.types.GroupMorph.name_e",
                "bpy.types.MMDBone.name_e",
                "bpy.types.MMDDisplayItemFrame.name_e",
                "bpy.types.MMDJoint.name_e",
                "bpy.types.MMDMaterial.name_e",
                "bpy.types.MMDRigidBody.name_e",
                "bpy.types.MaterialMorph.name_e",
                "bpy.types.UVMorph.name_e",
                "bpy.types.VertexMorph.name_e",
            ),
            (),
        ),
        ("ja_JP", "名前(英語)", (False, ())),
        ("zh_HANS", "名称(英文)", (False, ())),
    ),
    (
        ("*", "The english name of the MMD model"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_create_mmd_model_root_object.name_e",
                "bpy.types.MMDRoot.name_e",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "此MMD模型的英文名", (False, ())),
    ),
    (
        ("*", "The name of the MMD model"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_create_mmd_model_root_object.name_j",
                "bpy.types.MMDRoot.name",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "此MMD模型的名称", (False, ())),
    ),
    (
        ("Operator", "Create Work Material"),
        (("bpy.types.MMD_TOOLS_OT_create_work_material",), ()),
        ("ja_JP", "ワークマテリアルを生成", (False, ())),
        ("zh_HANS", "创建工作材质", (False, ())),
    ),
    (
        ("*", "Creates a temporary material to edit this offset"),
        (("bpy.types.MMD_TOOLS_OT_create_work_material",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建临时材质以编辑此偏移", (False, ())),
    ),
    (
        ("Operator", "Disassemble All"),
        (("bpy.types.MMD_TOOLS_OT_disassemble_all",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "全部拆卸", (False, ())),
    ),
    (
        ("Operator", "Add Display Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_add",), ()),
        ("ja_JP", "表示項目を追加", (False, ())),
        ("zh_HANS", "添加显示项目", (False, ())),
    ),
    (
        ("*", "Add a display item to the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将表示项目添加至列表", (False, ())),
    ),
    (
        ("Operator", "Find Display Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查找表示项目", (False, ())),
    ),
    (
        ("*", "Find the display item of active bone or morph"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查找活动骨骼或形变的表示项目", (False, ())),
    ),
    (
        ("*", "Find type"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find.type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查找类型", (False, ())),
    ),
    (
        ("*", "Find Bone Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find.type:'BONE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查找骨骼项目", (False, ())),
    ),
    (
        ("*", "Find active bone in Display Panel"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find.type:'BONE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在表示面板中查找活动的骨骼", (False, ())),
    ),
    (
        ("*", "Find Morph Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find.type:'MORPH'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查找变形项目", (False, ())),
    ),
    (
        ("*", "Find active morph of Morph Tools Panel in Display Panel"),
        (("bpy.types.MMD_TOOLS_OT_display_item_find.type:'MORPH'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在表示面板中查找变形面板中活动的变形", (False, ())),
    ),
    (
        ("Operator", "Add Display Item Frame"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_add",), ()),
        ("ja_JP", "表示項目フレームを追加", (False, ())),
        ("zh_HANS", "添加显示项目帧", (False, ())),
    ),
    (
        ("*", "Add a display item frame to the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向列表添加表示枠", (False, ())),
    ),
    (
        ("Operator", "Move Display Item Frame"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_move",), ()),
        ("ja_JP", "表示項目フレームを移動", (False, ())),
        ("zh_HANS", "移动显示项目帧", (False, ())),
    ),
    (
        ("*", "Move active display item frame up/down in the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_move",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将活动的表示枠上移或下移", (False, ())),
    ),
    (
        ("*", "Move type"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_display_item_frame_move.type",
                "bpy.types.MMD_TOOLS_OT_display_item_move.type",
                "bpy.types.MMD_TOOLS_OT_morph_move.type",
                "bpy.types.MMD_TOOLS_OT_object_move.type",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移动类型", (False, ())),
    ),
    (
        ("Operator", "Remove Display Item Frame"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_remove",), ()),
        ("ja_JP", "表示項目フレームを削除", (False, ())),
        ("zh_HANS", "移除显示项目帧", (False, ())),
    ),
    (
        ("*", "Remove active display item frame from the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_frame_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从列表中移除活动的表示枠", (False, ())),
    ),
    (
        ("Operator", "Move Display Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_move",), ()),
        ("ja_JP", "表示項目を移動", (False, ())),
        ("zh_HANS", "移动显示项目", (False, ())),
    ),
    (
        ("*", "Move active display item up/dowm in the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_move",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将活动的表示上移或下移", (False, ())),
    ),
    (
        ("Operator", "Display Item Quick Setup"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "表示快速设置", (False, ())),
    ),
    (
        ("*", "Quick setup display items"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup",), ()),
        ("ja_JP", "表示項目のクイックセットアップ", (False, ())),
        ("zh_HANS", "快速设置显示项目", (False, ())),
    ),
    (
        ("*", "Clear all items and frames, reset to default"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'RESET'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理所有项目和框架并重设至默认", (False, ())),
    ),
    (
        ("*", "Load Facial Items"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'FACIAL'",), ()),
        ("ja_JP", "表情項目をロード", (False, ())),
        ("zh_HANS", "载入面部项目", (False, ())),
    ),
    (
        ("*", "Load all morphs to faical frame"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'FACIAL'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在面部框架载入所有变形", (False, ())),
    ),
    (
        ("*", "Sync from Bone Collections"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'GROUP_LOAD'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从骨骼集合同步", (False, ())),
    ),
    (
        ("*", "Sync armature's bone collections to display item frames"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'GROUP_LOAD'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨架的骨骼集合同步至表示枠中", (False, ())),
    ),
    (
        ("*", "Sync to Bone Collections"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'GROUP_APPLY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "同步至骨骼集合", (False, ())),
    ),
    (
        ("*", "Sync display item frames to armature's bone collections"),
        (("bpy.types.MMD_TOOLS_OT_display_item_quick_setup.type:'GROUP_APPLY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将表示枠同步至骨骼集合", (False, ())),
    ),
    (
        ("Operator", "Remove Display Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_remove",), ()),
        ("ja_JP", "表示項目を削除", (False, ())),
        ("zh_HANS", "移除显示项目", (False, ())),
    ),
    (
        ("*", "Remove display item(s) from the list"),
        (("bpy.types.MMD_TOOLS_OT_display_item_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从列表中移除表示", (False, ())),
    ),
    (
        ("*", "Delete all display items"),
        (("bpy.types.MMD_TOOLS_OT_display_item_remove.all",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除所有表示", (False, ())),
    ),
    (
        ("Operator", "Select Current Display Item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_select_current",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选中当前表示", (False, ())),
    ),
    (
        ("*", "Select the bone or morph assigned to the display item"),
        (("bpy.types.MMD_TOOLS_OT_display_item_select_current",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选中表示关联的骨骼或变形", (False, ())),
    ),
    (
        ("Operator", "Edge Preview Setup"),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup",), ()),
        ("ja_JP", "輪郭プレビュー", (True, ())),
        ("zh_HANS", "边缘预览设置", (False, ())),
    ),
    (
        ("*", 'Preview toon edge settings of active model using "Solidify" modifier'),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '预览利用"实体化"修改器为活动模型生成的卡通边缘设置', (False, ())),
    ),
    (
        ("*", "Select action"),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup.action",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择行动", (False, ())),
    ),
    (
        ("*", "Create"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_edge_preview_setup.action:'CREATE'",
                "bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'CREATE'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建", (False, ())),
    ),
    (
        ("*", "Create toon edge"),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup.action:'CREATE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建卡通边缘", (False, ())),
    ),
    (
        ("*", "Clean"),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup.action:'CLEAN'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清理", (False, ())),
    ),
    (
        ("*", "Clear toon edge"),
        (("bpy.types.MMD_TOOLS_OT_edge_preview_setup.action:'CLEAN'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除卡通边缘", (False, ())),
    ),
    (
        ("Operator", "Edit Related Bone"),
        (("bpy.types.MMD_TOOLS_OT_edit_bone_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "编辑相关骨骼", (False, ())),
    ),
    (
        ("*", "Applies the location and rotation of this offset to the bone"),
        (("bpy.types.MMD_TOOLS_OT_edit_bone_morph_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将该偏移的位置与旋转应用至骨骼", (False, ())),
    ),
    (
        ("Operator", "Edit UV Morph"),
        (("bpy.types.MMD_TOOLS_OT_edit_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "编辑 UV 形变", (False, ())),
    ),
    (
        (
            "*",
            "Edit UV morph on a temporary UV layer (use UV Editor to edit the result)",
        ),
        (("bpy.types.MMD_TOOLS_OT_edit_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "在临时 UV 图层上编辑 UV 形变 (使用 UV 编辑器来编辑结果)",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Execute Translation Batch"),
        (("bpy.types.MMD_TOOLS_OT_execute_translation_batch",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "执行批量翻译", (False, ())),
    ),
    (
        ("Operator", "Export PMX File (.pmx)"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出 PMX 文件 (.pmx)", (False, ())),
    ),
    (
        ("*", "Export selected MMD model(s) to PMX file(s) (.pmx)"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中的 MMD 模型导出至 PMX 文件 (.pmx)", (False, ())),
    ),
    (
        ("*", "Copy textures"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.copy_textures",
                "bpy.types.MMD_TOOLS_OT_export_pmx.copy_textures",
            ),
            (),
        ),
        ("ja_JP", "テクスチャをコピー", (False, ())),
        ("zh_HANS", "复制纹理", (False, ())),
    ),
    (
        ("*", "Disable SPH/SPA"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.disable_specular",), ()),
        ("ja_JP", "SPH/SPAを無効化", (False, ())),
        ("zh_HANS", "禁用SPH/SPA", (False, ())),
    ),
    (
        (
            "*",
            "Disables all the Specular Map textures. It is required for some MME Shaders.",
        ),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.disable_specular",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "禁用所有高光材质 部分 MME 着色器必须禁用高光材质", (False, ())),
    ),
    (
        ("*", "Log level"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level",
            ),
            (),
        ),
        ("ja_JP", "ログレベル", (False, ())),
        ("zh_HANS", "日志级别", (False, ())),
    ),
    (
        ("*", "Select log level"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择日志级别", (False, ())),
    ),
    (
        ("*", "4. DEBUG"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level:'DEBUG'",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level:'DEBUG'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "4. DEBUG", (False, ())),
    ),
    (
        ("*", "3. INFO"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level:'INFO'",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level:'INFO'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "3. INFO", (False, ())),
    ),
    (
        ("*", "2. WARNING"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level:'WARNING'",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level:'WARNING'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "2. WARNING", (False, ())),
    ),
    (
        ("*", "1. ERROR"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.log_level:'ERROR'",
                "bpy.types.MMD_TOOLS_OT_import_model.log_level:'ERROR'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "1. ERROR", (False, ())),
    ),
    (
        ("*", "Overwrite Bone Morphs"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.overwrite_bone_morphs_from_action_pose",
            ),
            (),
        ),
        ("ja_JP", "ボーンモーフを上書き", (False, ())),
        ("zh_HANS", "覆盖骨骼变形", (False, ())),
    ),
    (
        ("*", "Overwrite the bone morphs from active Action Pose before exporting."),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.overwrite_bone_morphs_from_action_pose",
            ),
            (),
        ),
        (
            "ja_JP",
            "エクスポート前にボーンモーフをアクティブなポーズライブラリから上書きします",
            (True, ()),
        ),
        ("zh_HANS", "在导出前以活动姿态覆盖骨骼变形", (False, ())),
    ),
    (
        ("*", "Create a log file"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_pmx.save_log",
                "bpy.types.MMD_TOOLS_OT_export_pmx.save_log",
                "bpy.types.MMD_TOOLS_OT_import_model.save_log",
                "bpy.types.MMD_TOOLS_OT_import_model.save_log",
            ),
            (),
        ),
        ("ja_JP", "ログファイルを作成", (False, ())),
        ("zh_HANS", "创建一个日志文件", (False, ())),
    ),
    (
        ("*", "Scaling factor for exporting the model"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出模型时的缩放比例系数", (False, ())),
    ),
    (
        ("*", "Sort Materials"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_materials",), ()),
        ("ja_JP", "マテリアルをソート", (False, ())),
        ("zh_HANS", "排列材质", (False, ())),
    ),
    (
        (
            "*",
            "Sort materials for alpha blending. WARNING: Will not work if you have transparent meshes inside the model. E.g. blush meshes",
        ),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_materials",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "将材质排序以用于 Alpha 混合 警告: 若透明材质的网格在同一模型中则无效 例如腮红的网格",
            (False, ()),
        ),
    ),
    (
        ("*", "Sort Vertices"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_vertices",), ()),
        ("ja_JP", "頂点をソート", (False, ())),
        ("zh_HANS", "排列顶点", (False, ())),
    ),
    (
        ("*", "Choose the method to sort vertices"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_vertices",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择排列顶点的方式", (False, ())),
    ),
    (
        ("*", "No sorting"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_vertices:'NONE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "无排序", (False, ())),
    ),
    (
        ("*", "Use blender's internal vertex order"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_vertices:'BLENDER'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用 Blender 内部的顶点序", (False, ())),
    ),
    (
        ("*", 'Use custom vertex weight of vertex group "mmd_vertex_order"'),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.sort_vertices:'CUSTOM'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '使用"mmd_vertex_order"顶点组的自定义顶点权重', (False, ())),
    ),
    (
        ("*", "(Experimental) Translate in Presets"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.translate_in_presets",), ()),
        ("ja_JP", "(実験的) プリセットで翻訳", (False, ())),
        ("zh_HANS", "(实验性) 预设中的翻译", (False, ())),
    ),
    (
        ("*", "Translate in presets before exporting."),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.translate_in_presets",), ()),
        ("ja_JP", "エクスポート前にプリセットで翻訳します", (False, ())),
        ("zh_HANS", "导出前在预设中进行翻译", (False, ())),
    ),
    (
        ("*", "Visible Meshes Only"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.visible_meshes_only",), ()),
        ("ja_JP", "可視メッシュのみ", (False, ())),
        ("zh_HANS", "只有可见的网格", (False, ())),
    ),
    (
        ("*", "Export visible meshes only"),
        (("bpy.types.MMD_TOOLS_OT_export_pmx.visible_meshes_only",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "仅导出可见的网格", (False, ())),
    ),
    (
        ("Operator", "Export VMD File (.vmd)"),
        (("bpy.types.MMD_TOOLS_OT_export_vmd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出 VMD 文件 (.vmd)", (False, ())),
    ),
    (
        ("*", "Export motion data of active object to a VMD file (.vmd)"),
        (("bpy.types.MMD_TOOLS_OT_export_vmd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将活动物体的运动数据导出至 VMD 文件 (.vmd)", (False, ())),
    ),
    (
        ("*", "Scaling factor for exporting the motion"),
        (("bpy.types.MMD_TOOLS_OT_export_vmd.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出动作的缩放比例系数", (False, ())),
    ),
    (
        ("*", "Use Frame Range"),
        (("bpy.types.MMD_TOOLS_OT_export_vmd.use_frame_range",), ()),
        ("ja_JP", "フレーム範囲を使用", (False, ())),
        ("zh_HANS", "使用帧范围", (False, ())),
    ),
    (
        ("*", "Export frames only in the frame range of context scene"),
        (("bpy.types.MMD_TOOLS_OT_export_vmd.use_frame_range",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "仅导出当前上下文场景中帧范围中的帧", (False, ())),
    ),
    (
        ("*", "Treat Current Pose as Rest Pose"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_export_vmd.use_pose_mode",
                "bpy.types.MMD_TOOLS_OT_export_vpd.use_pose_mode",
                "bpy.types.MMD_TOOLS_OT_import_vmd.use_pose_mode",
                "bpy.types.MMD_TOOLS_OT_import_vpd.use_pose_mode",
            ),
            (),
        ),
        ("ja_JP", "現在のポーズをレストポーズとして処理", (False, ())),
        ("zh_HANS", "把当前的姿态当作静置姿态", (False, ())),
    ),
    (
        (
            "*",
            "You can pose the model to export a motion data to different pose base, such as T-Pose or A-Pose",
        ),
        (("bpy.types.MMD_TOOLS_OT_export_vmd.use_pose_mode",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "可设置模型的姿态以将动作数据导出至不同的基础姿态，如T字姿态或A字姿态",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Export VPD File (.vpd)"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出 VPD 文件 (.vpd)", (False, ())),
    ),
    (
        ("*", "Export active rig's Action Pose to VPD file(s) (.vpd)"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出活动骨架的动作姿态至 VPD 文件 (.vpd)", (False, ())),
    ),
    (
        ("*", "Pose Type"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "姿态类型", (False, ())),
    ),
    (
        ("*", "Choose the pose type to export"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择导出的姿态类型", (False, ())),
    ),
    (
        ("*", "Current Pose"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'CURRENT'",), ()),
        ("ja_JP", "現在のポーズ", (False, ())),
        ("zh_HANS", "当前的姿态", (False, ())),
    ),
    (
        ("*", "Current pose of the rig"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'CURRENT'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨架的当前姿态", (False, ())),
    ),
    (
        ("*", "Active Pose"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'ACTIVE'",), ()),
        ("ja_JP", "アクティブなポーズ", (False, ())),
        ("zh_HANS", "活动的姿态", (False, ())),
    ),
    (
        ("*", "Active pose of the rig's Action Pose"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'ACTIVE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "当前骨架动作姿态的活动姿态", (False, ())),
    ),
    (
        ("*", "All Poses"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'ALL'",), ()),
        ("ja_JP", "全てのポーズ", (False, ())),
        ("zh_HANS", "全部姿态", (False, ())),
    ),
    (
        (
            "*",
            "All poses of the rig's Action Pose (the pose name will be the file name)",
        ),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.pose_type:'ALL'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "当前骨架动作姿态的所有姿态 (文件名与姿态名相同)", (False, ())),
    ),
    (
        ("*", "Scaling factor for exporting the pose"),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导出姿态的缩放比例系数", (False, ())),
    ),
    (
        (
            "*",
            "You can pose the model to export a pose data to different pose base, such as T-Pose or A-Pose",
        ),
        (("bpy.types.MMD_TOOLS_OT_export_vpd.use_pose_mode",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "可设置模型的姿态以将姿态数据导出至不同的基础姿态，如T字姿态或A字姿态",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Realign Bone IDs"),
        (("bpy.types.MMD_TOOLS_OT_fix_bone_order",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重新对齐骨骼 ID", (False, ())),
    ),
    (
        (
            "*",
            "Realign bone IDs to be sequential without gaps and apply additional transforms",
        ),
        (("bpy.types.MMD_TOOLS_OT_fix_bone_order",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将骨骼 ID 重新对其为连续的序列并应用额外的变换", (False, ())),
    ),
    (
        ("Operator", "Flip Pose"),
        (("bpy.types.MMD_TOOLS_OT_flip_pose",), ()),
        ("ja_JP", "MMDポーズを反転", (True, ())),
        ("zh_HANS", "翻转姿态", (False, ())),
    ),
    (
        (
            "*",
            "Apply the current pose of selected bones to matching bone on opposite side of X-Axis.",
        ),
        (("bpy.types.MMD_TOOLS_OT_flip_pose",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中骨骼的当前姿态应用于X轴对称的骨骼", (False, ())),
    ),
    (
        ("Operator", "Global Translation Popup"),
        (("bpy.types.MMD_TOOLS_OT_global_translation_popup",), ()),
        ("ja_JP", "全体翻訳ポップアップ", (False, ())),
        ("zh_HANS", "全局翻译弹窗", (False, ())),
    ),
    (
        ("Operator", "Import Model File (.pmd, .pmx)"),
        (("bpy.types.MMD_TOOLS_OT_import_model",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入模型文件 (.pmd, .pmx)", (False, ())),
    ),
    (
        ("*", "Import model file(s) (.pmd, .pmx)"),
        (("bpy.types.MMD_TOOLS_OT_import_model",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入模型文件 (.pmd, .pmx)", (False, ())),
    ),
    (
        ("*", "Apply Bone Fixed Axis"),
        (("bpy.types.MMD_TOOLS_OT_import_model.apply_bone_fixed_axis",), ()),
        ("ja_JP", "ボーン修正回転軸を適用", (False, ())),
        ("zh_HANS", "应用骨骼固定轴", (False, ())),
    ),
    (
        ("*", "Apply bone's fixed axis to be blender suitable"),
        (("bpy.types.MMD_TOOLS_OT_import_model.apply_bone_fixed_axis",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用骨骼的固定坐标轴，使其适合被 Blender 处理", (False, ())),
    ),
    (
        ("*", "Clean Model"),
        (("bpy.types.MMD_TOOLS_OT_import_model.clean_model",), ()),
        ("ja_JP", "モデルをクリーン", (False, ())),
        ("zh_HANS", "清空模型", (False, ())),
    ),
    (
        ("*", "Remove unused vertices and duplicated/invalid faces"),
        (("bpy.types.MMD_TOOLS_OT_import_model.clean_model",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移除未使用的顶点和重复的或无效的面", (False, ())),
    ),
    (
        ("*", "Rename Bones To English"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.dictionary",
                "bpy.types.MMD_TOOLS_OT_import_vmd.dictionary",
                "bpy.types.MMD_TOOLS_OT_import_vpd.dictionary",
            ),
            (),
        ),
        ("ja_JP", "ボーンを英語にリネーム", (False, ())),
        ("zh_HANS", "将骨骼重命名为英文", (False, ())),
    ),
    (
        (
            "*",
            "Translate bone names from Japanese to English using selected dictionary",
        ),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.dictionary",
                "bpy.types.MMD_TOOLS_OT_import_vmd.dictionary",
                "bpy.types.MMD_TOOLS_OT_import_vpd.dictionary",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用选中的词典将骨骼名从日语翻译为英语", (False, ())),
    ),
    (
        ("*", "Fix IK Links"),
        (("bpy.types.MMD_TOOLS_OT_import_model.fix_IK_links",), ()),
        ("ja_JP", "IKリンクを修正", (False, ())),
        ("zh_HANS", "修复IK关联", (False, ())),
    ),
    (
        ("*", "Fix IK links to be blender suitable"),
        (("bpy.types.MMD_TOOLS_OT_import_model.fix_IK_links",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "修复逆向运动学关联，使其适合被 Blender 处理", (False, ())),
    ),
    (
        ("*", "IK Loop Factor"),
        (("bpy.types.MMD_TOOLS_OT_import_model.ik_loop_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "逆向运动学循环系数", (False, ())),
    ),
    (
        ("*", "Remove Doubles"),
        (("bpy.types.MMD_TOOLS_OT_import_model.remove_doubles",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "去重", (False, ())),
    ),
    (
        ("*", "Merge duplicated vertices and faces"),
        (("bpy.types.MMD_TOOLS_OT_import_model.remove_doubles",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "合并重复的顶点和面", (False, ())),
    ),
    (
        ("*", "Rename Bones - L / R Suffix"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.rename_bones",
                "bpy.types.MMD_TOOLS_OT_import_vmd.rename_bones",
                "bpy.types.MMD_TOOLS_OT_import_vpd.rename_bones",
            ),
            (),
        ),
        ("ja_JP", "ボーンをリネーム - L / R接尾辞", (False, ())),
        ("zh_HANS", "将骨骼重命名 - L / R后缀", (False, ())),
    ),
    (
        ("*", "Use Blender naming conventions for Left / Right paired bones"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.rename_bones",
                "bpy.types.MMD_TOOLS_OT_import_vmd.rename_bones",
                "bpy.types.MMD_TOOLS_OT_import_vpd.rename_bones",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为左右成对的骨骼应用 Blender 命名约定", (False, ())),
    ),
    (
        ("*", "Scaling factor for importing the model"),
        (("bpy.types.MMD_TOOLS_OT_import_model.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入模型的缩放比例系数", (False, ())),
    ),
    (
        ("*", "influence of .spa textures"),
        (("bpy.types.MMD_TOOLS_OT_import_model.spa_blend_factor",), ()),
        ("ja_JP", ".spaテクスチャの影響度", (False, ())),
        ("zh_HANS", ".spa纹理的影响", (False, ())),
    ),
    (
        ("*", "The diffuse color factor of texture slot for .spa textures"),
        (("bpy.types.MMD_TOOLS_OT_import_model.spa_blend_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质槽 .spa 纹理的漫射色系数", (False, ())),
    ),
    (
        ("*", "influence of .sph textures"),
        (("bpy.types.MMD_TOOLS_OT_import_model.sph_blend_factor",), ()),
        ("ja_JP", ".sphテクスチャの影響度", (False, ())),
        ("zh_HANS", ".sph纹理的影响", (False, ())),
    ),
    (
        ("*", "The diffuse color factor of texture slot for .sph textures"),
        (("bpy.types.MMD_TOOLS_OT_import_model.sph_blend_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质槽 .sph 纹理的漫射色系数", (False, ())),
    ),
    (
        ("*", "Types"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.types",
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types",
            ),
            (),
        ),
        ("ja_JP", "タイプ", (False, ())),
        ("zh_HANS", "类型", (False, ())),
    ),
    (
        ("*", "Select which parts will be imported"),
        (("bpy.types.MMD_TOOLS_OT_import_model.types",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择待导入的部分", (False, ())),
    ),
    (
        ("*", "Rigidbodies and joints (include Armature)"),
        (("bpy.types.MMD_TOOLS_OT_import_model.types:'PHYSICS'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体与关节 (含骨架)", (False, ())),
    ),
    (
        ("*", "Display frames (include Armature)"),
        (("bpy.types.MMD_TOOLS_OT_import_model.types:'DISPLAY'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "表示枠 (含骨架)", (False, ())),
    ),
    (
        ("*", "Morphs"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.types:'MORPHS'",
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types:'MORPH'",
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types:'MORPH'",
                "bpy.types.MMDTranslation.filter_types:'MORPH'",
                "bpy.types.MMDTranslation.filter_types:'MORPH'",
                "bpy.types.MMDTranslationElement.type:'MORPH'",
                "bpy.types.MMDTranslationElement.type:'MORPH'",
            ),
            (),
        ),
        ("ja_JP", "モーフ", (False, ())),
        ("zh_HANS", "变形", (False, ())),
    ),
    (
        ("*", "Morphs (include Armature and Mesh)"),
        (("bpy.types.MMD_TOOLS_OT_import_model.types:'MORPHS'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "变形 (含骨架与网格)", (False, ())),
    ),
    (
        ("*", "use MIP maps for UV textures"),
        (("bpy.types.MMD_TOOLS_OT_import_model.use_mipmap",), ()),
        ("ja_JP", "UVテクスチャへミップマップを使用", (False, ())),
        ("zh_HANS", "为UV纹理使用多级纹理", (False, ())),
    ),
    (
        ("*", "Specify if mipmaps will be generated"),
        (("bpy.types.MMD_TOOLS_OT_import_model.use_mipmap",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "是否生成多级纹理", (False, ())),
    ),
    (
        ("*", "Rename Bones - Use Underscore"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.use_underscore",
                "bpy.types.MMD_TOOLS_OT_import_vmd.use_underscore",
                "bpy.types.MMD_TOOLS_OT_import_vpd.use_underscore",
            ),
            (),
        ),
        ("ja_JP", "ボーンをリネーム - アンダースコア使用", (False, ())),
        ("zh_HANS", "将骨骼重命名 - 使用下划线", (False, ())),
    ),
    (
        ("*", "Will not use dot, e.g. if renaming bones, will use _R instead of .R"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_model.use_underscore",
                "bpy.types.MMD_TOOLS_OT_import_vmd.use_underscore",
                "bpy.types.MMD_TOOLS_OT_import_vpd.use_underscore",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "不使用点. 例如，重命名骨骼时使用 _R 而非 .R", (False, ())),
    ),
    (
        ("Operator", "Import VMD File (.vmd)"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入 VMD 文件 (.vmd)", (False, ())),
    ),
    (
        ("*", "Import a VMD file to selected objects (.vmd)"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向选中物体导入 VMD 文件 (.vmd)", (False, ())),
    ),
    (
        ("*", "Bone Mapper"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper",
                "bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper",
            ),
            (),
        ),
        ("ja_JP", "ボーンマッパー", (False, ())),
        ("zh_HANS", "骨骼映射器", (False, ())),
    ),
    (
        ("*", "Select bone mapper"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper",
                "bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择骨骼映射器", (False, ())),
    ),
    (
        ("*", "Use blender bone name"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper:'BLENDER'",
                "bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper:'BLENDER'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用 Blender 骨骼名", (False, ())),
    ),
    (
        ("*", "Use japanese name of MMD bone"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper:'PMX'",
                "bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper:'PMX'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用 MMD 骨骼的日语名", (False, ())),
    ),
    (
        ("*", "Renamed bones"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper:'RENAMED_BONES'",
                "bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper:'RENAMED_BONES'",
            ),
            (),
        ),
        ("ja_JP", "リネームしたボーン", (False, ())),
        ("zh_HANS", "重命名的骨骼", (False, ())),
    ),
    (
        ("*", "Rename the bone of motion data to be blender suitable"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.bone_mapper:'RENAMED_BONES'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重命名动作数据中的骨骼，使其适于被 Blender 处理", (False, ())),
    ),
    (
        ("*", "How many frames added before motion starting"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.margin",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "动作开始前的帧数", (False, ())),
    ),
    (
        ("*", "Scaling factor for importing the motion"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入运动的缩放比例系数", (False, ())),
    ),
    (
        ("*", "Update scene settings"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.update_scene_settings",), ()),
        ("ja_JP", "シーン設定を更新", (False, ())),
        ("zh_HANS", "更新场景设置", (False, ())),
    ),
    (
        ("*", "Update frame range and frame rate (30 fps)"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.update_scene_settings",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "更新帧范围与帧率 (30 FPS)", (False, ())),
    ),
    (
        ("*", "Use NLA"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.use_NLA",), ()),
        ("ja_JP", "NLAを使用", (False, ())),
        ("zh_HANS", "使用NLA", (False, ())),
    ),
    (
        ("*", "Import the motion as NLA strips"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.use_NLA",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将动作导入为非线性动作片段", (False, ())),
    ),
    (
        ("*", "Mirror Motion"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.use_mirror",), ()),
        ("ja_JP", "モーションをミラー", (False, ())),
        ("zh_HANS", "镜像运动", (False, ())),
    ),
    (
        ("*", "Import the motion by using X-Axis mirror"),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.use_mirror",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入动作时使用X轴镜像", (False, ())),
    ),
    (
        (
            "*",
            "You can pose the model to fit the original pose of a motion data, such as T-Pose or A-Pose",
        ),
        (("bpy.types.MMD_TOOLS_OT_import_vmd.use_pose_mode",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "可将设置模型的姿态，使其符合动作数据的原始姿态，如T字姿态或A字姿态",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Import VPD File (.vpd)"),
        (("bpy.types.MMD_TOOLS_OT_import_vpd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入 VPD 文件 (.vpd)", (False, ())),
    ),
    (
        ("*", "Import VPD file(s) to selected rig's Action Pose (.vpd)"),
        (("bpy.types.MMD_TOOLS_OT_import_vpd",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向选中骨架的动作姿态导入 VPD 文件 (.vpd)", (False, ())),
    ),
    (
        ("*", "Rename the bone of pose data to be blender suitable"),
        (("bpy.types.MMD_TOOLS_OT_import_vpd.bone_mapper:'RENAMED_BONES'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重命名姿态数据中的骨骼，使其适于被 Blender 处理", (False, ())),
    ),
    (
        ("*", "Scaling factor for importing the pose"),
        (("bpy.types.MMD_TOOLS_OT_import_vpd.scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "导入姿态的缩放比例", (False, ())),
    ),
    (
        (
            "*",
            "You can pose the model to fit the original pose of a pose data, such as T-Pose or A-Pose",
        ),
        (("bpy.types.MMD_TOOLS_OT_import_vpd.use_pose_mode",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "可将设置模型的姿态，使其符合姿态数据的原始姿态，如T字姿态或A字姿态",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Join Meshes"),
        (("bpy.types.MMD_TOOLS_OT_join_meshes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "接合网格", (False, ())),
    ),
    (
        ("*", "Join the Model meshes into a single one"),
        (("bpy.types.MMD_TOOLS_OT_join_meshes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将模型的网格结合为单个网格", (False, ())),
    ),
    (
        ("*", "Sort Shape Keys"),
        (("bpy.types.MMD_TOOLS_OT_join_meshes.sort_shape_keys",), ()),
        ("ja_JP", "シェイプキーをソート", (False, ())),
        ("zh_HANS", "排列形态键", (False, ())),
    ),
    (
        ("*", "Sort shape keys in the order of vertex morph"),
        (("bpy.types.MMD_TOOLS_OT_join_meshes.sort_shape_keys",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "按顶点变形顺序排列形态键", (False, ())),
    ),
    (
        ("Operator", "Add Joint"),
        (("bpy.types.MMD_TOOLS_OT_joint_add",), ()),
        ("ja_JP", "ジョイントを追加", (False, ())),
        ("zh_HANS", "添加关节", (False, ())),
    ),
    (
        ("*", "Add Joint(s) to selected rigidbody objects"),
        (("bpy.types.MMD_TOOLS_OT_joint_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向选中刚体物体添加关节", (False, ())),
    ),
    (
        ("*", "Limit Angular Lower"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_angular_lower",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "角限制最小值", (False, ())),
    ),
    (
        ("*", "Lower limit of rotation"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_angular_lower",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "旋转角度的下限", (False, ())),
    ),
    (
        ("*", "Limit Angular Upper"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_angular_upper",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "角限制最大值", (False, ())),
    ),
    (
        ("*", "Upper limit of rotation"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_angular_upper",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "旋转角度的上限", (False, ())),
    ),
    (
        ("*", "Limit Linear Lower"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_linear_lower",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "线限制最小值", (False, ())),
    ),
    (
        ("*", "Lower limit of translation"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_linear_lower",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "平移距离的下限", (False, ())),
    ),
    (
        ("*", "Limit Linear Upper"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_linear_upper",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "线限制最大值", (False, ())),
    ),
    (
        ("*", "Upper limit of translation"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.limit_linear_upper",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "平移距离的上限", (False, ())),
    ),
    (
        ("*", "Spring(Angular)"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_joint_add.spring_angular",
                "bpy.types.MMDJoint.spring_angular",
            ),
            (),
        ),
        ("ja_JP", "スプリング(角度)", (False, ())),
        ("zh_HANS", "弹簧(棱角)", (False, ())),
    ),
    (
        ("*", "Spring constant of rotation"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_joint_add.spring_angular",
                "bpy.types.MMDJoint.spring_angular",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "扭转弹簧的弹簧常数", (False, ())),
    ),
    (
        ("*", "Spring(Linear)"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_joint_add.spring_linear",
                "bpy.types.MMDJoint.spring_linear",
            ),
            (),
        ),
        ("ja_JP", "スプリング(リニア)", (False, ())),
        ("zh_HANS", "弹簧(线性)", (False, ())),
    ),
    (
        ("*", "Spring constant of movement"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_joint_add.spring_linear",
                "bpy.types.MMDJoint.spring_linear",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "线性弹簧的弹簧常数", (False, ())),
    ),
    (
        ("*", "Use Bone Rotation"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.use_bone_rotation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用骨骼旋转", (False, ())),
    ),
    (
        ("*", "Match joint orientation to bone orientation if enabled"),
        (("bpy.types.MMD_TOOLS_OT_joint_add.use_bone_rotation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "启用时，将关节的朝向与骨骼的朝向匹配", (False, ())),
    ),
    (
        ("Operator", "Remove Joint"),
        (("bpy.types.MMD_TOOLS_OT_joint_remove",), ()),
        ("ja_JP", "ジョイントを削除", (False, ())),
        ("zh_HANS", "移除关节", (False, ())),
    ),
    (
        ("*", "Deletes the currently selected Joint"),
        (("bpy.types.MMD_TOOLS_OT_joint_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除当前选中的关节", (False, ())),
    ),
    (
        ("Operator", "Init Material Offset"),
        (("bpy.types.MMD_TOOLS_OT_material_morph_offset_init",), ()),
        ("ja_JP", "マテリアルオフセットを初期化", (False, ())),
        ("zh_HANS", "初始化材质偏移", (False, ())),
    ),
    (
        ("*", "Set all offset values to target value"),
        (("bpy.types.MMD_TOOLS_OT_material_morph_offset_init",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将所有偏移值设置为目标值", (False, ())),
    ),
    (
        ("*", "Target Value"),
        (("bpy.types.MMD_TOOLS_OT_material_morph_offset_init.target_value",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "目标值", (False, ())),
    ),
    (
        ("*", "Target value"),
        (("bpy.types.MMD_TOOLS_OT_material_morph_offset_init.target_value",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "目标值", (False, ())),
    ),
    (
        ("Operator", "Open Sphere Texture"),
        (("bpy.types.MMD_TOOLS_OT_material_open_sphere_texture",), ()),
        ("ja_JP", "スフィアテクスチャ", (True, ())),
        ("zh_HANS", "打开球纹理", (False, ())),
    ),
    (
        ("*", "Create sphere texture of active material"),
        (("bpy.types.MMD_TOOLS_OT_material_open_sphere_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为活动的材质创建球球面纹理", (False, ())),
    ),
    (
        ("Operator", "Open Texture"),
        (("bpy.types.MMD_TOOLS_OT_material_open_texture",), ()),
        ("ja_JP", "スフィアテクスチャ", (True, ())),
        ("zh_HANS", "打开纹理", (False, ())),
    ),
    (
        ("*", "Create main texture of active material"),
        (("bpy.types.MMD_TOOLS_OT_material_open_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为活动的材质创建主纹理", (False, ())),
    ),
    (
        ("Operator", "Remove Sphere Texture"),
        (("bpy.types.MMD_TOOLS_OT_material_remove_sphere_texture",), ()),
        ("ja_JP", "スフィアテクスチャ", (True, ())),
        ("zh_HANS", "移除球纹理", (False, ())),
    ),
    (
        ("*", "Remove sphere texture of active material"),
        (("bpy.types.MMD_TOOLS_OT_material_remove_sphere_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移除活动材质的球面纹理", (False, ())),
    ),
    (
        ("Operator", "Remove Texture"),
        (("bpy.types.MMD_TOOLS_OT_material_remove_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移除纹理", (False, ())),
    ),
    (
        ("*", "Remove main texture of active material"),
        (("bpy.types.MMD_TOOLS_OT_material_remove_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移除活动材质的主纹理", (False, ())),
    ),
    (
        ("Operator", "Model Join by Bones"),
        (("bpy.types.MMD_TOOLS_OT_model_join_by_bones",), ()),
        ("ja_JP", "モデルをボーンで統合", (False, ())),
        ("zh_HANS", "用骨骼合并模型", (False, ())),
    ),
    (
        ("*", "Join Type"),
        (("bpy.types.MMD_TOOLS_OT_model_join_by_bones.join_type",), ()),
        ("ja_JP", "接続タイプ", (False, ())),
        ("zh_HANS", "接合类型", (False, ())),
    ),
    (
        ("Operator", "Model Separate by Bones"),
        (("bpy.types.MMD_TOOLS_OT_model_separate_by_bones",), ()),
        ("ja_JP", "モデルをボーンで分離", (False, ())),
        ("zh_HANS", "用骨骼分离模型", (False, ())),
    ),
    (
        ("*", "Boundary Joint Owner"),
        (("bpy.types.MMD_TOOLS_OT_model_separate_by_bones.boundary_joint_owner",), ()),
        ("ja_JP", "境界ジョイントオーナー", (False, ())),
        ("zh_HANS", "边界的关节所有者", (False, ())),
    ),
    (
        ("*", "Source Model"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_model_separate_by_bones.boundary_joint_owner:'SOURCE'",
            ),
            (),
        ),
        ("ja_JP", "分離元モデル", (False, ())),
        ("zh_HANS", "源模型", (False, ())),
    ),
    (
        ("*", "Destination Model"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_model_separate_by_bones.boundary_joint_owner:'DESTINATION'",
            ),
            (),
        ),
        ("ja_JP", "分離先モデル", (False, ())),
        ("zh_HANS", "目标模型", (False, ())),
    ),
    (
        ("*", "Include Descendant Bones"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_model_separate_by_bones.include_descendant_bones",
            ),
            (),
        ),
        ("ja_JP", "子孫ボーンを含む", (False, ())),
        ("zh_HANS", "包括后代的骨骼", (False, ())),
    ),
    (
        ("*", "Separate Armature"),
        (("bpy.types.MMD_TOOLS_OT_model_separate_by_bones.separate_armature",), ()),
        ("ja_JP", "アーマチュアを分離する", (False, ())),
        ("zh_HANS", "分离骨架", (False, ())),
    ),
    (
        ("*", "Weight Threshold"),
        (("bpy.types.MMD_TOOLS_OT_model_separate_by_bones.weight_threshold",), ()),
        ("ja_JP", "ウェイトしきい値", (False, ())),
        ("zh_HANS", "权重阈值", (False, ())),
    ),
    (
        ("Operator", "Add Morph"),
        (("bpy.types.MMD_TOOLS_OT_morph_add",), ()),
        ("ja_JP", "モーフを追加", (False, ())),
        ("zh_HANS", "添加变形", (False, ())),
    ),
    (
        ("*", "Add a morph item to active morph list"),
        (("bpy.types.MMD_TOOLS_OT_morph_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向活动的变形列表中添加变形", (False, ())),
    ),
    (
        ("Operator", "Copy Morph"),
        (("bpy.types.MMD_TOOLS_OT_morph_copy",), ()),
        ("ja_JP", "モーフをコピー", (False, ())),
        ("zh_HANS", "复制变形", (False, ())),
    ),
    (
        ("*", "Make a copy of active morph in the list"),
        (("bpy.types.MMD_TOOLS_OT_morph_copy",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在列表中创建活动变形的复制", (False, ())),
    ),
    (
        ("Operator", "Move Morph"),
        (("bpy.types.MMD_TOOLS_OT_morph_move",), ()),
        ("ja_JP", "モーフを移動", (False, ())),
        ("zh_HANS", "移动变形", (False, ())),
    ),
    (
        ("*", "Move active morph item up/down in the list"),
        (("bpy.types.MMD_TOOLS_OT_morph_move",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在列表中上移或下移活动的变形", (False, ())),
    ),
    (
        ("Operator", "Add Morph Offset"),
        (("bpy.types.MMD_TOOLS_OT_morph_offset_add",), ()),
        ("ja_JP", "モーフオフセットを追加", (False, ())),
        ("zh_HANS", "添加变形偏移", (False, ())),
    ),
    (
        ("*", "Add a morph offset item to the list"),
        (("bpy.types.MMD_TOOLS_OT_morph_offset_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "向列表中添加变形偏移", (False, ())),
    ),
    (
        ("Operator", "Remove Morph Offset"),
        (("bpy.types.MMD_TOOLS_OT_morph_offset_remove",), ()),
        ("ja_JP", "モーフオフセットを削除", (False, ())),
        ("zh_HANS", "移除变形偏移", (False, ())),
    ),
    (
        ("*", "Remove morph offset item(s) from the list"),
        (("bpy.types.MMD_TOOLS_OT_morph_offset_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从列表中移除变形偏移", (False, ())),
    ),
    (
        ("*", "Delete all morph offset items"),
        (("bpy.types.MMD_TOOLS_OT_morph_offset_remove.all",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除所有变形偏移项目", (False, ())),
    ),
    (
        ("Operator", "Overwrite Bone Morphs from active Action Pose"),
        (("bpy.types.MMD_TOOLS_OT_morph_overwrite_from_active_action_pose",), ()),
        ("ja_JP", "ボーンモーフをアクティブなポーズライブラリから上書き", (False, ())),
        ("zh_HANS", "覆盖活动姿态库中的骨骼变形", (False, ())),
    ),
    (
        ("Operator", "Remove Morph"),
        (("bpy.types.MMD_TOOLS_OT_morph_remove",), ()),
        ("ja_JP", "モーフを削除", (False, ())),
        ("zh_HANS", "移除变形", (False, ())),
    ),
    (
        ("*", "Remove morph item(s) from the list"),
        (("bpy.types.MMD_TOOLS_OT_morph_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从列表中移除变形", (False, ())),
    ),
    (
        ("*", "Delete all morph items"),
        (("bpy.types.MMD_TOOLS_OT_morph_remove.all",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除所有变形项目", (False, ())),
    ),
    (
        ("Operator", "Morph Slider Setup"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "设置变形滑块", (False, ())),
    ),
    (
        ("*", "Translate MMD morphs of selected object into format usable by Blender"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中物体的 MMD 变形转译为 Blender 可用的格式", (False, ())),
    ),
    (
        ("*", "Create placeholder object for morph sliders"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'CREATE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为变形滑块创建占位物体", (False, ())),
    ),
    (
        ("*", "Bind"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'BIND'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "绑定", (False, ())),
    ),
    (
        ("*", "Bind morph sliders"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'BIND'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "绑定变形滑块", (False, ())),
    ),
    (
        ("*", "Unbind"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'UNBIND'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "解绑", (False, ())),
    ),
    (
        ("*", "Unbind morph sliders"),
        (("bpy.types.MMD_TOOLS_OT_morph_slider_setup.type:'UNBIND'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "解除与变形滑块的绑定", (False, ())),
    ),
    (
        ("Operator", "Move Material Down"),
        (("bpy.types.MMD_TOOLS_OT_move_material_down",), ()),
        ("ja_JP", "マテリアルを移動", (False, ())),
        ("zh_HANS", "移动材质", (False, ())),
    ),
    (
        ("*", "Moves the selected material one slot down"),
        (("bpy.types.MMD_TOOLS_OT_move_material_down",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中材质槽下移", (False, ())),
    ),
    (
        ("Operator", "Move Material Up"),
        (("bpy.types.MMD_TOOLS_OT_move_material_up",), ()),
        ("ja_JP", "マテリアルを移動", (False, ())),
        ("zh_HANS", "移动材质", (False, ())),
    ),
    (
        ("*", "Moves selected material one slot up"),
        (("bpy.types.MMD_TOOLS_OT_move_material_up",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将选中材质槽上移", (False, ())),
    ),
    (
        ("Operator", "Move Object"),
        (("bpy.types.MMD_TOOLS_OT_object_move",), ()),
        ("ja_JP", "オブジェクトを移動", (False, ())),
        ("zh_HANS", "移动物体", (False, ())),
    ),
    (
        ("*", "Move active object up/down in the list"),
        (("bpy.types.MMD_TOOLS_OT_object_move",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将活动的物体在列表中上移或下移", (False, ())),
    ),
    (
        ("Operator", "Select Object"),
        (("bpy.types.MMD_TOOLS_OT_object_select",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选中物体", (False, ())),
    ),
    (
        ("*", "Select the object"),
        (("bpy.types.MMD_TOOLS_OT_object_select",), ()),
        ("ja_JP", "メッシュを選択してください", (True, ())),
        ("zh_HANS", "选中物体", (False, ())),
    ),
    (
        ("*", "The object name"),
        (("bpy.types.MMD_TOOLS_OT_object_select.name",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "物体名", (False, ())),
    ),
    (
        ("Operator", "Delete Bake"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_ptcache_rigid_body_delete_bake",
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:76",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除烘焙", (False, ())),
    ),
    (
        ("Operator", "Recalculate bone roll"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_recalculate_bone_roll",
                "extensions/user_default/mmd_tools/panels/prop_object.py:35",
            ),
            (),
        ),
        ("ja_JP", "ボーンロールを再計算", (False, ())),
        ("zh_HANS", "重算骨骼扭转", (False, ())),
    ),
    (
        ("*", "Recalculate bone roll for arm related bones"),
        (("bpy.types.MMD_TOOLS_OT_recalculate_bone_roll",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为骨架相关的骨骼重新计算滚转", (False, ())),
    ),
    (
        ("Operator", "Reset Object Visivility"),
        (("bpy.types.MMD_TOOLS_OT_reset_object_visibility",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重设物体可见性", (False, ())),
    ),
    (
        ("*", "Reset to default Blender shading"),
        (("bpy.types.MMD_TOOLS_OT_reset_shading",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重设为 Blender 默认着色", (False, ())),
    ),
    (
        ("Operator", "Restore this Name"),
        (("bpy.types.MMD_TOOLS_OT_restore_mmd_translation_element_name",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "恢复名称", (False, ())),
    ),
    (
        ("*", "Add Rigid Bodies to selected bones"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_add",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为选中的骨骼添加刚体", (False, ())),
    ),
    (
        ("*", "Collision Group Mask"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.collision_group_mask",
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'collision_group_mask'",
                "bpy.types.MMDRigidBody.collision_group_mask",
            ),
            (),
        ),
        ("ja_JP", "コリジョングループマスク", (False, ())),
        ("zh_HANS", "碰撞组遮罩", (False, ())),
    ),
    (
        ("*", "The groups the object can not collide with"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.collision_group_mask",
                "bpy.types.MMDRigidBody.collision_group_mask",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该物体不能碰撞的碰撞组", (False, ())),
    ),
    (
        ("*", "Collision Group"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.collision_group_number",
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'collision_group_number'",
                "bpy.types.MMDRigidBody.collision_group_number",
            ),
            (),
        ),
        ("ja_JP", "コリジョングループ", (False, ())),
        ("zh_HANS", "碰撞组", (False, ())),
    ),
    (
        ("*", "The collision group of the object"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.collision_group_number",
                "bpy.types.MMDRigidBody.collision_group_number",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该物体的碰撞组", (False, ())),
    ),
    (
        ("*", "How much the object 'weights' irrespective of gravity"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_add.mass",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该物体在重力影响之外的“重量”", (False, ())),
    ),
    (
        (
            "*",
            "The english name of rigid body ($name_e means use the english name of target bone)",
        ),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_add.name_e",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该刚体的英文名 ($name_e 表示使用目标骨骼的英文名)", (False, ())),
    ),
    (
        (
            "*",
            "The name of rigid body ($name_j means use the japanese name of target bone)",
        ),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_add.name_j",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该刚体的名称 ($name_j 表示使用目标骨骼的日文名)", (False, ())),
    ),
    (
        ("*", "Select the collision shape"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_shape",
                "bpy.types.MMDRigidBody.shape",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择碰撞形状", (False, ())),
    ),
    (
        ("*", "Rigid Type"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type",
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'type'",
                "bpy.types.MMDRigidBody.type",
            ),
            (),
        ),
        ("ja_JP", "リジッドタイプ", (False, ())),
        ("zh_HANS", "刚体类型", (False, ())),
    ),
    (
        ("*", "Select rigid type"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type",
                "bpy.types.MMDRigidBody.type",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择刚体类型", (False, ())),
    ),
    (
        ("*", "Rigid body's orientation completely determined by attached bone"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type:'0'",
                "bpy.types.MMDRigidBody.type:'0'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体的朝向完全由附加的骨骼确定", (False, ())),
    ),
    (
        ("*", "Attached bone's orientation completely determined by rigid body"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type:'1'",
                "bpy.types.MMDRigidBody.type:'1'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "附加骨骼的朝向完全由刚体确定", (False, ())),
    ),
    (
        ("*", "Physics + Bone"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type:'2'",
                "bpy.types.MMDRigidBody.type:'2'",
            ),
            (),
        ),
        ("ja_JP", "物理演算 + ボーン", (False, ())),
        ("zh_HANS", "物理 + 骨骼", (False, ())),
    ),
    (
        ("*", "Bone determined by combination of parent and attached rigid body"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_add.rigid_type:'2'",
                "bpy.types.MMDRigidBody.type:'2'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼由父级与刚体共同决定", (False, ())),
    ),
    (
        ("*", "Size of the object, the values will multiply the length of target bone"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_add.size",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "物体的大小，结果会与目标骨骼的长度相乘", (False, ())),
    ),
    (
        ("*", "Deletes the currently selected Rigid Body"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_remove",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "删除选中的刚体", (False, ())),
    ),
    (
        ("Operator", "Select Rigid Body"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select",), ()),
        ("ja_JP", "MMDリジッドボディ選択", (True, ())),
        ("zh_HANS", "选择刚体", (False, ())),
    ),
    (
        (
            "*",
            "Select similar rigidbody objects which have the same property values with active rigidbody object",
        ),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择与活动刚体具有相同属性值的刚体", (False, ())),
    ),
    (
        ("*", "Hide Others"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select.hide_others",), ()),
        ("ja_JP", "他を隠す", (False, ())),
        ("zh_HANS", "隐藏其他", (False, ())),
    ),
    (
        (
            "*",
            "Hide the rigidbody object which does not have the same property values with active rigidbody object",
        ),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select.hide_others",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "隐藏与选中刚体不具有相同属性值的刚体", (False, ())),
    ),
    (
        ("*", "Select the properties to be compared"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select.properties",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择欲比较的属性", (False, ())),
    ),
    (
        ("*", "Collision group"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'collision_group_number'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "碰撞组", (False, ())),
    ),
    (
        ("*", "Collision group mask"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'collision_group_mask'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "碰撞组遮罩", (False, ())),
    ),
    (
        ("*", "Rigid type"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'type'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体类型", (False, ())),
    ),
    (
        ("*", "Collision shape"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'shape'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "碰撞形状", (False, ())),
    ),
    (
        ("*", "Target bone"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_rigid_body_select.properties:'bone'",
                "bpy.types.BoneMorphData.bone",
                "bpy.types.MMDRigidBody.bone",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "目标骨骼", (False, ())),
    ),
    (
        ("Operator", "Update Rigid Body World"),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_world_update",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "更新刚体世界", (False, ())),
    ),
    (
        (
            "*",
            "Update rigid body world and references of rigid body constraint according to current scene objects (experimental)",
        ),
        (("bpy.types.MMD_TOOLS_OT_rigid_body_world_update",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "按照当前场景的物体更新刚体世界以及刚体约束的引用 (实验性)",
            (False, ()),
        ),
    ),
    (
        ("Operator", "Bind SDEF Driver"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind",), ()),
        ("ja_JP", "SDEFドライバーをバインド", (False, ())),
        ("zh_HANS", "绑定SDEF驱动器", (False, ())),
    ),
    (
        ("*", "Bind MMD SDEF data of selected objects"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为选中物体绑定 MMD SDEF 数据", (False, ())),
    ),
    (
        ("*", "Bulk"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.mode:'2'",), ()),
        ("ja_JP", "バルク", (False, ())),
        ("zh_HANS", "散装", (False, ())),
    ),
    (
        ("*", "Speed up with numpy (may be slower in some cases)"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.mode:'2'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用 Numpy 加速 (可能反而减速)", (False, ())),
    ),
    (
        ("*", "Normal mode"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.mode:'1'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "正常模式", (False, ())),
    ),
    (
        ("*", "- Auto -"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.mode:'0'",), ()),
        ("ja_JP", "自動", (False, ())),
        ("zh_HANS", "自动", (False, ())),
    ),
    (
        ("*", "Select best mode by benchmark result"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.mode:'0'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "按基准测试结果选择最优的模式", (False, ())),
    ),
    (
        ("*", "Support bone scaling (slow)"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.use_scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "支持骨骼缩放 (较慢)", (False, ())),
    ),
    (
        ("*", "Skip"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.use_skip",), ()),
        ("ja_JP", "スキップ", (False, ())),
        ("zh_HANS", "略过", (False, ())),
    ),
    (
        ("*", "Skip when the bones are not moving"),
        (("bpy.types.MMD_TOOLS_OT_sdef_bind.use_skip",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼不运动时跳过", (False, ())),
    ),
    (
        ("Operator", "Reset MMD SDEF cache"),
        (("bpy.types.MMD_TOOLS_OT_sdef_cache_reset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重设 MMD SDEF 缓存", (False, ())),
    ),
    (
        ("*", "Reset MMD SDEF cache of selected objects and clean unused cache"),
        (("bpy.types.MMD_TOOLS_OT_sdef_cache_reset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重设选中物体的 MMD SDEF 缓存并清理未使用的缓存", (False, ())),
    ),
    (
        ("Operator", "Unbind SDEF Driver"),
        (("bpy.types.MMD_TOOLS_OT_sdef_unbind",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "解绑 SDEF 驱动器", (False, ())),
    ),
    (
        ("*", "Unbind MMD SDEF data of selected objects"),
        (("bpy.types.MMD_TOOLS_OT_sdef_unbind",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "解除选中物体的 MMD SDEF 数据绑定", (False, ())),
    ),
    (
        ("Operator", "Select Related Bone"),
        (("bpy.types.MMD_TOOLS_OT_select_bone_morph_offset_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择相关骨骼", (False, ())),
    ),
    (
        ("*", "Select the bone assigned to this offset in the armature"),
        (("bpy.types.MMD_TOOLS_OT_select_bone_morph_offset_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在骨架中选中与此偏移相关的骨骼", (False, ())),
    ),
    (
        ("Operator", "Separate By Materials"),
        (("bpy.types.MMD_TOOLS_OT_separate_by_materials",), ()),
        ("ja_JP", "マテリアルで分解", (True, ())),
        ("zh_HANS", "按材质分离", (False, ())),
    ),
    (
        ("*", "Clean Shape Keys"),
        (("bpy.types.MMD_TOOLS_OT_separate_by_materials.clean_shape_keys",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除形态键", (False, ())),
    ),
    (
        ("*", "Remove unused shape keys of separated objects"),
        (("bpy.types.MMD_TOOLS_OT_separate_by_materials.clean_shape_keys",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除分离物体未使用的形态键", (False, ())),
    ),
    (
        ("Operator", "Set Frame Range"),
        (("bpy.types.MMD_TOOLS_OT_set_frame_range",), ()),
        ("ja_JP", "フレーム範囲を使用", (True, ())),
        ("zh_HANS", "设置帧区间", (False, ())),
    ),
    (
        (
            "*",
            "Set the frame range to best values to play the animation from start to finish. And set the frame rate to 30.0.",
        ),
        (("bpy.types.MMD_TOOLS_OT_set_frame_range",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "将帧区间设置为完整播放动画的最优值, 并将帧率设置为 30.0",
            (False, ()),
        ),
    ),
    (
        ("Operator", "GLSL View"),
        (("bpy.types.MMD_TOOLS_OT_set_glsl_shading",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "GLSL 视图", (False, ())),
    ),
    (
        ("*", "Use GLSL shading with additional lighting"),
        (("bpy.types.MMD_TOOLS_OT_set_glsl_shading",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用带有额外光照的 GLSL 着色", (False, ())),
    ),
    (
        ("Operator", "Shadeless GLSL View"),
        (("bpy.types.MMD_TOOLS_OT_set_shadeless_glsl_shading",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "无光影 GLSL 视图", (False, ())),
    ),
    (
        ("*", "Use only toon shading"),
        (("bpy.types.MMD_TOOLS_OT_set_shadeless_glsl_shading",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "仅使用卡通着色", (False, ())),
    ),
    (
        ("Operator", "Translate a MMD Model"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model",), ()),
        ("ja_JP", "MMDモデルを翻訳", (False, ())),
        ("zh_HANS", "翻译一个MMD模型", (False, ())),
    ),
    (
        ("*", "Translate Japanese names of a MMD model"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "翻译 MMD 模型中的日文名", (False, ())),
    ),
    (
        ("*", "Allow Fails"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.allow_fails",), ()),
        ("ja_JP", "失敗を許可", (False, ())),
        ("zh_HANS", "允许失败", (False, ())),
    ),
    (
        ("*", "Allow incompletely translated names"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.allow_fails",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "允许为完全翻译的名称", (False, ())),
    ),
    (
        ("*", "Dictionary"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.dictionary",
                "bpy.types.MMDTranslation.dictionary",
            ),
            (),
        ),
        ("ja_JP", "辞書", (False, ())),
        ("zh_HANS", "词典", (False, ())),
    ),
    (
        ("*", "Translate names from Japanese to English using selected dictionary"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.dictionary",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用选中的词典从日语翻译为英语", (False, ())),
    ),
    (
        ("*", "Select translation mode"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.modes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择翻译模式", (False, ())),
    ),
    (
        ("*", "MMD Names"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.modes:'MMD'",), ()),
        ("ja_JP", "MMD名", (False, ())),
        ("zh_HANS", "MMD名称", (False, ())),
    ),
    (
        ("*", "Fill MMD English names"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.modes:'MMD'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "填入 MMD 英文名", (False, ())),
    ),
    (
        ("*", "Translate blender names (experimental)"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.modes:'BLENDER'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "翻译 Blender 名称 (实验性)", (False, ())),
    ),
    (
        ("*", "Overwrite a translated English name"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.overwrite",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "覆写翻译的英文名", (False, ())),
    ),
    (
        ("*", "Select which parts will be translated"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.types",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择欲翻译的部分", (False, ())),
    ),
    (
        ("*", "Display frames"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types:'DISPLAY'",
                "bpy.types.MMDTranslation.filter_types:'DISPLAY'",
                "bpy.types.MMDTranslationElement.type:'DISPLAY'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "表示枠", (False, ())),
    ),
    (
        ("*", "Rigidbodies and joints"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types:'PHYSICS'",
                "bpy.types.MMDTranslation.filter_types:'PHYSICS'",
                "bpy.types.MMDTranslationElement.type:'PHYSICS'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体与关节", (False, ())),
    ),
    (
        ("*", "Model name and comments"),
        (
            (
                "bpy.types.MMD_TOOLS_OT_translate_mmd_model.types:'INFO'",
                "bpy.types.MMDTranslation.filter_types:'INFO'",
                "bpy.types.MMDTranslationElement.type:'INFO'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "模型名与注释", (False, ())),
    ),
    (
        ("*", "Use Morph Prefix"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.use_morph_prefix",), ()),
        ("ja_JP", "モーフ接頭辞を使用", (False, ())),
        ("zh_HANS", "使用变形前缀", (False, ())),
    ),
    (
        ("*", "Add/remove prefix to English name of morph"),
        (("bpy.types.MMD_TOOLS_OT_translate_mmd_model.use_morph_prefix",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "添加或删除变形英文名的前缀", (False, ())),
    ),
    (
        ("Operator", "View Bone Morph"),
        (("bpy.types.MMD_TOOLS_OT_view_bone_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查看骨骼变形", (False, ())),
    ),
    (
        ("*", "View the result of active bone morph"),
        (("bpy.types.MMD_TOOLS_OT_view_bone_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查看活动骨骼变形的结果", (False, ())),
    ),
    (
        ("Operator", "View UV Morph"),
        (("bpy.types.MMD_TOOLS_OT_view_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查看 UV 变形", (False, ())),
    ),
    (
        ("*", "View the result of active UV morph on current mesh object"),
        (("bpy.types.MMD_TOOLS_OT_view_uv_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "查看活动 UV 变形在当前网格物体上的结果", (False, ())),
    ),
    (
        ("*", "MMD Bone Tools"),
        (("bpy.types.BONE_PT_mmd_tools_bone",), ()),
        ("ja_JP", "MMDボーンツール", (True, ())),
        ("zh_HANS", "MMD Tools", (True, ())),
    ),
    (
        ("*", "MMD Joint"),
        (("bpy.types.JOINT_PT_mmd_tools_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "MMD Material"),
        (("bpy.types.MATERIAL_PT_mmd_tools_material",), ()),
        ("ja_JP", "マテリアル:", (True, ())),
        ("zh_HANS", "材质:", (True, ())),
    ),
    (
        ("*", "MMD Texture"),
        (("bpy.types.MATERIAL_PT_mmd_tools_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "MMD"),
        (
            (
                "bpy.types.OBJECT_PT_mmd_tools_bone_order",
                "bpy.types.OBJECT_PT_mmd_tools_display_items",
                "bpy.types.OBJECT_PT_mmd_tools_joint_list",
                "bpy.types.OBJECT_PT_mmd_tools_material_sorter",
                "bpy.types.OBJECT_PT_mmd_tools_meshes_sorter",
                "bpy.types.OBJECT_PT_mmd_tools_model_production",
                "bpy.types.OBJECT_PT_mmd_tools_model_setup",
                "bpy.types.OBJECT_PT_mmd_tools_morph_tools",
                "bpy.types.OBJECT_PT_mmd_tools_rigidbody_list",
                "bpy.types.OBJECT_PT_mmd_tools_scene_setup",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Bone Order"),
        (("bpy.types.OBJECT_PT_mmd_tools_bone_order",), ()),
        ("ja_JP", "ボーン順序", (False, ())),
        ("zh_HANS", "骨骼顺序", (False, ())),
    ),
    (
        ("*", "MMD Camera Tools"),
        (("bpy.types.OBJECT_PT_mmd_tools_camera",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Display Panel"),
        (("bpy.types.OBJECT_PT_mmd_tools_display_items",), ()),
        ("ja_JP", "表示パネル", (False, ())),
        ("zh_HANS", "显示面板", (False, ())),
    ),
    (
        ("*", "Joints"),
        (("bpy.types.OBJECT_PT_mmd_tools_joint_list",), ()),
        ("ja_JP", "ジョイント", (True, ())),
        ("zh_HANS", "关节", (True, ())),
    ),
    (
        ("*", "MMD Light Tools"),
        (("bpy.types.OBJECT_PT_mmd_tools_light",), ()),
        ("ja_JP", "MMDボーンツール", (True, ())),
        ("zh_HANS", "MMD Tools", (True, ())),
    ),
    (
        ("*", "Material Sorter"),
        (("bpy.types.OBJECT_PT_mmd_tools_material_sorter",), ()),
        ("ja_JP", "マテリアル順序", (False, ())),
        ("zh_HANS", "材质顺序", (False, ())),
    ),
    (
        ("*", "Meshes Sorter"),
        (("bpy.types.OBJECT_PT_mmd_tools_meshes_sorter",), ()),
        ("ja_JP", "メッシュ順序", (False, ())),
        ("zh_HANS", "网格顺序", (False, ())),
    ),
    (
        ("*", "Model Production"),
        (("bpy.types.OBJECT_PT_mmd_tools_model_production",), ()),
        ("ja_JP", "モデル製作", (False, ())),
        ("zh_HANS", "模型制作", (False, ())),
    ),
    (
        ("*", "Model Setup"),
        (("bpy.types.OBJECT_PT_mmd_tools_model_setup",), ()),
        ("ja_JP", "モデル設定", (False, ())),
        ("zh_HANS", "模型设定", (False, ())),
    ),
    (
        ("*", "Morph Tools"),
        (("bpy.types.OBJECT_PT_mmd_tools_morph_tools",), ()),
        ("ja_JP", "モーフツール", (False, ())),
        ("zh_HANS", "变形工具", (False, ())),
    ),
    (
        ("*", "Rigid Bodies"),
        (("bpy.types.OBJECT_PT_mmd_tools_rigidbody_list",), ()),
        ("ja_JP", "リジッドボディ", (False, ())),
        ("zh_HANS", "刚体", (False, ())),
    ),
    (
        ("*", "MMD Model Information"),
        (("bpy.types.OBJECT_PT_mmd_tools_root_object",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "MMD Rigid Body"),
        (("bpy.types.RIGID_PT_mmd_tools_bone",), ()),
        ("ja_JP", "MMDリジッドボディ選択", (True, ())),
        ("zh_HANS", "刚体", (True, ())),
    ),
    (
        ("*", "MMD IK Toggle"),
        (("bpy.types.PoseBone.mmd_ik_toggle",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "切换 MMD IK", (False, ())),
    ),
    (
        ("*", "MMD IK toggle is used to import/export animation of IK on-off"),
        (("bpy.types.PoseBone.mmd_ik_toggle",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在启用或关闭逆向运动学的情况下导入或导出动画", (False, ())),
    ),
    (
        ("*", "Bone Morph"),
        (("bpy.types.BoneMorph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼变形", (False, ())),
    ),
    (
        ("*", "Active Bone Data"),
        (("bpy.types.BoneMorph.active_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动骨骼数据", (False, ())),
    ),
    (
        ("*", "Select category"),
        (
            (
                "bpy.types.BoneMorph.category",
                "bpy.types.GroupMorph.category",
                "bpy.types.MaterialMorph.category",
                "bpy.types.UVMorph.category",
                "bpy.types.VertexMorph.category",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择类别", (False, ())),
    ),
    (
        ("*", "Eye Brow"),
        (
            (
                "bpy.types.BoneMorph.category:'EYEBROW'",
                "bpy.types.GroupMorph.category:'EYEBROW'",
                "bpy.types.MaterialMorph.category:'EYEBROW'",
                "bpy.types.UVMorph.category:'EYEBROW'",
                "bpy.types.VertexMorph.category:'EYEBROW'",
                "bpy.types.MMD_ROOT_UL_display_items.morph_filter:'EYEBROW'",
            ),
            (),
        ),
        ("ja_JP", "眉毛", (False, ())),
        ("zh_HANS", "眉毛", (False, ())),
    ),
    (
        ("*", "Eye"),
        (
            (
                "bpy.types.BoneMorph.category:'EYE'",
                "bpy.types.GroupMorph.category:'EYE'",
                "bpy.types.MaterialMorph.category:'EYE'",
                "bpy.types.UVMorph.category:'EYE'",
                "bpy.types.VertexMorph.category:'EYE'",
                "bpy.types.MMD_ROOT_UL_display_items.morph_filter:'EYE'",
            ),
            (),
        ),
        ("ja_JP", "目", (False, ())),
        ("zh_HANS", "眼睛", (False, ())),
    ),
    (
        ("*", "Mouth"),
        (
            (
                "bpy.types.BoneMorph.category:'MOUTH'",
                "bpy.types.GroupMorph.category:'MOUTH'",
                "bpy.types.MaterialMorph.category:'MOUTH'",
                "bpy.types.UVMorph.category:'MOUTH'",
                "bpy.types.VertexMorph.category:'MOUTH'",
                "bpy.types.MMD_ROOT_UL_display_items.morph_filter:'MOUTH'",
            ),
            (),
        ),
        ("ja_JP", "口", (False, ())),
        ("zh_HANS", "嘴巴", (False, ())),
    ),
    (
        ("*", "Morph Data"),
        (
            (
                "bpy.types.BoneMorph.data",
                "bpy.types.GroupMorph.data",
                "bpy.types.MaterialMorph.data",
                "bpy.types.UVMorph.data",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "变形数据", (False, ())),
    ),
    (
        ("*", "Japanese Name"),
        (
            (
                "bpy.types.BoneMorph.name",
                "bpy.types.GroupMorph.name",
                "bpy.types.MMDBone.name_j",
                "bpy.types.MMDJoint.name_j",
                "bpy.types.MMDMaterial.name_j",
                "bpy.types.MMDRigidBody.name_j",
                "bpy.types.MaterialMorph.name",
                "bpy.types.UVMorph.name",
                "bpy.types.VertexMorph.name",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "日文名", (False, ())),
    ),
    (
        ("*", "English Name"),
        (
            (
                "bpy.types.BoneMorph.name_e",
                "bpy.types.GroupMorph.name_e",
                "bpy.types.MMDBone.name_e",
                "bpy.types.MMDDisplayItemFrame.name_e",
                "bpy.types.MMDJoint.name_e",
                "bpy.types.MMDMaterial.name_e",
                "bpy.types.MMDRigidBody.name_e",
                "bpy.types.MaterialMorph.name_e",
                "bpy.types.UVMorph.name_e",
                "bpy.types.VertexMorph.name_e",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "英文名", (False, ())),
    ),
    (
        ("*", "Bone ID"),
        (("bpy.types.BoneMorphData.bone_id", "bpy.types.MMDBone.bone_id"), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼 ID", (False, ())),
    ),
    (
        ("*", "Rotation in quaternions"),
        (("bpy.types.BoneMorphData.rotation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "四元数表示的旋转", (False, ())),
    ),
    (
        ("*", "Group Morph"),
        (("bpy.types.GroupMorph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "组变形", (False, ())),
    ),
    (
        ("*", "Active Group Data"),
        (("bpy.types.GroupMorph.active_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动组数据", (False, ())),
    ),
    (
        ("*", "Group Morph Offset"),
        (("bpy.types.GroupMorphOffset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "组变形偏移", (False, ())),
    ),
    (
        ("*", "Morph Type"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type",
                "bpy.types.MMDDisplayItem.morph_type",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "变形类型", (False, ())),
    ),
    (
        ("*", "Select morph type"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type",
                "bpy.types.MMDDisplayItem.morph_type",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择变形类型", (False, ())),
    ),
    (
        ("*", "Material Morphs"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type:'material_morphs'",
                "bpy.types.MMDDisplayItem.morph_type:'material_morphs'",
                "bpy.types.MMDRoot.active_morph_type:'material_morphs'",
                "bpy.types.MMDRoot.material_morphs",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质变形", (False, ())),
    ),
    (
        ("*", "UV Morphs"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type:'uv_morphs'",
                "bpy.types.MMDDisplayItem.morph_type:'uv_morphs'",
                "bpy.types.MMDRoot.active_morph_type:'uv_morphs'",
                "bpy.types.MMDRoot.uv_morphs",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV 变形", (False, ())),
    ),
    (
        ("*", "Bone Morphs"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type:'bone_morphs'",
                "bpy.types.MMDDisplayItem.morph_type:'bone_morphs'",
                "bpy.types.MMDRoot.active_morph_type:'bone_morphs'",
                "bpy.types.MMDRoot.bone_morphs",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼变形", (False, ())),
    ),
    (
        ("*", "Vertex Morphs"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type:'vertex_morphs'",
                "bpy.types.MMDDisplayItem.morph_type:'vertex_morphs'",
                "bpy.types.MMDRoot.active_morph_type:'vertex_morphs'",
                "bpy.types.MMDRoot.vertex_morphs",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "顶点变形", (False, ())),
    ),
    (
        ("*", "Group Morphs"),
        (
            (
                "bpy.types.GroupMorphOffset.morph_type:'group_morphs'",
                "bpy.types.MMDDisplayItem.morph_type:'group_morphs'",
                "bpy.types.MMDRoot.active_morph_type:'group_morphs'",
                "bpy.types.MMDRoot.group_morphs",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "组变形", (False, ())),
    ),
    (
        ("*", "Additional Transform Bone"),
        (("bpy.types.MMDBone.additional_transform_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外变换骨骼", (False, ())),
    ),
    (
        ("*", "Additional transform bone"),
        (("bpy.types.MMDBone.additional_transform_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外变换骨骼", (False, ())),
    ),
    (
        ("*", "Additional Transform Bone ID"),
        (("bpy.types.MMDBone.additional_transform_bone_id",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外变换骨骼ID", (False, ())),
    ),
    (
        ("*", "Additional Transform Influence"),
        (("bpy.types.MMDBone.additional_transform_influence",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外变换骨骼影响", (False, ())),
    ),
    (
        ("*", "Additional transform influence"),
        (("bpy.types.MMDBone.additional_transform_influence",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外变换骨骼的影响权重", (False, ())),
    ),
    (
        ("*", "Unique ID for the reference of bone morph and rotate+/move+"),
        (("bpy.types.MMDBone.bone_id",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该骨骼形变, 旋转 + 和移动 + 的参照的唯一ID", (False, ())),
    ),
    (
        ("*", "Display Connection Bone"),
        (("bpy.types.MMDBone.display_connection_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接骨骼", (False, ())),
    ),
    (
        ("*", "Target bone for display connection"),
        (("bpy.types.MMDBone.display_connection_bone",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接的目标骨骼", (False, ())),
    ),
    (
        ("*", "Display Connection Bone ID"),
        (("bpy.types.MMDBone.display_connection_bone_id",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接的骨骼 ID", (False, ())),
    ),
    (
        ("*", "Bone ID for display connection (PMX displayConnection)"),
        (("bpy.types.MMDBone.display_connection_bone_id",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接 (PMX displayConnection) 使用的骨骼 ID", (False, ())),
    ),
    (
        ("*", "Display Connection Offset"),
        (("bpy.types.MMDBone.display_connection_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示联系偏移", (False, ())),
    ),
    (
        ("*", "Offset vector for display connection"),
        (("bpy.types.MMDBone.display_connection_offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接使用的偏移向量", (False, ())),
    ),
    (
        ("*", "Display Connection Type"),
        (("bpy.types.MMDBone.display_connection_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接类型", (False, ())),
    ),
    (
        ("*", "Type of display connection"),
        (("bpy.types.MMDBone.display_connection_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示连接的类型", (False, ())),
    ),
    (
        ("*", "Connected to a bone"),
        (("bpy.types.MMDBone.display_connection_type:'BONE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "连接至骨骼", (False, ())),
    ),
    (
        ("*", "Connected to an offset position"),
        (("bpy.types.MMDBone.display_connection_type:'OFFSET'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "连接至加偏移的位置", (False, ())),
    ),
    (
        ("*", "No connection"),
        (("bpy.types.MMDBone.display_connection_type:'NONE'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "没有连接", (False, ())),
    ),
    (
        ("*", "Fixed Axis"),
        (("bpy.types.MMDBone.enabled_fixed_axis", "bpy.types.MMDBone.fixed_axis"), ()),
        ("ja_JP", "軸制限", (False, ())),
        ("zh_HANS", "固定轴", (False, ())),
    ),
    (
        ("*", "Use fixed axis"),
        (("bpy.types.MMDBone.enabled_fixed_axis",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用固定的坐标轴", (False, ())),
    ),
    (
        ("*", "Local Axes"),
        (("bpy.types.MMDBone.enabled_local_axes",), ()),
        ("ja_JP", "ローカル軸", (False, ())),
        ("zh_HANS", "局部轴", (False, ())),
    ),
    (
        ("*", "Use local axes"),
        (("bpy.types.MMDBone.enabled_local_axes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用局部坐标轴", (False, ())),
    ),
    (
        ("*", "Fixed axis"),
        (("bpy.types.MMDBone.fixed_axis",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "固定轴", (False, ())),
    ),
    (
        ("*", "Additional Location"),
        (("bpy.types.MMDBone.has_additional_location",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外位置", (False, ())),
    ),
    (
        ("*", "Additional location"),
        (("bpy.types.MMDBone.has_additional_location",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "具有额外的位置", (False, ())),
    ),
    (
        ("*", "Additional Rotation"),
        (("bpy.types.MMDBone.has_additional_rotation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "额外旋转", (False, ())),
    ),
    (
        ("*", "Additional rotation"),
        (("bpy.types.MMDBone.has_additional_rotation",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "具有额外的旋转", (False, ())),
    ),
    (
        ("*", "IK Rotation Constraint"),
        (("bpy.types.MMDBone.ik_rotation_constraint",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "IK 旋转约束", (False, ())),
    ),
    (
        ("*", "The unit angle of IK"),
        (("bpy.types.MMDBone.ik_rotation_constraint",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "逆向运动学的单位角", (False, ())),
    ),
    (
        ("*", "Controllable"),
        (("bpy.types.MMDBone.is_controllable",), ()),
        ("ja_JP", "操作", (False, ())),
        ("zh_HANS", "可控制的", (False, ())),
    ),
    (
        ("*", "Is controllable"),
        (("bpy.types.MMDBone.is_controllable",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该骨骼是否可被控制", (False, ())),
    ),
    (
        ("*", "Tip Bone"),
        (("bpy.types.MMDBone.is_tip",), ()),
        ("ja_JP", "ティップボーン", (False, ())),
        ("zh_HANS", "尖端骨骼", (False, ())),
    ),
    (
        ("*", "Is zero length bone"),
        (("bpy.types.MMDBone.is_tip",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该骨骼的长度是否为零", (False, ())),
    ),
    (
        ("*", "Local X-Axis"),
        (("bpy.types.MMDBone.local_axis_x",), ()),
        ("ja_JP", "ローカルX軸", (False, ())),
        ("zh_HANS", "局部X轴", (False, ())),
    ),
    (
        ("*", "Local x-axis"),
        (("bpy.types.MMDBone.local_axis_x",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "局部X轴", (False, ())),
    ),
    (
        ("*", "Local Z-Axis"),
        (("bpy.types.MMDBone.local_axis_z",), ()),
        ("ja_JP", "ローカルZ軸", (False, ())),
        ("zh_HANS", "局部Z轴", (False, ())),
    ),
    (
        ("*", "Local z-axis"),
        (("bpy.types.MMDBone.local_axis_z",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "局部Z轴", (False, ())),
    ),
    (
        ("*", "After Dynamics"),
        (("bpy.types.MMDBone.transform_after_dynamics",), ()),
        ("ja_JP", "物理後", (False, ())),
        ("zh_HANS", "作用于物理之后", (False, ())),
    ),
    (
        ("*", "After physics"),
        (("bpy.types.MMDBone.transform_after_dynamics",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "变换作用在物理之后", (False, ())),
    ),
    (
        ("*", "Transform Order"),
        (("bpy.types.MMDBone.transform_order",), ()),
        ("ja_JP", "変形階層", (False, ())),
        ("zh_HANS", "变形顺序", (False, ())),
    ),
    (
        ("*", "Deformation tier"),
        (("bpy.types.MMDBone.transform_order",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "变形的顺序, 越大约靠后计算", (False, ())),
    ),
    (
        ("*", "Is perspective"),
        (("bpy.types.MMDCamera.is_perspective",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "相机是否是透视相机", (False, ())),
    ),
    (
        ("*", "PMX 表示項目(表示枠内の1項目)"),
        (("bpy.types.MMDDisplayItem",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "PMX 表示（表示枠中的一项）", (False, ())),
    ),
    (
        ("*", "Select item type"),
        (("bpy.types.MMDDisplayItem.type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择项目类型", (False, ())),
    ),
    (
        ("*", "Morph"),
        (("bpy.types.MMDDisplayItem.type:'MORPH'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "形变", (False, ())),
    ),
    (
        (
            "*",
            "PMX 表示枠\n\n    PMXファイル内では表示枠がリストで格納されています。\n    ",
        ),
        (("bpy.types.MMDDisplayItemFrame",), ()),
        ("ja_JP", "", (False, ())),
        (
            "zh_HANS",
            "PMX 表示枠\n\n    在 PMX 文件中，表示枠存储在一个列表中\n    ",
            (False, ()),
        ),
    ),
    (
        ("*", "Active Display Item"),
        (("bpy.types.MMDDisplayItemFrame.active_item",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的表示枠项目", (False, ())),
    ),
    (
        ("*", "Display Items"),
        (("bpy.types.MMDDisplayItemFrame.data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "表示枠项目", (False, ())),
    ),
    (
        ("*", "Special"),
        (("bpy.types.MMDDisplayItemFrame.is_special",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "特别", (False, ())),
    ),
    (
        ("*", "Is special"),
        (("bpy.types.MMDDisplayItemFrame.is_special",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "是否是特别的表示枠", (False, ())),
    ),
    (
        ("*", "マテリアル"),
        (("bpy.types.MMDMaterial",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质", (False, ())),
    ),
    (
        ("*", "Alpha transparency"),
        (("bpy.types.MMDMaterial.alpha",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "Alpha 透明度", (False, ())),
    ),
    (
        ("*", "Ambient color"),
        (
            (
                "bpy.types.MMDMaterial.ambient_color",
                "bpy.types.MaterialMorphData.ambient_color",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "环境色", (False, ())),
    ),
    (
        ("*", "Edge Color"),
        (
            (
                "bpy.types.MMDMaterial.edge_color",
                "bpy.types.MaterialMorphData.edge_color",
            ),
            (),
        ),
        ("ja_JP", "輪郭カラー", (False, ())),
        ("zh_HANS", "边缘颜色", (False, ())),
    ),
    (
        ("*", "Toon edge color"),
        (("bpy.types.MMDMaterial.edge_color",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "卡通边缘色", (False, ())),
    ),
    (
        ("*", "Edge Weight"),
        (
            (
                "bpy.types.MMDMaterial.edge_weight",
                "bpy.types.MaterialMorphData.edge_weight",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "边缘权重", (True, ())),
    ),
    (
        ("*", "Toon edge size"),
        (("bpy.types.MMDMaterial.edge_weight",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "卡通边缘的大小", (False, ())),
    ),
    (
        ("*", "Ground Shadow"),
        (("bpy.types.MMDMaterial.enabled_drop_shadow",), ()),
        ("ja_JP", "地面シャドウ", (False, ())),
        ("zh_HANS", "地面阴影", (False, ())),
    ),
    (
        ("*", "Display ground shadow"),
        (("bpy.types.MMDMaterial.enabled_drop_shadow",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示地面阴影", (False, ())),
    ),
    (
        ("*", "Self Shadow"),
        (("bpy.types.MMDMaterial.enabled_self_shadow",), ()),
        ("ja_JP", "セルフシャドウ", (False, ())),
        ("zh_HANS", "自身阴影", (False, ())),
    ),
    (
        ("*", "Object can cast shadows"),
        (("bpy.types.MMDMaterial.enabled_self_shadow",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "物体能否投射阴影", (False, ())),
    ),
    (
        ("*", "Self Shadow Map"),
        (("bpy.types.MMDMaterial.enabled_self_shadow_map",), ()),
        ("ja_JP", "セルフシャドウマップ", (False, ())),
        ("zh_HANS", "自身阴影贴图", (False, ())),
    ),
    (
        ("*", "Object can become shadowed by other objects"),
        (("bpy.types.MMDMaterial.enabled_self_shadow_map",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "物体上能否产生其他物体的阴影", (False, ())),
    ),
    (
        ("*", "Toon Edge"),
        (("bpy.types.MMDMaterial.enabled_toon_edge",), ()),
        ("ja_JP", "トゥーン輪郭", (False, ())),
        ("zh_HANS", "卡通边缘", (False, ())),
    ),
    (
        ("*", "Use toon edge"),
        (("bpy.types.MMDMaterial.enabled_toon_edge",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用卡通边缘", (False, ())),
    ),
    (
        ("*", "Double Sided"),
        (("bpy.types.MMDMaterial.is_double_sided",), ()),
        ("ja_JP", "両面表示", (False, ())),
        ("zh_HANS", "双面", (False, ())),
    ),
    (
        ("*", "Both sides of mesh should be rendered"),
        (("bpy.types.MMDMaterial.is_double_sided",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "是否渲染网格的两面", (False, ())),
    ),
    (
        ("*", "Use Shared Toon Texture"),
        (("bpy.types.MMDMaterial.is_shared_toon_texture",), ()),
        ("ja_JP", "共有トゥーンテクスチャを使用", (False, ())),
        ("zh_HANS", "使用共用的卡通纹理", (False, ())),
    ),
    (
        ("*", "Use shared toon texture or custom toon texture"),
        (("bpy.types.MMDMaterial.is_shared_toon_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用共用的卡通纹理或自定义的卡通纹理", (False, ())),
    ),
    (
        ("*", "Material ID"),
        (
            (
                "bpy.types.MMDMaterial.material_id",
                "bpy.types.MaterialMorphData.material_id",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质ID", (False, ())),
    ),
    (
        ("*", "Unique ID for the reference of material morph"),
        (("bpy.types.MMDMaterial.material_id",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质变形引用使用的唯一ID", (False, ())),
    ),
    (
        ("*", "Shared Toon Texture"),
        (("bpy.types.MMDMaterial.shared_toon_texture",), ()),
        ("ja_JP", "共有トゥーンテクスチャ", (False, ())),
        ("zh_HANS", "共用的卡通纹理", (False, ())),
    ),
    (
        ("*", "Shared toon texture id (toon01.bmp ~ toon10.bmp)"),
        (("bpy.types.MMDMaterial.shared_toon_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "共用卡通纹理的ID (toon01.bmp ~ toon10.bmp)", (False, ())),
    ),
    (
        ("*", "Reflect"),
        (
            (
                "bpy.types.MMDMaterial.shininess",
                "bpy.types.MaterialMorphData.shininess",
                "bpy.types.MaterialMorphData.shininess",
            ),
            (),
        ),
        ("ja_JP", "反射", (False, ())),
        ("zh_HANS", "反射", (False, ())),
    ),
    (
        ("*", "Sharpness of reflected highlights"),
        (("bpy.types.MMDMaterial.shininess",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "反射高光的锐度", (False, ())),
    ),
    (
        ("*", "Specular color"),
        (
            (
                "bpy.types.MMDMaterial.specular_color",
                "bpy.types.MaterialMorphData.specular_color",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "高光色", (False, ())),
    ),
    (
        ("*", "Sphere Map Type"),
        (("bpy.types.MMDMaterial.sphere_texture_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "球面纹理类型", (False, ())),
    ),
    (
        ("*", "Choose sphere texture blend type"),
        (("bpy.types.MMDMaterial.sphere_texture_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择球面纹理的混合类型", (False, ())),
    ),
    (
        ("*", "SubTexture"),
        (("bpy.types.MMDMaterial.sphere_texture_type:'3'",), ()),
        ("ja_JP", "サブテクスチャ", (False, ())),
        ("zh_HANS", "次纹理", (False, ())),
    ),
    (
        ("*", "Toon Texture"),
        (
            (
                "bpy.types.MMDMaterial.toon_texture",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:147",
            ),
            (),
        ),
        ("ja_JP", "トゥーンテクスチャ", (False, ())),
        ("zh_HANS", "卡通纹理", (False, ())),
    ),
    (
        ("*", "The file path of custom toon texture"),
        (("bpy.types.MMDMaterial.toon_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "自定义卡通纹理的文件路径", (False, ())),
    ),
    (
        ("*", "Size of the object"),
        (("bpy.types.MMDRigidBody.size",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "物体大小", (False, ())),
    ),
    (
        (
            "*",
            "MMDモデルデータ\n\n    モデルルート用に作成されたEmtpyオブジェクトで使用します\n    ",
        ),
        (("bpy.types.MMDRoot",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Active Bone Index"),
        (("bpy.types.MMDRoot.active_bone_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动骨骼索引", (False, ())),
    ),
    (
        ("*", "Index of the active bone in the armature"),
        (("bpy.types.MMDRoot.active_bone_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨架中活动骨骼的索引", (False, ())),
    ),
    (
        ("*", "Active Display Item Frame"),
        (("bpy.types.MMDRoot.active_display_item_frame",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的表示枠项目", (False, ())),
    ),
    (
        ("*", "Active Joint Index"),
        (("bpy.types.MMDRoot.active_joint_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的关节索引", (False, ())),
    ),
    (
        ("*", "Active Mesh"),
        (("bpy.types.MMDRoot.active_mesh_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的网格", (False, ())),
    ),
    (
        ("*", "Active Morph"),
        (("bpy.types.MMDRoot.active_morph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的形变", (False, ())),
    ),
    (
        ("*", "Active Morph Type"),
        (("bpy.types.MMDRoot.active_morph_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的形变类型", (False, ())),
    ),
    (
        ("*", "Select current morph type"),
        (("bpy.types.MMDRoot.active_morph_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择当前的形变类型", (False, ())),
    ),
    (
        ("*", "Active Rigidbody Index"),
        (("bpy.types.MMDRoot.active_rigidbody_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的刚体索引", (False, ())),
    ),
    (
        ("*", "Comment (English)"),
        (("bpy.types.MMDRoot.comment_e_text",), ()),
        ("ja_JP", "コメント(英語)", (False, ())),
        ("zh_HANS", "注释(英文)", (False, ())),
    ),
    (
        ("*", "The text datablock of the english comment"),
        (("bpy.types.MMDRoot.comment_e_text",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "英文注释的文本数据块", (False, ())),
    ),
    (
        ("*", "The text datablock of the comment"),
        (("bpy.types.MMDRoot.comment_text",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "注释的文本数据块", (False, ())),
    ),
    (
        ("*", "Is Built"),
        (("bpy.types.MMDRoot.is_built",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "已装配", (False, ())),
    ),
    (
        ("*", "Morph Panel Show Settings"),
        (("bpy.types.MMDRoot.morph_panel_show_settings",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "形变面板展示设定", (False, ())),
    ),
    (
        ("*", "Show Morph Settings"),
        (("bpy.types.MMDRoot.morph_panel_show_settings",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示形变设定", (False, ())),
    ),
    (
        ("*", "Name (English)"),
        (("bpy.types.MMDRoot.name_e",), ()),
        ("ja_JP", "名前(英語)", (False, ())),
        ("zh_HANS", "名称(英文)", (False, ())),
    ),
    (
        ("*", "Show Armature"),
        (("bpy.types.MMDRoot.show_armature",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示骨架", (False, ())),
    ),
    (
        ("*", "Show the armature object of the MMD model"),
        (("bpy.types.MMDRoot.show_armature",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD模型的骨架物体", (False, ())),
    ),
    (
        ("*", "Show Joints"),
        (("bpy.types.MMDRoot.show_joints",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示关节", (False, ())),
    ),
    (
        ("*", "Show all joints of the MMD model"),
        (("bpy.types.MMDRoot.show_joints",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD模型的所有关节", (False, ())),
    ),
    (
        ("*", "Show all meshes of the MMD model"),
        (("bpy.types.MMDRoot.show_meshes",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD模型的所有网格", (False, ())),
    ),
    (
        ("*", "Show Joint Names"),
        (("bpy.types.MMDRoot.show_names_of_joints",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示关节名", (False, ())),
    ),
    (
        ("*", "Show joint names"),
        (("bpy.types.MMDRoot.show_names_of_joints",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示关节的名称", (False, ())),
    ),
    (
        ("*", "Show Rigid Body Names"),
        (("bpy.types.MMDRoot.show_names_of_rigid_bodies",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示刚体名", (False, ())),
    ),
    (
        ("*", "Show rigid body names"),
        (("bpy.types.MMDRoot.show_names_of_rigid_bodies",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示刚体的名称", (False, ())),
    ),
    (
        ("*", "Show Rigid Bodies"),
        (("bpy.types.MMDRoot.show_rigid_bodies",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示刚体", (False, ())),
    ),
    (
        ("*", "Show all rigid bodies of the MMD model"),
        (("bpy.types.MMDRoot.show_rigid_bodies",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD模型的所有刚体", (False, ())),
    ),
    (
        ("*", "Show Temps"),
        (("bpy.types.MMDRoot.show_temporary_objects",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示临时物体", (False, ())),
    ),
    (
        ("*", "Show all temporary objects of the MMD model"),
        (("bpy.types.MMDRoot.show_temporary_objects",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD模型的所有临时物体", (False, ())),
    ),
    (
        ("*", "Use Property Driver"),
        (("bpy.types.MMDRoot.use_property_driver",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用属性驱动器", (False, ())),
    ),
    (
        ("*", "Setup drivers for MMD property animation (Visibility and IK toggles)"),
        (("bpy.types.MMDRoot.use_property_driver",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "为MMD属性动画 (可见性与逆向运动学切换) 设置驱动器", (False, ())),
    ),
    (
        ("*", "Use SDEF"),
        (("bpy.types.MMDRoot.use_sdef", "bpy.types.MMDRoot.use_sdef"), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用球面混合蒙皮权重", (False, ())),
    ),
    (
        ("*", "Use Sphere Texture"),
        (("bpy.types.MMDRoot.use_sphere_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用球材质", (False, ())),
    ),
    (
        ("*", "Use sphere texture"),
        (("bpy.types.MMDRoot.use_sphere_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用球材质", (False, ())),
    ),
    (
        ("*", "Use Toon Texture"),
        (("bpy.types.MMDRoot.use_toon_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用卡通材质", (False, ())),
    ),
    (
        ("*", "Use toon texture"),
        (("bpy.types.MMDRoot.use_toon_texture",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "使用卡通材质", (False, ())),
    ),
    (
        ("*", "Operation Script Preset"),
        (("bpy.types.MMDTranslation.batch_operation_script_preset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "操作脚本预设", (False, ())),
    ),
    (
        ("*", '""'),
        (("bpy.types.MMDTranslation.batch_operation_script_preset:'CLEAR'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '""', (False, ())),
    ),
    (
        ("*", "Translate to English"),
        (("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_ENGLISH'",), ()),
        ("ja_JP", "英語へ翻訳", (False, ())),
        ("zh_HANS", "翻译成英文", (False, ())),
    ),
    (
        ("*", "to_english(name)"),
        (("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_ENGLISH'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "to_english(name)", (False, ())),
    ),
    (
        ("*", "Blender L/R to MMD L/R"),
        (("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_MMD_LR'",), ()),
        ("ja_JP", "Blenderの左右をMMDの左右へ", (False, ())),
        ("zh_HANS", "将Blender的左右改为MMD的左右", (False, ())),
    ),
    (
        ("*", "to_mmd_lr(name)"),
        (("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_MMD_LR'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "to_mmd_lr(name)", (False, ())),
    ),
    (
        ("*", "MMD L/R to Blender L/R"),
        (
            ("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_BLENDER_LR'",),
            (),
        ),
        ("ja_JP", "MMDの左右をBlenderの左右へ", (False, ())),
        ("zh_HANS", "将MMD的左右改为Blender的左右", (False, ())),
    ),
    (
        ("*", "to_blender_lr(name_j)"),
        (
            ("bpy.types.MMDTranslation.batch_operation_script_preset:'TO_BLENDER_LR'",),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "to_blender_lr(name_j)", (False, ())),
    ),
    (
        ("*", "Restore Blender Names"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_BLENDER'",
            ),
            (),
        ),
        ("ja_JP", "Blender名称を復元", (False, ())),
        ("zh_HANS", "恢复Blender名称", (False, ())),
    ),
    (
        ("*", "org_name"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_BLENDER'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "org_name", (False, ())),
    ),
    (
        ("*", "Restore Japanese MMD Names"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_JAPANESE'",
            ),
            (),
        ),
        ("ja_JP", "日本語MMD名称を復元", (False, ())),
        ("zh_HANS", "恢复日文MMD名称", (False, ())),
    ),
    (
        ("*", "org_name_j"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_JAPANESE'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "org_name_j", (False, ())),
    ),
    (
        ("*", "Restore English MMD Names"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_ENGLISH'",
            ),
            (),
        ),
        ("ja_JP", "英語MMD名称を復元", (False, ())),
        ("zh_HANS", "恢复英文MMD名称", (False, ())),
    ),
    (
        ("*", "org_name_e"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'RESTORE_ENGLISH'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "org_name_e", (False, ())),
    ),
    (
        ("*", "Copy English MMD Names, if empty copy Japanese MMD Name"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'ENGLISH_IF_EMPTY_JAPANESE'",
            ),
            (),
        ),
        ("ja_JP", "英語MMD名称をコピー、空なら日本語MMD名称をコピー", (False, ())),
        ("zh_HANS", "复制英文MMD名称，如果为空则复制日文MMD名称", (False, ())),
    ),
    (
        ("*", "name_e if name_e else name_j"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'ENGLISH_IF_EMPTY_JAPANESE'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "name_e if name_e else name_j", (False, ())),
    ),
    (
        ("*", "Copy Japanese MMD Names, if empty copy English MMD Name"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'JAPANESE_IF_EMPTY_ENGLISH'",
            ),
            (),
        ),
        ("ja_JP", "日本語MMD名称をコピー、空なら英語MMD名称をコピー", (False, ())),
        ("zh_HANS", "复制日文MMD名称，如果为空则复制英文MMD名称", (False, ())),
    ),
    (
        ("*", "name_j if name_j else name_e"),
        (
            (
                "bpy.types.MMDTranslation.batch_operation_script_preset:'JAPANESE_IF_EMPTY_ENGLISH'",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "name_j if name_j else name_e", (False, ())),
    ),
    (
        ("*", "Operation Target"),
        (("bpy.types.MMDTranslation.batch_operation_target",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "操作目标", (False, ())),
    ),
    (
        ("*", "Blender Name (name)"),
        (("bpy.types.MMDTranslation.batch_operation_target:'BLENDER'",), ()),
        ("ja_JP", "Blender名称 (name)", (False, ())),
        ("zh_HANS", "Blender名称 (name)", (False, ())),
    ),
    (
        ("*", "Japanese MMD Name (name_j)"),
        (("bpy.types.MMDTranslation.batch_operation_target:'JAPANESE'",), ()),
        ("ja_JP", "日本語MMD名称 (name_j)", (False, ())),
        ("zh_HANS", "日文MMD名称 (name_j)", (False, ())),
    ),
    (
        ("*", "English MMD Name (name_e)"),
        (("bpy.types.MMDTranslation.batch_operation_target:'ENGLISH'",), ()),
        ("ja_JP", "英語MMD名称 (name_e)", (False, ())),
        ("zh_HANS", "英文MMD名称 (name_e)", (False, ())),
    ),
    (
        ("*", "English Blank"),
        (("bpy.types.MMDTranslation.filter_english_blank",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "英语为空", (False, ())),
    ),
    (
        ("*", "Japanese Blank"),
        (("bpy.types.MMDTranslation.filter_japanese_blank",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "日语为空", (False, ())),
    ),
    (
        ("*", "Restorable"),
        (("bpy.types.MMDTranslation.filter_restorable",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "可恢复", (False, ())),
    ),
    (
        ("*", "Material Morph"),
        (("bpy.types.MaterialMorph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质变形", (False, ())),
    ),
    (
        ("*", "Active Material Data"),
        (("bpy.types.MaterialMorph.active_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的材质数据", (False, ())),
    ),
    (
        ("*", "Edge color"),
        (("bpy.types.MaterialMorphData.edge_color",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "边缘颜色", (False, ())),
    ),
    (
        ("*", "Edge weight"),
        (("bpy.types.MaterialMorphData.edge_weight",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "边缘权重", (False, ())),
    ),
    (
        ("*", "Target material"),
        (("bpy.types.MaterialMorphData.material",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "目标材质", (False, ())),
    ),
    (
        ("*", "Material Data"),
        (("bpy.types.MaterialMorphData.material_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "材质数据", (False, ())),
    ),
    (
        ("*", "Offset Type"),
        (("bpy.types.MaterialMorphData.offset_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "偏移类型", (False, ())),
    ),
    (
        ("*", "Select offset type"),
        (("bpy.types.MaterialMorphData.offset_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择偏移类型", (False, ())),
    ),
    (
        ("*", "Related Mesh"),
        (("bpy.types.MaterialMorphData.related_mesh",), ()),
        ("ja_JP", "関連するメッシュ", (False, ())),
        ("zh_HANS", "相关网格", (False, ())),
    ),
    (
        ("*", "Stores a reference to the mesh where this morph data belongs to"),
        (("bpy.types.MaterialMorphData.related_mesh",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "保存该形变数据所属的网格的引用", (False, ())),
    ),
    (
        ("*", "Related Mesh Data"),
        (("bpy.types.MaterialMorphData.related_mesh_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "相关网格数据", (False, ())),
    ),
    (
        ("*", "Sphere Texture factor"),
        (("bpy.types.MaterialMorphData.sphere_texture_factor",), ()),
        ("ja_JP", "スフィアテクスチャ係数", (False, ())),
        ("zh_HANS", "球体纹理系数", (False, ())),
    ),
    (
        ("*", "Sphere texture factor"),
        (("bpy.types.MaterialMorphData.sphere_texture_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "球体纹理的系数", (False, ())),
    ),
    (
        ("*", "Texture factor"),
        (
            (
                "bpy.types.MaterialMorphData.texture_factor",
                "bpy.types.MaterialMorphData.texture_factor",
            ),
            (),
        ),
        ("ja_JP", "テクスチャ係数", (False, ())),
        ("zh_HANS", "纹理系数", (False, ())),
    ),
    (
        ("*", "Toon Texture factor"),
        (("bpy.types.MaterialMorphData.toon_texture_factor",), ()),
        ("ja_JP", "トゥーンテクスチャ係数", (False, ())),
        ("zh_HANS", "卡通纹理系数", (False, ())),
    ),
    (
        ("*", "Toon texture factor"),
        (("bpy.types.MaterialMorphData.toon_texture_factor",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "卡通纹理的系数", (False, ())),
    ),
    (
        ("*", "UV Morph"),
        (("bpy.types.UVMorph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV形变", (False, ())),
    ),
    (
        ("*", "Active UV Data"),
        (("bpy.types.UVMorph.active_data",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "活动的UV数据", (False, ())),
    ),
    (
        ("*", "Select data type"),
        (("bpy.types.UVMorph.data_type",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择数据类型", (False, ())),
    ),
    (
        ("*", "Store offset data in root object (deprecated)"),
        (("bpy.types.UVMorph.data_type:'DATA'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在根物体中保存偏移数据 (已弃用)", (False, ())),
    ),
    (
        ("*", "Store offset data in vertex groups"),
        (("bpy.types.UVMorph.data_type:'VERTEX_GROUP'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "在顶点组中保存便宜数据", (False, ())),
    ),
    (
        ("*", "UV Index"),
        (("bpy.types.UVMorph.uv_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV索引", (False, ())),
    ),
    (
        ("*", "UV index (UV, UV1 ~ UV4)"),
        (("bpy.types.UVMorph.uv_index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV索引 (UV, UV1 ~ UV4)", (False, ())),
    ),
    (
        ("*", "Vertex Group Scale"),
        (("bpy.types.UVMorph.vertex_group_scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "顶点组比例", (False, ())),
    ),
    (
        ("*", 'The value scale of "Vertex Group" data type'),
        (("bpy.types.UVMorph.vertex_group_scale",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '"顶点组"数据类型的缩放比例值', (False, ())),
    ),
    (
        ("*", "UV Morph Offset"),
        (("bpy.types.UVMorphOffset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV 形变偏移", (False, ())),
    ),
    (
        ("*", "Vertex Index"),
        (("bpy.types.UVMorphOffset.index",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "顶点索引", (False, ())),
    ),
    (
        ("*", "UV offset"),
        (("bpy.types.UVMorphOffset.offset",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "UV偏移", (False, ())),
    ),
    (
        ("*", "Vertex Morph"),
        (("bpy.types.VertexMorph",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "顶点形变", (False, ())),
    ),
    (
        ("*", "MMD Name"),
        (("bpy.types.MMD_ROOT_UL_display_items.mmd_name",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MMD名称", (False, ())),
    ),
    (
        ("*", "Show JP or EN name of MMD bone"),
        (("bpy.types.MMD_ROOT_UL_display_items.mmd_name",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示MMD骨骼的日文或英文名", (False, ())),
    ),
    (
        ("*", "JP"),
        (("bpy.types.MMD_ROOT_UL_display_items.mmd_name:'name_j'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "日文", (False, ())),
    ),
    (
        ("*", "EN"),
        (("bpy.types.MMD_ROOT_UL_display_items.mmd_name:'name_e'",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "英文", (False, ())),
    ),
    (
        ("*", "Morph Filter"),
        (("bpy.types.MMD_ROOT_UL_display_items.morph_filter",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "形变筛选", (False, ())),
    ),
    (
        ("*", "Only show items matching this category"),
        (("bpy.types.MMD_ROOT_UL_display_items.morph_filter",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "仅展示符合分类的项目", (False, ())),
    ),
    (
        ("*", "Model Filter"),
        (
            (
                "bpy.types.MMD_TOOLS_UL_joints.model_filter",
                "bpy.types.MMD_TOOLS_UL_rigidbodies.model_filter",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "模型筛选", (False, ())),
    ),
    (
        ("*", "Show items of active model or all models"),
        (
            (
                "bpy.types.MMD_TOOLS_UL_joints.model_filter",
                "bpy.types.MMD_TOOLS_UL_rigidbodies.model_filter",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示选中的模型还是全部模型的项目", (False, ())),
    ),
    (
        ("*", "Active Model"),
        (
            (
                "bpy.types.MMD_TOOLS_UL_joints.model_filter:'ACTIVE'",
                "bpy.types.MMD_TOOLS_UL_rigidbodies.model_filter:'ACTIVE'",
            ),
            (),
        ),
        ("ja_JP", "選択中のモデル", (False, ())),
        ("zh_HANS", "活动的模型", (False, ())),
    ),
    (
        ("*", "All Models"),
        (
            (
                "bpy.types.MMD_TOOLS_UL_joints.model_filter:'ALL'",
                "bpy.types.MMD_TOOLS_UL_rigidbodies.model_filter:'ALL'",
            ),
            (),
        ),
        ("ja_JP", "全てのモデル", (False, ())),
        ("zh_HANS", "全部模型", (False, ())),
    ),
    (
        ("*", "Only show visible items"),
        (
            (
                "bpy.types.MMD_TOOLS_UL_joints.visible_only",
                "bpy.types.MMD_TOOLS_UL_rigidbodies.visible_only",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "仅显示可见项目", (False, ())),
    ),
    (
        ("Operator", "MikuMikuDance Model (.pmd, .pmx)"),
        (("extensions/user_default/mmd_tools/menus.py:22",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MikuMikuDance 模型 (.pmd, .pmx)", (False, ())),
    ),
    (
        ("Operator", "MikuMikuDance Motion (.vmd)"),
        (
            (
                "extensions/user_default/mmd_tools/menus.py:23",
                "extensions/user_default/mmd_tools/menus.py:45",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MikuMikuDance 动作 (.vmd)", (False, ())),
    ),
    (
        ("Operator", "Vocaloid Pose Data (.vpd)"),
        (
            (
                "extensions/user_default/mmd_tools/menus.py:24",
                "extensions/user_default/mmd_tools/menus.py:46",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "Vocaloid 姿态数据 (.vpd)", (False, ())),
    ),
    (
        ("Operator", "MikuMikuDance Model (.pmx)"),
        (("extensions/user_default/mmd_tools/menus.py:44",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MikuMikuDance 模型 (.pmx)", (False, ())),
    ),
    (
        ("Operator", "Create MMD Model"),
        (("extensions/user_default/mmd_tools/menus.py:66",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "创建 MMD 模型", (False, ())),
    ),
    (
        ("Operator", "MMD Flip Pose"),
        (("extensions/user_default/mmd_tools/menus.py:129",), ()),
        ("ja_JP", "MMDポーズを反転", (False, ())),
        ("zh_HANS", "MMD翻转姿态", (False, ())),
    ),
    (
        ("Operator", "Select MMD Rigid Body"),
        (("extensions/user_default/mmd_tools/menus.py:109",), ()),
        ("ja_JP", "MMDリジッドボディ選択", (False, ())),
        ("zh_HANS", "选择MMD刚体", (False, ())),
    ),
    (
        ("*", 'Imported MMD model from "%s"'),
        (("extensions/user_default/mmd_tools/operators/fileio.py:210",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '已从"%s"导入 MMD 模型', (False, ())),
    ),
    (
        ("*", '[Skipped] The armature object of MMD model "%s" can\'t be found'),
        (("extensions/user_default/mmd_tools/operators/fileio.py:552",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '[跳过] 找不到 MMD 模型"%s"的骨架数据', (False, ())),
    ),
    (
        ("*", 'Exported MMD model "%s" to "%s"'),
        (("extensions/user_default/mmd_tools/operators/fileio.py:580",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '已导出 MMD 模型"%s"至"%s"', (False, ())),
    ),
    (
        ("*", "Select a MMD model"),
        (
            (
                "extensions/user_default/mmd_tools/operators/material.py:262",
                "extensions/user_default/mmd_tools/operators/misc.py:195",
                "extensions/user_default/mmd_tools/operators/misc.py:241",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择 MMD 模型", (False, ())),
    ),
    (
        ("*", "Created %d toon edge(s)"),
        (("extensions/user_default/mmd_tools/operators/material.py:273",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "已创建 %d 条卡通边缘", (False, ())),
    ),
    (
        ("*", " * Failed to change to Cycles render engine."),
        (("extensions/user_default/mmd_tools/operators/material.py:48",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", " * 未能改变至 Cycles 渲染引擎", (False, ())),
    ),
    (
        ("*", "Materials not found"),
        (
            (
                "extensions/user_default/mmd_tools/operators/material.py:209",
                "extensions/user_default/mmd_tools/operators/material.py:235",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到材质", (False, ())),
    ),
    (
        ("*", "This operation will break existing f-curve/action."),
        (("extensions/user_default/mmd_tools/operators/misc.py:301",), ()),
        ("ja_JP", "この操作は既存のFカーブ/アクションを破壊します", (False, ())),
        ("zh_HANS", "这一操作将破坏现有的函数曲线/动作", (False, ())),
    ),
    (
        ("*", "Click [OK] to run the operation."),
        (("extensions/user_default/mmd_tools/operators/misc.py:302",), ()),
        ("ja_JP", "[OK]をクリックして操作を実行してください", (False, ())),
        ("zh_HANS", "点击[确定]来运行操作", (False, ())),
    ),
    (
        ("*", 'Can not move object "%s"'),
        (("extensions/user_default/mmd_tools/operators/misc.py:67",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '无法移动物体"%s"', (False, ())),
    ),
    (
        ("*", "The model does not have any meshes"),
        (("extensions/user_default/mmd_tools/operators/misc.py:205",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该模型不具有任何网格", (False, ())),
    ),
    (
        ("*", "Model Armature not found"),
        (("extensions/user_default/mmd_tools/operators/misc.py:246",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到模型的骨架", (False, ())),
    ),
    (
        ("*", "Active object is not an armature object"),
        (
            (
                "extensions/user_default/mmd_tools/operators/model.py:152",
                "extensions/user_default/mmd_tools/operators/model.py:183",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选中的物体不是骨架物体", (False, ())),
    ),
    (
        ("*", "You need to choose a Related Mesh first"),
        (
            (
                "extensions/user_default/mmd_tools/operators/morph.py:298",
                "extensions/user_default/mmd_tools/operators/morph.py:362",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "请首先选择关联的网格", (False, ())),
    ),
    (
        ("*", "The model mesh can't be found"),
        (
            (
                "extensions/user_default/mmd_tools/operators/morph.py:302",
                "extensions/user_default/mmd_tools/operators/morph.py:366",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到模型的网格", (False, ())),
    ),
    (
        ("*", 'Material "%s" not found'),
        (("extensions/user_default/mmd_tools/operators/morph.py:371",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '找不到材质"%s"', (False, ())),
    ),
    (
        ("*", 'Temporary material "%s" is in use'),
        (("extensions/user_default/mmd_tools/operators/morph.py:376",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", '临时材质"%s"已占用', (False, ())),
    ),
    (
        ("*", "Material not found"),
        (("extensions/user_default/mmd_tools/operators/morph.py:308",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到材质", (False, ())),
    ),
    (
        ("*", "Please select a mesh object"),
        (("extensions/user_default/mmd_tools/operators/morph.py:586",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择网格物体", (False, ())),
    ),
    (
        ("*", "Invalid uv index: %d"),
        (("extensions/user_default/mmd_tools/operators/morph.py:600",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "无效 UV 索引: %d", (False, ())),
    ),
    (
        ("*", "Failed to create a temporary uv layer"),
        (("extensions/user_default/mmd_tools/operators/morph.py:610",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "无法创建临时 UV 层", (False, ())),
    ),
    (
        ("*", ' * UV map "%s" not found'),
        (("extensions/user_default/mmd_tools/operators/morph.py:734",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", ' * 找不到 UV 贴图 "%s"', (False, ())),
    ),
    (
        ("*", "An unexpected error happened"),
        (("extensions/user_default/mmd_tools/operators/morph.py:329",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "发生了非预期的错误", (False, ())),
    ),
    (
        ("*", "Base material for %s was not found"),
        (("extensions/user_default/mmd_tools/operators/morph.py:435",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到 %s 的基础材质", (False, ())),
    ),
    (
        ("*", "The model root can't be found"),
        (("extensions/user_default/mmd_tools/operators/rigid_body.py:55",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到模型的根部", (False, ())),
    ),
    (
        ("*", "Please select two or more mmd rigid objects"),
        (("extensions/user_default/mmd_tools/operators/rigid_body.py:451",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择两个或更多 MMD 刚体", (False, ())),
    ),
    (
        ("*", "Binded  of  selected mesh(es)"),
        (("extensions/user_default/mmd_tools/operators/sdef.py:88",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "", (False, ())),
    ),
    (
        ("*", "Filter"),
        (("extensions/user_default/mmd_tools/operators/translations.py:238",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "筛选", (False, ())),
    ),
    (
        ("*", "is Blank:"),
        (("extensions/user_default/mmd_tools/operators/translations.py:242",), ()),
        ("ja_JP", "空白のみ:", (False, ())),
        ("zh_HANS", "是空白:", (False, ())),
    ),
    (
        ("*", "Japanese"),
        (("extensions/user_default/mmd_tools/operators/translations.py:244",), ()),
        ("ja_JP", "日本語", (False, ())),
        ("zh_HANS", "日文", (False, ())),
    ),
    (
        ("*", "English"),
        (("extensions/user_default/mmd_tools/operators/translations.py:245",), ()),
        ("ja_JP", "英語", (False, ())),
        ("zh_HANS", "英文", (False, ())),
    ),
    (
        ("*", "Select the target column for Batch Operations:"),
        (("extensions/user_default/mmd_tools/operators/translations.py:255",), ()),
        ("ja_JP", "一括操作の対象列を選択:", (False, ())),
        ("zh_HANS", "选择批量操作的目标列:", (False, ())),
    ),
    (
        ("*", "Batch Operation:"),
        (("extensions/user_default/mmd_tools/operators/translations.py:276",), ()),
        ("ja_JP", "一括操作:", (False, ())),
        ("zh_HANS", "批量操作:", (False, ())),
    ),
    (
        ("*", "Preset"),
        (("extensions/user_default/mmd_tools/operators/translations.py:281",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "预设", (False, ())),
    ),
    (
        ("Operator", "Execute"),
        (("extensions/user_default/mmd_tools/operators/translations.py:282",), ()),
        ("ja_JP", "実行", (False, ())),
        ("zh_HANS", "执行", (False, ())),
    ),
    (
        ("*", "Dictionaries:"),
        (("extensions/user_default/mmd_tools/operators/translations.py:286",), ()),
        ("ja_JP", "辞書:", (False, ())),
        ("zh_HANS", "词典:", (False, ())),
    ),
    (
        ("*", "to_english"),
        (("extensions/user_default/mmd_tools/operators/translations.py:288",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "to_english", (False, ())),
    ),
    (
        ("*", "replace"),
        (("extensions/user_default/mmd_tools/operators/translations.py:293",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "替换", (False, ())),
    ),
    (
        ("*", "Failed to translate %d names, see '%s' in text editor"),
        (
            (
                "extensions/user_default/mmd_tools/operators/translations.py:104",
                "extensions/user_default/mmd_tools/operators/translations.py:331",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "未能翻译 %d 个名称，参见文本编辑器中的 '%s'", (False, ())),
    ),
    (
        ("*", "Failed to load dictionary: %s"),
        (("extensions/user_default/mmd_tools/operators/translations.py:87",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "未能加载词典: %s", (False, ())),
    ),
    (
        ("*", "Information:"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_bone.py:46",
                "extensions/user_default/mmd_tools/panels/prop_material.py:31",
            ),
            (),
        ),
        ("ja_JP", "情報:", (False, ())),
        ("zh_HANS", "信息:", (False, ())),
    ),
    (
        ("*", "ID"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_bone.py:49",
                "extensions/user_default/mmd_tools/panels/prop_material.py:34",
            ),
            (),
        ),
        ("ja_JP", "ID", (False, ())),
        ("zh_HANS", "ID", (False, ())),
    ),
    (
        ("*", "Rotate +"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:87",), ()),
        ("ja_JP", "回転 +", (False, ())),
        ("zh_HANS", "旋转 +", (False, ())),
    ),
    (
        ("*", "Move +"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:88",), ()),
        ("ja_JP", "移動 +", (False, ())),
        ("zh_HANS", "移动 +", (False, ())),
    ),
    (
        ("*", "Influence"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:92",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "影响", (False, ())),
    ),
    (
        ("*", "Display Connection (Bone Target):"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:95",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼末端指向:", (False, ())),
    ),
    (
        ("*", "Type"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:96",), ()),
        ("ja_JP", "タイプ", (True, ())),
        ("zh_HANS", "类型", (False, ())),
    ),
    (
        ("*", "MMD Shadow Bone!"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:38",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MMD Shadow Bone!", (False, ())),
    ),
    (
        ("Operator", "Load"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_bone.py:68",
                "extensions/user_default/mmd_tools/panels/prop_bone.py:78",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "加载", (False, ())),
    ),
    (
        ("Operator", "Apply"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_bone.py:69",
                "extensions/user_default/mmd_tools/panels/prop_bone.py:79",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:183",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:210",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:153",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "应用", (False, ())),
    ),
    (
        ("*", "Target Bone"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:99",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "目标骨骼", (False, ())),
    ),
    (
        ("*", "IK Angle {%s}"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:27",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "IK角度 {%s}", (False, ())),
    ),
    (
        ("*", "IK Angle (%s)"),
        (("extensions/user_default/mmd_tools/panels/prop_bone.py:29",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "IK角度 (%s)", (False, ())),
    ),
    (
        ("*", "Distance"),
        (("extensions/user_default/mmd_tools/panels/prop_camera.py:36",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "距离", (False, ())),
    ),
    (
        ("Operator", "Convert"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_camera.py:44",
                "extensions/user_default/mmd_tools/panels/prop_lamp.py:36",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "转换", (False, ())),
    ),
    (
        ("*", "Light Source"),
        (("extensions/user_default/mmd_tools/panels/prop_lamp.py:34",), ()),
        ("ja_JP", "光源", (False, ())),
        ("zh_HANS", "光源", (False, ())),
    ),
    (
        ("*", "Color:"),
        (("extensions/user_default/mmd_tools/panels/prop_material.py:41",), ()),
        ("ja_JP", "カラー:", (False, ())),
        ("zh_HANS", "颜色:", (False, ())),
    ),
    (
        ("*", "Shadow:"),
        (("extensions/user_default/mmd_tools/panels/prop_material.py:53",), ()),
        ("ja_JP", "シャドウ:", (False, ())),
        ("zh_HANS", "阴影:", (False, ())),
    ),
    (
        ("*", "Texture:"),
        (("extensions/user_default/mmd_tools/panels/prop_material.py:91",), ()),
        ("ja_JP", "テクスチャ:", (False, ())),
        ("zh_HANS", "纹理:", (False, ())),
    ),
    (
        ("*", "Sphere Texture:"),
        (("extensions/user_default/mmd_tools/panels/prop_material.py:105",), ()),
        ("ja_JP", "スフィアテクスチャ:", (False, ())),
        ("zh_HANS", "球体纹理:", (False, ())),
    ),
    (
        ("Operator", "Add"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_material.py:102",
                "extensions/user_default/mmd_tools/panels/prop_material.py:116",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "添加", (False, ())),
    ),
    (
        ("Operator", "Remove"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_material.py:99",
                "extensions/user_default/mmd_tools/panels/prop_material.py:113",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "移除", (False, ())),
    ),
    (
        ("*", "Collision Group Mask:"),
        (("extensions/user_default/mmd_tools/panels/prop_physics.py:73",), ()),
        ("ja_JP", "コリジョングループマスク:", (False, ())),
        ("zh_HANS", "碰撞组遮罩:", (False, ())),
    ),
    (
        ("*", "Damping"),
        (("extensions/user_default/mmd_tools/panels/prop_physics.py:82",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "阻尼", (False, ())),
    ),
    (
        ("*", "X-Axis:"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_physics.py:118",
                "extensions/user_default/mmd_tools/panels/prop_physics.py:134",
            ),
            (),
        ),
        ("ja_JP", "X軸:", (False, ())),
        ("zh_HANS", "X轴:", (False, ())),
    ),
    (
        ("*", "Y-Axis:"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_physics.py:119",
                "extensions/user_default/mmd_tools/panels/prop_physics.py:135",
            ),
            (),
        ),
        ("ja_JP", "Y軸:", (False, ())),
        ("zh_HANS", "Y轴:", (False, ())),
    ),
    (
        ("*", "Z-Axis:"),
        (
            (
                "extensions/user_default/mmd_tools/panels/prop_physics.py:120",
                "extensions/user_default/mmd_tools/panels/prop_physics.py:136",
            ),
            (),
        ),
        ("ja_JP", "Z軸:", (False, ())),
        ("zh_HANS", "Z轴:", (False, ())),
    ),
    (
        ("*", "MMD Shading Presets"),
        (("extensions/user_default/mmd_tools/panels/shading.py:23",), ()),
        ("ja_JP", "MMDシェーディングプリセット", (False, ())),
        ("zh_HANS", "MMD着色预设", (False, ())),
    ),
    (
        ("Operator", "GLSL"),
        (("extensions/user_default/mmd_tools/panels/shading.py:25",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "GLSL", (False, ())),
    ),
    (
        ("Operator", "Shadeless"),
        (("extensions/user_default/mmd_tools/panels/shading.py:26",), ()),
        ("ja_JP", "影なし", (False, ())),
        ("zh_HANS", "无明暗", (False, ())),
    ),
    (
        ("Operator", "Reset"),
        (
            (
                "extensions/user_default/mmd_tools/panels/shading.py:28",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:41",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "重设", (False, ())),
    ),
    (
        ("*", "Migrating from old vertex group ordering to bone_id system"),
        (("extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:254",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "从旧的顶点组排序迁移至新的骨骼 ID 系统", (False, ())),
    ),
    (
        ("*", "Successfully migrated  bones from vertex groups to bone_id system"),
        (("extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:279",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "成功从旧的顶点组排序迁移至新的骨骼 ID 系统", (False, ())),
    ),
    (
        ("Operator", "Fix Bone Order"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:518",
                "extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:565",
            ),
            (),
        ),
        ("ja_JP", "ボーン順序", (True, ())),
        ("zh_HANS", "修复骨骼顺序", (False, ())),
    ),
    (
        ("*", "Total Bones: %d"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:564",
                "extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:564",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨骼数量: %d", (False, ())),
    ),
    (
        ("*", "Converting layer collections to MMD collections"),
        (("extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:229",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "将分层集合转换为 MMD 集合", (False, ())),
    ),
    (
        ("*", "Select a MMD Model"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:532",
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:23",
                "extensions/user_default/mmd_tools/panels/sidebar/joints.py:21",
                "extensions/user_default/mmd_tools/panels/sidebar/meshes_sorter.py:22",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:24",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:24",
                "extensions/user_default/mmd_tools/panels/sidebar/rigid_bodies.py:21",
            ),
            (),
        ),
        ("ja_JP", "MMDモデルを選択してください", (False, ())),
        ("zh_HANS", "选择一个MMD模型", (False, ())),
    ),
    (
        ("*", "The armature object of active MMD model can't be found"),
        (("extensions/user_default/mmd_tools/panels/sidebar/bone_order.py:537",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到选中MMD模型的骨架", (False, ())),
    ),
    (
        ("Operator", "Select"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:75",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:195",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择", (False, ())),
    ),
    (
        ("Operator", "Bone"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:73",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:74",
            ),
            (),
        ),
        ("ja_JP", "ボーン", (False, ())),
        ("zh_HANS", "骨骼", (False, ())),
    ),
    (
        ("Operator", "Morph"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:74",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:78",
            ),
            (),
        ),
        ("ja_JP", "モーフ", (False, ())),
        ("zh_HANS", "变形", (False, ())),
    ),
    (
        ("Operator", "Move To Top"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:176",
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:188",
                "extensions/user_default/mmd_tools/panels/sidebar/joints.py:70",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:333",
                "extensions/user_default/mmd_tools/panels/sidebar/rigid_bodies.py:82",
            ),
            (),
        ),
        ("ja_JP", "最初へ移動", (False, ())),
        ("zh_HANS", "移至顶部", (False, ())),
    ),
    (
        ("Operator", "Move To Bottom"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:177",
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:189",
                "extensions/user_default/mmd_tools/panels/sidebar/joints.py:71",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:334",
                "extensions/user_default/mmd_tools/panels/sidebar/rigid_bodies.py:83",
            ),
            (),
        ),
        ("ja_JP", "最後へ移動", (False, ())),
        ("zh_HANS", "移至底部", (False, ())),
    ),
    (
        ("Operator", "Delete All"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/display_panel.py:186",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:324",
            ),
            (),
        ),
        ("ja_JP", "全て削除", (False, ())),
        ("zh_HANS", "删除全部", (False, ())),
    ),
    (
        ("*", "Use the arrows to sort"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/material_sorter.py:49",
                "extensions/user_default/mmd_tools/panels/sidebar/meshes_sorter.py:48",
            ),
            (),
        ),
        ("ja_JP", "矢印を使って並べ替えてください", (False, ())),
        ("zh_HANS", "使用箭头来排序", (False, ())),
    ),
    (
        ("*", "Select a mesh object"),
        (
            ("extensions/user_default/mmd_tools/panels/sidebar/material_sorter.py:20",),
            (),
        ),
        ("ja_JP", "メッシュを選択してください", (False, ())),
        ("zh_HANS", "选择一个网格物体", (False, ())),
    ),
    (
        ("Operator", "Create Model"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:23",
            ),
            (),
        ),
        ("ja_JP", "モデルを作成", (False, ())),
        ("zh_HANS", "创建新的模型", (False, ())),
    ),
    (
        ("Operator", "Convert Model"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:24",
            ),
            (),
        ),
        ("ja_JP", "モデルを変換", (False, ())),
        ("zh_HANS", "转换模型", (False, ())),
    ),
    (
        ("Operator", "Attach Meshes"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:29",
            ),
            (),
        ),
        ("ja_JP", "メッシュを取付", (False, ())),
        ("zh_HANS", "连接网格", (False, ())),
    ),
    (
        ("Operator", "Translate"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:32",
            ),
            (),
        ),
        ("ja_JP", "翻訳", (False, ())),
        ("zh_HANS", "翻译", (False, ())),
    ),
    (
        ("*", "Model Surgery:"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:39",
            ),
            (),
        ),
        ("ja_JP", "モデル手術", (False, ())),
        ("zh_HANS", "模型手术", (False, ())),
    ),
    (
        ("Operator", "Chop"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:45",
            ),
            (),
        ),
        ("ja_JP", "切断", (False, ())),
        ("zh_HANS", "切断", (False, ())),
    ),
    (
        ("Operator", "Peel"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:56",
            ),
            (),
        ),
        ("ja_JP", "はがす", (False, ())),
        ("zh_HANS", "剥去", (False, ())),
    ),
    (
        ("Operator", "Join"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_production.py:63",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:139",
            ),
            (),
        ),
        ("ja_JP", "統合", (False, ())),
        ("zh_HANS", "合并", (False, ())),
    ),
    (
        ("*", "Visibility:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:40",), ()),
        ("ja_JP", "可視性:", (False, ())),
        ("zh_HANS", "可见性:", (False, ())),
    ),
    (
        ("*", "Mesh"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:46",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "网格", (False, ())),
    ),
    (
        ("*", "Armature"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:47",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "骨架", (False, ())),
    ),
    (
        ("*", "Temporary Object"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:48",), ()),
        ("ja_JP", "テンポラリオブジェクト", (False, ())),
        ("zh_HANS", "临时物体", (False, ())),
    ),
    (
        ("*", "Rigid Body"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:50",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "刚体", (False, ())),
    ),
    (
        ("*", "Assembly:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:59",), ()),
        ("ja_JP", "組み立て:", (False, ())),
        ("zh_HANS", "装配:", (False, ())),
    ),
    (
        ("Operator", "All"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:64",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "全部", (False, ())),
    ),
    (
        ("Operator", "SDEF"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:68",), ()),
        ("ja_JP", "SDEF", (False, ())),
        ("zh_HANS", "SDEF", (False, ())),
    ),
    (
        ("*", "Property"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:91",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "属性", (False, ())),
    ),
    (
        ("*", "IK Toggle:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:128",), ()),
        ("ja_JP", "IK切替え:", (False, ())),
        ("zh_HANS", "IK切换:", (False, ())),
    ),
    (
        ("*", "Mesh:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:136",), ()),
        ("ja_JP", "メッシュ:", (False, ())),
        ("zh_HANS", "网格:", (False, ())),
    ),
    (
        ("Operator", "Separate by Materials"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:138",), ()),
        ("ja_JP", "マテリアルで分解", (False, ())),
        ("zh_HANS", "按材质分开", (False, ())),
    ),
    (
        ("*", "Material:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:143",), ()),
        ("ja_JP", "マテリアル:", (False, ())),
        ("zh_HANS", "材质:", (False, ())),
    ),
    (
        ("*", "Sphere Texture"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:148",), ()),
        ("ja_JP", "スフィアテクスチャ", (False, ())),
        ("zh_HANS", "球体纹理", (False, ())),
    ),
    (
        ("Operator", "Convert to Blender"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:153",), ()),
        ("ja_JP", "Blender用に変換", (False, ())),
        ("zh_HANS", "转换给Blender", (False, ())),
    ),
    (
        ("Operator", "Convert to MMD"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:154",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "转换至 MMD", (False, ())),
    ),
    (
        ("*", "Misc:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:158",), ()),
        ("ja_JP", "その他:", (False, ())),
        ("zh_HANS", "杂项:", (False, ())),
    ),
    (
        ("Operator", "(Experimental) Global Translation"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:160",), ()),
        ("ja_JP", "(実験的) 全体翻訳", (False, ())),
        ("zh_HANS", "(实验性) 全局翻译", (False, ())),
    ),
    (
        ("Operator", "Physics"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:86",
                "extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:88",
            ),
            (),
        ),
        ("ja_JP", "物理演算", (False, ())),
        ("zh_HANS", "物理", (False, ())),
    ),
    (
        ("Operator", "Edge Preview"),
        (("extensions/user_default/mmd_tools/panels/sidebar/model_setup.py:150",), ()),
        ("ja_JP", "輪郭プレビュー", (False, ())),
        ("zh_HANS", "边缘预览", (False, ())),
    ),
    (
        ("*", "Material Offsets (%d)"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:103",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:103",
            ),
            (),
        ),
        ("ja_JP", "マテリアルオフセット (%d)", (False, ())),
        ("zh_HANS", "材质偏移 (%d)", (False, ())),
    ),
    (
        ("Operator", "View"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:182",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:206",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "显示", (False, ())),
    ),
    (
        ("Operator", "Clear"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:184",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:207",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:154",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:128",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "清除", (False, ())),
    ),
    (
        ("*", "Bone Offsets (%d)"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:186",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:186",
            ),
            (),
        ),
        ("ja_JP", "ボーンオフセット (%d)", (False, ())),
        ("zh_HANS", "骨骼偏移 (%d)", (False, ())),
    ),
    (
        ("Operator", "Edit"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:209",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:196",
            ),
            (),
        ),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "编辑", (False, ())),
    ),
    (
        ("*", "Group Offsets (%d)"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:226",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:226",
            ),
            (),
        ),
        ("ja_JP", "グループオフセット (%d)", (False, ())),
        ("zh_HANS", "群组偏移 (%d)", (False, ())),
    ),
    (
        ("*", "Morph Settings"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:61",), ()),
        ("ja_JP", "モーフ設定", (False, ())),
        ("zh_HANS", "变形设置", (False, ())),
    ),
    (
        ("*", "Not found"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:98",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "未找到", (False, ())),
    ),
    (
        ("*", "This is not a valid base material"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:116",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "不是有效的基础材质", (False, ())),
    ),
    (
        ("*", "Armature not found"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:178",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "找不到骨架", (False, ())),
    ),
    (
        ("Operator", "Update"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:197",), ()),
        ("ja_JP", "更新", (False, ())),
        ("zh_HANS", "更新", (False, ())),
    ),
    (
        ("*", "Scale"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:218",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "缩放", (False, ())),
    ),
    (
        ("*", "UV Offsets (%d)"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:220",
                "extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:220",
            ),
            (),
        ),
        ("ja_JP", "UVオフセット (%d)", (False, ())),
        ("zh_HANS", "UV偏移 (%d)", (False, ())),
    ),
    (
        ("Operator", "Bind morphs to .placeholder"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:326",), ()),
        ("ja_JP", "モーフを.placeholderへバインド", (False, ())),
        ("zh_HANS", "将变形绑定到.placeholder", (False, ())),
    ),
    (
        ("Operator", "Unbind morphs from .placeholder"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:327",), ()),
        ("ja_JP", "モーフを.placeholderからバインド解除", (False, ())),
        ("zh_HANS", "解绑变形与.placeholder", (False, ())),
    ),
    (
        ("*", "This offset affects all materials"),
        (("extensions/user_default/mmd_tools/panels/sidebar/morph_tools.py:125",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "该偏移影响所有材质", (False, ())),
    ),
    (
        ("Operator", "Select Similar..."),
        (("extensions/user_default/mmd_tools/panels/sidebar/rigid_bodies.py:67",), ()),
        ("ja_JP", "類似を選択...", (False, ())),
        ("zh_HANS", "选择类似...", (False, ())),
    ),
    (
        ("*", "Select Similar"),
        (("extensions/user_default/mmd_tools/panels/sidebar/rigid_bodies.py:80",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "选择类似的刚体", (False, ())),
    ),
    (
        ("*", "Model:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:29",), ()),
        ("ja_JP", "モデル:", (False, ())),
        ("zh_HANS", "模型:", (False, ())),
    ),
    (
        ("Operator", "Import"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:30",
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:35",
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:40",
            ),
            (),
        ),
        ("ja_JP", "インポート", (False, ())),
        ("zh_HANS", "导入", (False, ())),
    ),
    (
        ("Operator", "Export"),
        (
            (
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:31",
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:36",
                "extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:41",
            ),
            (),
        ),
        ("ja_JP", "エクスポート", (False, ())),
        ("zh_HANS", "导出", (False, ())),
    ),
    (
        ("*", "Motion:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:34",), ()),
        ("ja_JP", "モーション:", (False, ())),
        ("zh_HANS", "运动:", (False, ())),
    ),
    (
        ("*", "Pose:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:39",), ()),
        ("ja_JP", "ポーズ:", (False, ())),
        ("zh_HANS", "姿态:", (False, ())),
    ),
    (
        ("*", "Timeline:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:46",), ()),
        ("ja_JP", "タイムライン:", (False, ())),
        ("zh_HANS", "时间线:", (False, ())),
    ),
    (
        ("*", "Start"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:49",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "开始", (False, ())),
    ),
    (
        ("*", "End"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:50",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "结束", (False, ())),
    ),
    (
        ("*", "Rigid Body Physics:"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:58",), ()),
        ("ja_JP", "リジッドボディ物理演算:", (False, ())),
        ("zh_HANS", "刚体物理:", (False, ())),
    ),
    (
        ("Operator", "Update World"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:59",), ()),
        ("ja_JP", "ワールドを更新", (False, ())),
        ("zh_HANS", "更新世界", (False, ())),
    ),
    (
        ("Operator", "MMD Tools/Manual"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:20",), ()),
        ("ja_JP", "MMD Tools/マニュアル", (False, ())),
        ("zh_HANS", "MMD Tools/使用手册", (False, ())),
    ),
    (
        ("*", "Substeps"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:63",), ()),
        ("ja_JP", "サブステップ", (False, ())),
        ("zh_HANS", "子步数", (False, ())),
    ),
    (
        ("*", "Iterations"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:64",), ()),
        ("ja_JP", "反復数", (False, ())),
        ("zh_HANS", "迭代", (False, ())),
    ),
    (
        ("Operator", "Bake"),
        (("extensions/user_default/mmd_tools/panels/sidebar/scene_setup.py:78",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "烘焙", (False, ())),
    ),
    (
        ("*", "MMD Tools"),
        (("Add-on MMD Tools info: name",), ()),
        ("ja_JP", "MMDボーンツール", (True, ())),
        ("zh_HANS", "MMD Tools", (False, ())),
    ),
    (
        ("*", "Utility tools for MMD model editing"),
        (("Add-on MMD Tools info: description",), ()),
        ("ja_JP", "", (False, ())),
        ("zh_HANS", "MMD 模型编辑实用工具", (False, ())),
    ),
)

translations_dict = {}
for msg in translations_tuple:
    key = msg[0]
    for lang, trans, (is_fuzzy, comments) in msg[2:]:
        if trans and not is_fuzzy:
            translations_dict.setdefault(lang, {})[key] = trans

# ##### END AUTOGENERATED I18N SECTION #####

if __name__ == "__main__":
    total_count = 0
    untranslated_count = {"ja_JP": 0, "zh_HANS": 0}

    for msg in translations_tuple:
        key = msg[0]
        total_count += 1
        for lang, trans, (is_fuzzy, comments) in msg[2:]:
            if not trans:
                untranslated_count[lang] += 1
                print(f"Untranslated key {key} for language {lang}")
            elif is_fuzzy:
                untranslated_count[lang] += 1
                print(f"Fuzzy translation key {key} for language {lang}")

    for lang, cnt in untranslated_count.items():
        print(
            f"Of {total_count} entries, {cnt} have not been translated for language {lang}"
        )
