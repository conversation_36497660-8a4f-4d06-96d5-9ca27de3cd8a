from typing import Dict, Any, List, Optional, Type, Union
import traceback
from enum import Enum, auto
from dataclasses import dataclass
from datetime import datetime

from utils.MLogger import <PERSON><PERSON><PERSON>ger
from utils.MException import SizingException, MKilledException

class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    INFO = auto()        # 信息
    WARNING = auto()     # 警告
    ERROR = auto()       # 错误
    CRITICAL = auto()    # 严重错误
    FATAL = auto()       # 致命错误

@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: datetime
    component: str
    operation: str
    details: Dict[str, Any]

@dataclass
class ErrorRecord:
    """错误记录"""
    error_type: Type[Exception]
    message: str
    severity: ErrorSeverity
    context: ErrorContext
    traceback: Optional[str] = None

class ErrorHandlingService:
    def __init__(self):
        self.logger = MLogger(self.__class__.__name__)
        self.error_records: List[ErrorRecord] = []
        self.error_handlers: Dict[Type[Exception], callable] = {}
        
        # 注册默认错误处理器
        self._register_default_handlers()

    def _register_default_handlers(self) -> None:
        """注册默认的错误处理器"""
        self.register_handler(SizingException, self._handle_sizing_error)
        self.register_handler(MKilledException, self._handle_killed_error)
        self.register_handler(ValueError, self._handle_value_error)
        self.register_handler(Exception, self._handle_generic_error)

    def register_handler(self, error_type: Type[Exception], handler: callable) -> None:
        """
        注册错误处理器
        
        Args:
            error_type: 错误类型
            handler: 处理器函数
        """
        self.error_handlers[error_type] = handler

    def handle_error(
        self,
        error: Exception,
        component: str,
        operation: str,
        context_details: Dict[str, Any] = None,
        severity: Optional[ErrorSeverity] = None
    ) -> None:
        """
        处理错误
        
        Args:
            error: 异常对象
            component: 发生错误的组件名称
            operation: 发生错误时的操作
            context_details: 额外的上下文信息
            severity: 错误严重程度
        """
        try:
            # 创建错误上下文
            context = ErrorContext(
                timestamp=datetime.now(),
                component=component,
                operation=operation,
                details=context_details or {}
            )
            
            # 查找并执行对应的错误处理器
            handler = self._get_handler(error)
            handler(error, context, severity)
            
        except Exception as e:
            # 如果错误处理过程中发生错误，记录为严重错误
            self.logger.critical(f"错误处理过程中发生异常: {str(e)}\n{traceback.format_exc()}")

    def _get_handler(self, error: Exception) -> callable:
        """获取错误对应的处理器"""
        for error_type, handler in self.error_handlers.items():
            if isinstance(error, error_type):
                return handler
        return self._handle_generic_error

    def _handle_sizing_error(
        self,
        error: SizingException,
        context: ErrorContext,
        severity: Optional[ErrorSeverity] = None
    ) -> None:
        """处理尺寸相关错误"""
        record = ErrorRecord(
            error_type=type(error),
            message=str(error),
            severity=severity or ErrorSeverity.ERROR,
            context=context,
            traceback=traceback.format_exc()
        )
        self._record_and_log_error(record)

    def _handle_killed_error(
        self,
        error: MKilledException,
        context: ErrorContext,
        severity: Optional[ErrorSeverity] = None
    ) -> None:
        """处理被终止的错误"""
        record = ErrorRecord(
            error_type=type(error),
            message="操作被用户终止",
            severity=severity or ErrorSeverity.INFO,
            context=context
        )
        self._record_and_log_error(record)

    def _handle_value_error(
        self,
        error: ValueError,
        context: ErrorContext,
        severity: Optional[ErrorSeverity] = None
    ) -> None:
        """处理值错误"""
        record = ErrorRecord(
            error_type=ValueError,
            message=str(error),
            severity=severity or ErrorSeverity.ERROR,
            context=context,
            traceback=traceback.format_exc()
        )
        self._record_and_log_error(record)

    def _handle_generic_error(
        self,
        error: Exception,
        context: ErrorContext,
        severity: Optional[ErrorSeverity] = None
    ) -> None:
        """处理通用错误"""
        record = ErrorRecord(
            error_type=type(error),
            message=str(error),
            severity=severity or ErrorSeverity.CRITICAL,
            context=context,
            traceback=traceback.format_exc()
        )
        self._record_and_log_error(record)

    def _record_and_log_error(self, record: ErrorRecord) -> None:
        """记录并日志输出错误"""
        self.error_records.append(record)
        
        # 根据严重程度选择不同的日志级别
        log_message = self._format_error_message(record)
        if record.severity == ErrorSeverity.FATAL:
            self.logger.critical(log_message)
        elif record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif record.severity == ErrorSeverity.ERROR:
            self.logger.error(log_message)
        elif record.severity == ErrorSeverity.WARNING:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)

    def _format_error_message(self, record: ErrorRecord) -> str:
        """格式化错误消息"""
        message = f"[{record.severity.name}] {record.error_type.__name__}: {record.message}\n"
        message += f"组件: {record.context.component}\n"
        message += f"操作: {record.context.operation}\n"
        message += f"时间: {record.context.timestamp}\n"
        
        if record.context.details:
            message += "详细信息:\n"
            for key, value in record.context.details.items():
                message += f"  {key}: {value}\n"
        
        if record.traceback:
            message += f"\n堆栈跟踪:\n{record.traceback}"
        
        return message

    def get_error_summary(self) -> Dict[str, int]:
        """获取错误统计摘要"""
        summary = {severity.name: 0 for severity in ErrorSeverity}
        for record in self.error_records:
            summary[record.severity.name] += 1
        return summary

    def clear_records(self) -> None:
        """清除错误记录"""
        self.error_records.clear()

    def has_critical_errors(self) -> bool:
        """检查是否有严重错误"""
        return any(
            record.severity in (ErrorSeverity.CRITICAL, ErrorSeverity.FATAL)
            for record in self.error_records
        ) 