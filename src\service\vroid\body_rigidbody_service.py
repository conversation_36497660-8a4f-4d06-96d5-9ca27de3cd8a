# -*- coding: utf-8 -*-
#
import logging
import numpy as np
import math
from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel, RigidBody
from module.MMath import MVector3D, MQuaternion, MMatrix4x4
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class BodyRigidbodyService:
    def __init__(self, model: PmxModel):
        self.model = model
        self.bone_vertices = {}
        self.bone_weights = {}

    def prepare_bone_vertex_mapping(self):
        """ボーン・頂点の対応表を作成"""
        logger.info("-- 身体剛体準備開始")

        skin_vidxs = []
        cloth_vidxs = []
        for material_name, vidxs in self.model.material_vertices.items():
            if "SKIN" in self.model.materials[material_name].english_name:
                skin_vidxs.extend(vidxs)
            elif "CLOTH" in self.model.materials[material_name].english_name:
                cloth_vidxs.extend(vidxs)

        for bidx, vidxs in self.model.vertices.items():
            bone_name = str(bidx)  # Convert bidx to string
            if bone_name not in self.model.bone_indexes:
                continue
            
            bone_index = self.model.bone_indexes[bone_name]
            bone = self.model.bones[bone_index]
            target_bone_weights = {}

            bone_strong_vidxs = [
                vidx for vidx in vidxs if bone.index in self.model.vertex_dict[vidx].deform.get_idx_list(0.4)
            ]
            target_bone_vidxs = list(set(skin_vidxs) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 強参照頂点が少ない場合、弱参照頂点を確認する
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in self.model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list(set(skin_vidxs) & set(bone_weak_vidxs))

            if 20 > len(target_bone_vidxs) or "足先EX" in bone.name:
                # 弱参照肌頂点が少ない場合、衣装強参照頂点を確認する
                # 足先は靴が必ず入るので衣装も含む
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                # 衣装強参照頂点が少ない場合、衣装弱参照頂点を確認する
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_weak_vidxs))

            if 20 > len(target_bone_vidxs):
                continue

            for vidx in target_bone_vidxs:
                target_bone_weights[vidx] = self.model.vertex_dict[vidx].deform.get_weight(bone.index)

            bones = []
            if "捩" in bone.name:
                # 捩りは親に入れる
                parent_bone = self.model.bones[str(bone.parent_index)]  # Convert parent_index to string
                bones.append(parent_bone)
            elif "指" in bone.name:
                bones.append(self.model.bones[f"{bone.name[0]}手首"])
            elif "胸先" in bone.name:
                bones.append(self.model.bones[f"{bone.name[0]}胸"])
            elif "胸" in bone.name:
                bones.append(bone)
                # 胸は上半身2にも割り振る
                bones.append(self.model.bones["上半身2"])
            elif "足先EX" in bone.name:
                bones.append(self.model.bones[f"{bone.name[0]}足首"])
            elif bone.getExternalRotationFlag() and bone.effect_factor == 1:
                # 回転付与の場合、付与親に入れる(足D系)
                effect_bone = self.model.bones[str(bone.effect_index)]  # Convert effect_index to string
                bones.append(effect_bone)
            else:
                bones.append(bone)

            # 導入対象に入れる
            for bone in bones:
                if bone.name not in self.bone_vertices:
                    self.bone_vertices[bone.name] = []
                    self.bone_weights[bone.name] = {}
                self.bone_vertices[bone.name].extend(target_bone_vidxs)
                for vidx, weight in target_bone_weights.items():
                    if vidx not in self.bone_weights[bone.name]:
                        self.bone_weights[bone.name][vidx] = 0
                    self.bone_weights[bone.name][vidx] += weight

        logger.info("-- 身体剛体準備終了")

    def create_body_rigidbodies(self) -> None:
        """Create body rigidbodies"""
        logger.info("-- 身体剛体設定開始")

        # Define rigidbody pairs if not imported
        RIGIDBODY_PAIRS = {
            "頭": {"bone": "頭", "english": "head", "group": 1, "shape": 0, "size": [1.5, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "後頭部": {"bone": "頭", "english": "back_head", "group": 1, "shape": 0, "size": [1.5, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "首": {"bone": "首", "english": "neck", "group": 1, "shape": 0, "size": [1.0, 1.5, 1.0], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "上半身3": {"bone": "上半身3", "english": "upper_body3", "group": 1, "shape": 0, "size": [2.0, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "上半身2": {"bone": "上半身2", "english": "upper_body2", "group": 1, "shape": 0, "size": [2.0, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "上半身": {"bone": "上半身", "english": "upper_body", "group": 1, "shape": 0, "size": [2.0, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "下半身": {"bone": "下半身", "english": "lower_body", "group": 1, "shape": 0, "size": [2.0, 2.0, 1.5], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "左胸": {"bone": "左胸", "english": "left_breast", "group": 1, "shape": 0, "size": [1.0, 1.0, 1.0], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "右胸": {"bone": "右胸", "english": "right_breast", "group": 1, "shape": 0, "size": [1.0, 1.0, 1.0], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "左尻": {"bone": "下半身", "english": "left_hip", "group": 1, "shape": 0, "size": [1.0, 1.0, 1.0], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"},
            "右尻": {"bone": "下半身", "english": "right_hip", "group": 1, "shape": 0, "size": [1.0, 1.0, 1.0], "position": [0, 0, 0], "rotation": [0, 0, 0], "no_collision_group": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "direction": "vertical"}
        }

        for rigidbody_name, rigidbody_param in RIGIDBODY_PAIRS.items():
            try:
                # Skip if bone doesn't exist
                if rigidbody_param["bone"] not in self.model.bones:
                    logger.warning("Skipping rigidbody %s - bone %s not found", rigidbody_name, rigidbody_param["bone"])
                    continue

                no_collision_group = 0
                for nc in range(16):
                    if nc not in rigidbody_param["no_collision_group"]:
                        no_collision_group |= 1 << nc

                bone = self.model.bones[rigidbody_param["bone"]]

                # Follow bone direction
                if "手首" in bone.name:
                    # Wrist points to middle finger
                    finger_name = f"{bone.name[0]}中指先"
                    if finger_name not in self.model.bones:
                        logger.warning("Skipping rigidbody %s - finger bone %s not found", rigidbody_name, finger_name)
                        continue
                    tail_position = self.model.bones[finger_name].position
                else:
                    if bone.tail_index > 0:
                        tail_bones = [b for b in self.model.bones.values() if bone.tail_index == b.index]
                        if not tail_bones:
                            logger.warning("Skipping rigidbody %s - tail bone not found", rigidbody_name)
                            continue
                        tail_position = tail_bones[0].position
                    else:
                        tail_position = bone.tail_position + bone.position

                shape_position, shape_size, shape_rotation = self._calculate_rigidbody_shape(
                    rigidbody_name, rigidbody_param, bone, tail_position
                )

                if shape_position is None or shape_size is None or shape_rotation is None:
                    logger.warning("Skipping rigidbody %s - could not calculate shape", rigidbody_name)
                    continue

                rigidbody = RigidBody(
                    rigidbody_name,
                    rigidbody_param["english"],
                    bone.index,
                    rigidbody_param["group"],
                    no_collision_group,
                    rigidbody_param["shape"],
                    shape_size,
                    shape_position,
                    shape_rotation,
                    0,  # physics_calc_type
                    0   # bone_index
                )
                rigidbody.index = len(self.model.rigidbodies)
                self.model.rigidbodies[rigidbody.name] = rigidbody

                logger.info("-- -- Created rigidbody [%s]", rigidbody_name)

            except Exception as e:
                logger.warning("Failed to create rigidbody %s: %s", rigidbody_name, str(e))
                continue

        logger.info("-- Finished creating rigidbodies")

    def _calculate_rigidbody_shape(
        self, rigidbody_name: str, rigidbody_param: Dict[str, Any], bone: Any, tail_position: MVector3D
    ) -> Tuple[MVector3D, MVector3D, MVector3D]:
        """Calculate rigidbody shape"""
        try:
            # If bone has no vertices, use default values
            if bone.name not in self.bone_vertices or not self.bone_vertices[bone.name]:
                logger.warning("No vertices found for bone %s, using default values", bone.name)
                return (
                    bone.position,  # position at bone origin
                    MVector3D(*rigidbody_param["size"]),  # default size
                    MVector3D(0, 0, 0)  # no rotation
                )

            if rigidbody_param["direction"] == "horizonal":
                # Bone direction (x)
                x_direction_pos = MVector3D(1, 0, 0)
                # Horizontal axis relative to bone direction (y)
                y_direction_pos = MVector3D(0, 1, 0)
            else:
                # Calculate bone direction from vertices if tail position is at origin
                if tail_position.length() < 0.001:
                    # Find the furthest vertex in the bone's local space
                    max_dist = 0
                    furthest_pos = None
                    for vidx in self.bone_vertices[bone.name]:
                        vertex = self.model.vertex_dict[vidx]
                        local_pos = vertex.position - bone.position
                        dist = local_pos.length()
                        if dist > max_dist:
                            max_dist = dist
                            furthest_pos = vertex.position
                    
                    if furthest_pos:
                        x_direction_pos = (furthest_pos - bone.position).normalized()
                    else:
                        x_direction_pos = MVector3D(0, 1, 0)  # Default to up if no vertices
                else:
                    x_direction_pos = (tail_position - bone.position).normalized()
                
                # Horizontal axis relative to bone direction (y)
                y_direction_pos = MVector3D(1, 0, 0)

            # Vertical axis relative to bone direction (z)
            z_direction_pos = MVector3D.crossProduct(x_direction_pos, y_direction_pos)
            bone_shape_qq = MQuaternion.fromDirection(z_direction_pos, x_direction_pos)

            # Get vertex positions in bone space
            vposes = []
            vweights = []
            for vidx in self.bone_vertices[bone.name]:
                vertex = self.model.vertex_dict[vidx]
                vposes.append(vertex.position.data())
                vweights.append(self.bone_weights[bone.name][vidx])

            # Calculate shape size and position
            shape_size = MVector3D(*rigidbody_param["size"])  # Start with default size
            if vposes:
                # Calculate bounding box in bone space
                local_positions = [(MVector3D(*pos) - bone.position) for pos in vposes]
                rotated_positions = [bone_shape_qq.inverse() * pos for pos in local_positions]
                
                min_pos = MVector3D(float('inf'), float('inf'), float('inf'))
                max_pos = MVector3D(float('-inf'), float('-inf'), float('-inf'))
                for pos in rotated_positions:
                    min_pos.x = min(min_pos.x, pos.x)
                    min_pos.y = min(min_pos.y, pos.y)
                    min_pos.z = min(min_pos.z, pos.z)
                    max_pos.x = max(max_pos.x, pos.x)
                    max_pos.y = max(max_pos.y, pos.y)
                    max_pos.z = max(max_pos.z, pos.z)
                
                # Calculate size and center position
                shape_size = MVector3D(
                    max(0.1, (max_pos.x - min_pos.x) * 0.5),
                    max(0.1, (max_pos.y - min_pos.y) * 0.5),
                    max(0.1, (max_pos.z - min_pos.z) * 0.5)
                )
                center_pos = (min_pos + max_pos) * 0.5
                shape_position = bone.position + (bone_shape_qq * center_pos)
            else:
                shape_position = bone.position

            # Calculate rotation in radians
            shape_rotation = MVector3D(0, 0, 0)
            if bone_shape_qq:
                euler = bone_shape_qq.toEulerAngles()
                shape_rotation = MVector3D(
                    math.radians(euler.x()),
                    math.radians(euler.y()),
                    math.radians(euler.z())
                )

            return shape_position, shape_size, shape_rotation

        except Exception as e:
            logger.warning("Error calculating rigidbody shape for %s: %s", rigidbody_name, str(e))
            return None, None, None

    def _get_target_vertices(
        self, range_type: str, vposes: List[List[float]], vweights: List[float], bone_shape_qq: MQuaternion
    ) -> Tuple[List[List[float]], List[float]]:
        """対象頂点を取得"""
        if range_type not in ["upper", "lower"]:
            return vposes, vweights

        # 重心
        gravity_pos = MVector3D(np.average(vposes, axis=0, weights=vweights))

        mat = MMatrix4x4()
        mat.setToIdentity()
        mat.translate(gravity_pos)
        mat.rotate(bone_shape_qq)

        # 上下に分ける系はローカル位置で分ける
        local_vposes = np.array([(mat.inverted() * MVector3D(vpos)).data() for vpos in vposes])

        # 中央値
        mean_y = np.mean(local_vposes, axis=0)[1]

        target_vposes = []
        target_vweights = []
        for vpos, vweight in zip(local_vposes, vweights):
            if (vpos[1] >= mean_y and range_type == "upper") or (vpos[1] <= mean_y and range_type == "lower"):
                target_vposes.append((mat * MVector3D(vpos)).data())
                target_vweights.append(vweight)

        return target_vposes, target_vweights

    def _adjust_shape_position(
        self, rigidbody_name: str, shape_position: MVector3D, x_size: float, 
        target_vposes: List[List[float]], target_vweights: List[float], bone: Any
    ) -> MVector3D:
        """形状位置を調整"""
        if rigidbody_name in ["上半身2", "上半身3"]:
            # ちょっと後ろにずらす
            shape_position.setZ(shape_position.z() + (x_size * 0.5))

        if rigidbody_param["shape"] == 0:
            # 球剛体はバウンティングボックスの中心
            shape_position = MVector3D(
                np.mean([np.max(target_vposes, axis=0), np.min(target_vposes, axis=0)], axis=0)
            )
            if "尻" in rigidbody_name:
                shape_position = MVector3D(
                    np.average(
                        [self.model.bones["下半身"].position.data(), self.model.bones[f"{rigidbody_name[0]}足"].position.data()],
                        axis=0,
                        weights=[0.3, 0.7],
                    )
                )
            elif "胸" in rigidbody_name:
                shape_position = MVector3D(
                    np.average(
                        [shape_position.data(), self.model.bones[f"{rigidbody_name[0]}胸"].position.data()],
                        axis=0,
                        weights=[0.3, 0.7],
                    )
                )
            elif "後頭部" in rigidbody_name:
                shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))
                shape_position.setZ(shape_position.z() + (x_size * 0.3))
            elif "頭" in rigidbody_name:
                shape_position = MVector3D(np.average(target_vposes, axis=0, weights=target_vweights))
                shape_position.setY(shape_position.y() + (x_size * 0.3))
                shape_position.setZ(shape_position.z() - (x_size * 0.1))

        if "足首" in rigidbody_name or "首" == rigidbody_name or "太もも" in rigidbody_name:
            mat = MMatrix4x4()
            mat.setToIdentity()
            mat.translate(shape_position)
            mat.rotate(bone_shape_qq)

            if "足首" in rigidbody_name:
                shape_position = mat * MVector3D(0, -y_size * 0.15, x_size * 0.2)
            elif "首" == rigidbody_name:
                shape_position = mat * MVector3D(0, 0, x_size * 0.3)
            elif "太もも" in rigidbody_name:
                shape_position = mat * MVector3D(x_size * 0.1 * np.sign(bone.position.x()), 0, -x_size * 0.1)

        return shape_position

# 剛体のパラメータ定義
RIGIDBODY_PAIRS = {
    "頭": {
        "english": "Head",
        "bone": "頭",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "後頭部": {
        "english": "Back Head",
        "bone": "頭",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "首": {
        "english": "Neck",
        "bone": "首",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "上半身3": {
        "english": "Upper Body3",
        "bone": "上半身3",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "上半身2": {
        "english": "Upper Body2",
        "bone": "上半身2",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "上半身": {
        "english": "Upper Body",
        "bone": "上半身",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "下半身": {
        "english": "Lower Body",
        "bone": "下半身",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左胸": {
        "english": "Left Bust",
        "bone": "左胸",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右胸": {
        "english": "Right Bust",
        "bone": "右胸",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左尻": {
        "english": "Left Hip",
        "bone": "下半身",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右尻": {
        "english": "Right Hip",
        "bone": "下半身",
        "shape": 0,  # 球
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左太もも": {
        "english": "Left Thigh",
        "bone": "左足",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": "upper",
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右太もも": {
        "english": "Right Thigh",
        "bone": "右足",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": "upper",
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左ひざ": {
        "english": "Left Knee",
        "bone": "左ひざ",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右ひざ": {
        "english": "Right Knee",
        "bone": "右ひざ",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左足首": {
        "english": "Left Ankle",
        "bone": "左足首",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右足首": {
        "english": "Right Ankle",
        "bone": "右足首",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左腕": {
        "english": "Left Arm",
        "bone": "左腕",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右腕": {
        "english": "Right Arm",
        "bone": "右腕",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左ひじ": {
        "english": "Left Elbow",
        "bone": "左ひじ",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右ひじ": {
        "english": "Right Elbow",
        "bone": "右ひじ",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "左手首": {
        "english": "Left Wrist",
        "bone": "左手首",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
    "右手首": {
        "english": "Right Wrist",
        "bone": "右手首",
        "shape": 2,  # カプセル
        "group": 1,
        "no_collision_group": [0, 12, 13, 14],
        "direction": "vertical",
        "range": None,
        "ratio": MVector3D(1.1, 1.1, 1.1),
    },
} 