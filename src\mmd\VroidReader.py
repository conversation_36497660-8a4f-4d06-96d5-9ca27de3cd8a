# -*- coding: utf-8 -*-
#
import hashlib
import json
import struct
import os
import numpy as np

from mmd.PmxReader import PmxReader
from mmd.PmxData import PmxModel, Bone, RigidBody, Vertex, Material, Morph, DisplaySlot, RigidBody, Joint, Ik, IkLink, Bdef1, Bdef2, Bdef4, Sdef, Qdef, MaterialMorphData, UVMorphData, BoneMorphData, VertexMorphOffset, GroupMorphData # noqa
from module.MMath import MRect, MVector2D, MVector3D, MVector4D, MQuaternion, MMatrix4x4 # noqa
from service.vroid.constants import BONE_PAIRS
from service.vroid.bone_converter import BoneConverter
from utils.MLogger import MLogger # noqa
from utils.MException import SizingException, MKilledException, MParseException     # noqa

logger = MLogger(__name__, level=1)


class VroidReader(PmxReader):
    def __init__(self, file_path, options=None):
        self.file_path = file_path
        self.offset = 0
        self.buffer = None
        self.options = options
        self.logger = MLogger(__name__)

    def read_model_name(self):
        return ""

    def read_data(self):
        # Create Pmx model
        pmx = PmxModel()
        pmx.path = self.file_path

        try:
            if not os.path.exists(self.file_path):
                raise MParseException(f"VRM file not found: {self.file_path}")

            file_size = os.path.getsize(self.file_path)
            if file_size < 20:  # Minimum size for header
                raise MParseException(f"Invalid VRM file - file too small ({file_size} bytes)")

            with open(self.file_path, "rb") as f:
                # Read GLB header
                magic = f.read(4)
                if magic != b'glTF':
                    raise MParseException("Invalid VRM file - not a glTF/VRM file")

                version = struct.unpack('<I', f.read(4))[0]
                length = struct.unpack('<I', f.read(4))[0]

                # Read JSON chunk
                json_chunk_length = struct.unpack('<I', f.read(4))[0]
                json_chunk_type = f.read(4)
                if json_chunk_type != b'JSON':
                    raise MParseException("Invalid VRM file - JSON chunk not found")

                json_data = f.read(json_chunk_length)
                try:
                    pmx.json_data = json.loads(json_data.decode('utf-8'))
                except UnicodeDecodeError:
                    # Try different encodings
                    for encoding in ['shift-jis', 'cp932', 'utf-8-sig']:
                        try:
                            pmx.json_data = json.loads(json_data.decode(encoding))
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        # If all encodings fail, try with ignore option
                        pmx.json_data = json.loads(json_data.decode('utf-8', errors='ignore'))

                # Initialize empty buffers if not present
                if not pmx.json_data.get("buffers"):
                    pmx.json_data["buffers"] = []
                if not pmx.json_data.get("bufferViews"):
                    pmx.json_data["bufferViews"] = []
                if not pmx.json_data.get("accessors"):
                    pmx.json_data["accessors"] = []
                if not pmx.json_data.get("nodes"):
                    pmx.json_data["nodes"] = []

                # Try to read BIN chunk if present
                try:
                    bin_chunk_length = struct.unpack('<I', f.read(4))[0]
                    bin_chunk_type = f.read(4)
                    if bin_chunk_type == b'BIN\x00':
                        bin_data = f.read(bin_chunk_length)
                        if len(bin_data) != bin_chunk_length:
                            logger.warning(f"Incomplete BIN chunk - expected {bin_chunk_length} bytes, got {len(bin_data)}")
                        pmx.buffers = [bin_data]
                    else:
                        logger.warning("No BIN chunk found, using empty buffer")
                        pmx.buffers = [b'']
                except Exception as e:
                    logger.warning(f"Error reading BIN chunk: {str(e)}")
                    pmx.buffers = [b'']

                # Process buffer views
                pmx.buffer_views = []
                for idx, buffer_view in enumerate(pmx.json_data.get("bufferViews", [])):
                    if not isinstance(buffer_view, dict):
                        logger.warning(f"Invalid bufferView at index {idx}, skipping")
                        continue

                    try:
                        offset = buffer_view.get("byteOffset", 0)
                        length = buffer_view.get("byteLength", 0)
                        stride = buffer_view.get("byteStride", 0)

                        if offset + length > len(pmx.buffers[0]):
                            logger.warning(f"Buffer view {idx} extends beyond buffer data, using empty data")
                            view_data = {
                                "data": b'',
                                "byteOffset": offset,
                                "byteLength": length,
                                "byteStride": stride
                            }
                        else:
                            view_data = {
                                "data": pmx.buffers[0][offset:offset + length],
                                "byteOffset": offset,
                                "byteLength": length,
                                "byteStride": stride
                            }
                        pmx.buffer_views.append(view_data)
                        logger.debug(f"Processed buffer view {idx}: offset={offset}, length={length}, stride={stride}")
                    except Exception as e:
                        logger.warning(f"Error processing buffer view {idx}: {str(e)}")
                        pmx.buffer_views.append({
                            "data": b'',
                            "byteOffset": 0,
                            "byteLength": 0,
                            "byteStride": 0
                        })

                # Process accessors
                pmx.accessors = []
                for idx, accessor in enumerate(pmx.json_data.get("accessors", [])):
                    if not isinstance(accessor, dict):
                        logger.warning(f"Invalid accessor at index {idx}, skipping")
                        continue

                    try:
                        buffer_view_idx = accessor.get("bufferView", -1)
                        if buffer_view_idx < 0 or buffer_view_idx >= len(pmx.buffer_views):
                            logger.warning(f"Accessor {idx} references invalid bufferView, using empty data")
                            accessor_data = {
                                "bufferView": -1,
                                "componentType": 5126,  # FLOAT
                                "count": 0,
                                "type": "SCALAR",
                                "byteOffset": 0,
                                "data": b'',
                                "stride": 0
                            }
                        else:
                            buffer_view = pmx.buffer_views[buffer_view_idx]
                            component_type = accessor.get("componentType", 5126)  # Default to FLOAT
                            count = accessor.get("count", 0)
                            type_str = accessor.get("type", "SCALAR")
                            byte_offset = accessor.get("byteOffset", 0)

                            accessor_data = {
                                "bufferView": buffer_view_idx,
                                "componentType": component_type,
                                "count": count,
                                "type": type_str,
                                "byteOffset": byte_offset,
                                "data": buffer_view["data"],
                                "stride": buffer_view["byteStride"]
                            }
                        pmx.accessors.append(accessor_data)
                        logger.debug(f"Processed accessor {idx}: type={accessor_data['type']}, count={accessor_data['count']}")
                    except Exception as e:
                        logger.warning(f"Error processing accessor {idx}: {str(e)}")
                        pmx.accessors.append({
                            "bufferView": -1,
                            "componentType": 5126,
                            "count": 0,
                            "type": "SCALAR",
                            "byteOffset": 0,
                            "data": b'',
                            "stride": 0
                        })

                # Use BoneConverter to handle bones
                bone_converter = BoneConverter(self.options)
                pmx, bone_dict = bone_converter.convert_bone(pmx)
                
                if not pmx or not bone_dict:
                    raise MParseException("Failed to convert bones")

                # Set hash
                pmx.digest = self.hexdigest()
                self.logger.debug("pmx: %s, hash: %s", pmx.name, pmx.digest)

                return pmx

        except MKilledException as ke:
            raise ke
        except SizingException as se:
            self.logger.error("VRM loading failed due to invalid data size.\n\n%s", se.message, decoration=MLogger.DECORATION_BOX)
            return se
        except MParseException as pe:
            self.logger.error("VRM loading failed.\n\n%s", str(pe), decoration=MLogger.DECORATION_BOX)
            raise pe
        except Exception as e:
            import traceback
            self.logger.error("VRM loading failed due to unexpected error.\n\n%s", traceback.format_exc(), decoration=MLogger.DECORATION_BOX)
            raise e

    def read_text(self, format_size):
        bresult = self.unpack(format_size, "{0}s".format(format_size))
        
        # Try encodings in order of likelihood
        encodings = ['utf-8', 'shift-jis', 'cp932', 'utf-8-sig', 'gbk', 'big5', 'euc-jp', 'euc-kr', 'latin1']
        
        for encoding in encodings:
            try:
                self.logger.debug(f"Trying to decode with {encoding}")
                return bresult.decode(encoding)
            except UnicodeDecodeError:
                continue
                
        # If all encodings fail, try UTF-8 with error handling
        return bresult.decode('utf-8', errors='ignore')

    def unpack(self, format_size, format):
        try:
            bresult = struct.unpack_from(format, self.buffer, self.offset)
        except struct.error as e:
            raise MParseException(f"Failed to unpack data: {str(e)}")

        # Update offset
        self.offset += format_size

        if bresult:
            result = bresult[0]
        else:
            result = None

        return result
