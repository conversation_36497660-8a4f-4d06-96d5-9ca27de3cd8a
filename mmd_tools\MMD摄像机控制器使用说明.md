# MMD摄像机控制器使用说明

这是一个用于UPBGE游戏引擎的MMD摄像机控制组件，可以实现MMD父子摄像机的动画控制。

## 功能特点

- 支持MMD父子摄像机结构（Empty父对象 + Camera子对象）
- 每帧更新动画数据，实现平滑的摄像机运动
- 内置示例动画（360度环绕运动）
- 支持加载自定义动画数据
- 提供播放控制功能（播放、暂停、停止、跳转）
- 支持动画循环和速度调节

## 安装使用

### 1. 在Blender中设置MMD摄像机结构

首先需要在Blender场景中创建MMD摄像机结构：

```python
# 在Blender中运行此脚本创建MMD摄像机
import bpy
import mathutils

# 创建父对象（Empty）
parent = bpy.data.objects.new(name="MMD_Camera", object_data=None)
bpy.context.collection.objects.link(parent)
parent.location = (0, 0, 10)
parent.rotation_mode = 'YXZ'

# 创建摄像机对象
camera_data = bpy.data.cameras.new("Camera")
camera = bpy.data.objects.new("Camera", camera_data)
bpy.context.collection.objects.link(camera)

# 设置父子关系
camera.parent = parent
camera.location = (0, -45, 0)  # 距离设置
camera.rotation_mode = 'XYZ'
camera.rotation_euler = (mathutils.radians(90), 0, 0)

# 锁定变换
camera.lock_location = (True, False, True)
camera.lock_rotation = (True, True, True)
camera.lock_scale = (True, True, True)
```

### 2. 将脚本添加到UPBGE项目

1. 将`mmd_camera_controller.py`复制到你的UPBGE项目文件夹
2. 在UPBGE中打开你的项目
3. 选择任意游戏对象（建议选择场景中的控制器对象）
4. 在Properties面板中找到Logic Properties
5. 添加新的Python Component，选择`MMDCameraController`

### 3. 配置组件参数

在组件参数中可以设置：

- `animation_speed`: 动画播放速度（默认1.0）
- `loop_animation`: 是否循环播放（默认True）
- `auto_start`: 是否自动开始播放（默认True）
- `camera_name`: 摄像机对象名称（默认"Camera"）
- `parent_name`: 父对象名称（默认"MMD_Camera"）

## API使用说明

### 基本控制方法

```python
# 获取组件引用
controller = obj['MMDCameraController']

# 播放控制
controller.play()        # 开始播放
controller.pause()       # 暂停播放
controller.stop()        # 停止并重置

# 帧控制
controller.set_frame(100)     # 跳转到第100帧
controller.set_speed(2.0)     # 设置播放速度为2倍

# 加载自定义动画数据
custom_data = create_sample_vmd_data()
controller.load_animation_data(custom_data)
```

### 自定义动画数据格式

动画数据是一个包含帧信息的列表，每个帧包含以下字段：

```python
frame_data = {
    'frame': 0,                                    # 帧号
    'location': mathutils.Vector((x, y, z)),       # 父对象位置
    'rotation': mathutils.Euler((rx, ry, rz)),     # 父对象旋转（欧拉角）
    'distance': -45.0,                             # 摄像机距离（负值）
    'fov': math.radians(30),                       # 视野角度（弧度）
    'is_perspective': True                         # 是否透视模式
}
```

### 从VMD文件加载数据

如果你有真实的VMD摄像机动画文件，可以使用项目中的VMD导入功能：

```python
def load_vmd_camera_data(vmd_file_path):
    """从VMD文件加载摄像机动画数据"""
    from core.vmd import vmd
    
    # 加载VMD文件
    vmd_file = vmd.File()
    vmd_file.load(filepath=vmd_file_path)
    
    # 转换为组件可用的格式
    animation_data = []
    for camera_frame in vmd_file.cameraAnimation:
        frame_data = {
            'frame': camera_frame.frame_number,
            'location': mathutils.Vector(camera_frame.location),
            'rotation': mathutils.Euler(camera_frame.rotation),
            'distance': camera_frame.distance,
            'fov': math.radians(camera_frame.angle),
            'is_perspective': camera_frame.persp
        }
        animation_data.append(frame_data)
    
    return animation_data

# 使用示例
vmd_data = load_vmd_camera_data("camera_animation.vmd")
controller.load_animation_data(vmd_data)
```

## 示例场景设置

### 1. 基础环绕摄像机

默认的示例动画创建了一个360度环绕的摄像机运动，适用于展示场景中的模型。

### 2. 自定义路径摄像机

```python
def create_custom_path():
    """创建自定义路径动画"""
    frames = []
    waypoints = [
        (0, 0, 5),     # 起点
        (10, 10, 8),   # 中点1
        (-5, 15, 6),   # 中点2
        (0, 0, 5)      # 终点
    ]
    
    total_frames = 200
    for i in range(total_frames):
        progress = i / (total_frames - 1)
        
        # 简单的路径插值
        segment = int(progress * (len(waypoints) - 1))
        local_progress = (progress * (len(waypoints) - 1)) - segment
        
        if segment >= len(waypoints) - 1:
            pos = mathutils.Vector(waypoints[-1])
        else:
            start_pos = mathutils.Vector(waypoints[segment])
            end_pos = mathutils.Vector(waypoints[segment + 1])
            pos = start_pos.lerp(end_pos, local_progress)
        
        # 始终看向原点
        direction = (mathutils.Vector((0, 0, 0)) - pos).normalized()
        
        frame_data = {
            'frame': i,
            'location': pos,
            'rotation': direction.to_track_quat('-Z', 'Y').to_euler(),
            'distance': -45.0,
            'fov': math.radians(30),
            'is_perspective': True
        }
        frames.append(frame_data)
    
    return frames
```

## 注意事项

1. **摄像机结构**: 确保场景中有正确的MMD父子摄像机结构
2. **坐标系**: UPBGE使用的坐标系可能与Blender略有不同，注意调整
3. **性能**: 对于复杂的动画数据，考虑优化插值计算
4. **帧率**: 动画数据通常假设30fps，根据你的游戏帧率调整播放速度

## 故障排除

- **摄像机不动**: 检查对象名称是否正确，确保父子关系设置正确
- **动画跳跃**: 可能是旋转插值问题，考虑使用四元数而不是欧拉角
- **视野不变**: 确保摄像机对象有lens属性可供修改

## 扩展功能

这个组件可以很容易地扩展以支持：

- 多摄像机切换
- 动画混合
- 实时摄像机控制
- 与MMD模型动画同步
- 摄像机震动效果 