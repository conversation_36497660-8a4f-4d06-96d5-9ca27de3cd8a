from typing import Dict, List, Tuple, Set
from mmd.PmxData import PmxModel, Bone
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class VertexService:
    @staticmethod
    def get_material_vertices(model: PmxModel) -> <PERSON><PERSON>[List[int], List[int]]:
        """获取材质对应的顶点索引

        Args:
            model (PmxModel): PMX模型

        Returns:
            Tuple[List[int], List[int]]: 皮肤顶点和衣物顶点的索引列表
        """
        skin_vidxs = []
        cloth_vidxs = []
        for material_name, vidxs in model.material_vertices.items():
            if "SKIN" in model.materials[material_name].english_name:
                skin_vidxs.extend(vidxs)
            elif "CLOTH" in model.materials[material_name].english_name:
                cloth_vidxs.extend(vidxs)
        return skin_vidxs, cloth_vidxs

    @staticmethod
    def process_bone_vertices(
        model: PmxModel,
        skin_vidxs: List[int],
        cloth_vidxs: List[int]
    ) -> <PERSON><PERSON>[Dict[str, List[int]], Dict[str, Dict[int, float]]]:
        """处理骨骼对应的顶点

        Args:
            model (PmxModel): PMX模型
            skin_vidxs (List[int]): 皮肤顶点索引列表
            cloth_vidxs (List[int]): 衣物顶点索引列表

        Returns:
            Tuple[Dict[str, List[int]], Dict[str, Dict[int, float]]]: 骨骼顶点映射和骨骼权重映射
        """
        bone_vertices = {}
        bone_weights = {}

        for bidx, vidxs in model.vertices.items():
            bone = model.bones[model.bone_indexes[bidx]]
            target_bone_weights = {}

            # 获取强权重顶点
            bone_strong_vidxs = [
                vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.4)
            ]
            target_bone_vidxs = list(set(skin_vidxs) & set(bone_strong_vidxs))

            if len(target_bone_vidxs) < 20:
                # 如果强权重顶点太少，检查弱权重顶点
                bone_weak_vidxs = [
                    vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.2)
                ]
                target_bone_vidxs = list(set(skin_vidxs) & set(bone_weak_vidxs))

            if len(target_bone_vidxs) < 20 or "足先EX" in bone.name:
                # 如果弱权重皮肤顶点太少，或者是脚趾骨骼，检查衣物顶点
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_strong_vidxs))

            if len(target_bone_vidxs) < 20:
                # 如果衣物强权重顶点太少，检查衣物弱权重顶点
                target_bone_vidxs = list((set(skin_vidxs) | set(cloth_vidxs)) & set(bone_weak_vidxs))

            if len(target_bone_vidxs) < 20:
                continue

            # 计算顶点权重
            for vidx in target_bone_vidxs:
                target_bone_weights[vidx] = model.vertex_dict[vidx].deform.get_weight(bone.index)

            # 处理特殊骨骼
            bones = VertexService._get_special_bones(model, bone)

            # 添加到映射中
            for bone in bones:
                if bone.name not in bone_vertices:
                    bone_vertices[bone.name] = []
                    bone_weights[bone.name] = {}
                bone_vertices[bone.name].extend(target_bone_vidxs)
                for vidx, weight in target_bone_weights.items():
                    if vidx not in bone_weights[bone.name]:
                        bone_weights[bone.name][vidx] = 0
                    bone_weights[bone.name][vidx] += weight

        return bone_vertices, bone_weights

    @staticmethod
    def _get_special_bones(model: PmxModel, bone: Bone) -> List[Bone]:
        """获取特殊骨骼列表

        Args:
            model (PmxModel): PMX模型
            bone (Bone): 骨骼

        Returns:
            List[Bone]: 特殊骨骼列表
        """
        bones = []
        if "捩" in bone.name:
            # 扭转骨骼添加到父骨骼
            bones.append(model.bones[model.bone_indexes[bone.parent_index]])
        elif "指" in bone.name:
            # 手指骨骼添加到手腕
            bones.append(model.bones[f"{bone.name[0]}手首"])
        elif "胸先" in bone.name:
            # 胸部末端骨骼添加到胸部
            bones.append(model.bones[f"{bone.name[0]}胸"])
        elif "胸" in bone.name:
            # 胸部骨骼同时添加到自身和上半身2
            bones.append(bone)
            bones.append(model.bones["上半身2"])
        elif "足先EX" in bone.name:
            # 脚趾骨骼添加到脚踝
            bones.append(model.bones[f"{bone.name[0]}足首"])
        elif bone.getExternalRotationFlag() and bone.effect_factor == 1:
            # 旋转约束骨骼添加到约束目标（足D系列）
            bones.append(model.bones[model.bone_indexes[bone.effect_index]])
        else:
            bones.append(bone)

        return bones 