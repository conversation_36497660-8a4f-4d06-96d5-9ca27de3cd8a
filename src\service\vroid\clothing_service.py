# -*- coding: utf-8 -*-
#
import logging
import json
import os
from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class ClothingService:
    def __init__(self, model: PmxModel):
        self.model = model
        self.material_bones = {}

    def prepare_material_bone_mapping(self, material_bones: Dict[str, List[int]]):
        """材質・ボーンの対応表を設定"""
        self.material_bones = material_bones

    def create_clothing_settings(self, setting_dir_path: str) -> None:
        """衣服の設定を作成"""
        logger.info("-- 衣服の設定作成")
        
        CLOTHING_COATSKIRT = logger.transtext("単一揺れ物")
        CLOTHING_SKIRT = logger.transtext("単一揺れ物")
        CLOTHING_COAT = logger.transtext("単一揺れ物")
        clothing_cnt = {"_CoatSkirt": 1, "_Skirt": 1, "_Coat": 1}
        pmx_tailor_settings = {}

        for weighted_material_name, cbones in self.material_bones.items():
            if "CLOTH" not in weighted_material_name or not cbones:
                continue

            logger.debug("weighted_material_name: %s", weighted_material_name)

            for target_name, target_primitive, target_group, target_abb in (
                ("_CoatSkirt", CLOTHING_COATSKIRT, 7, "CS"),
                ("_Skirt", CLOTHING_SKIRT, 8, "SK"),
                ("_Coat", CLOTHING_COAT, 9, "CT"),
            ):
                if [
                    bidx
                    for bidx in cbones
                    if target_name in self.model.bone_indexes[bidx] and self.model.bone_indexes[bidx] in self.model.vertices
                ]:
                    settings = self._create_clothing_part_settings(
                        weighted_material_name, target_name, target_primitive, target_group, target_abb, cbones, clothing_cnt
                    )
                    if settings:
                        pmx_tailor_settings.update(settings)
                    clothing_cnt[target_name] += 1

        self._save_clothing_settings(pmx_tailor_settings, setting_dir_path)

    def _create_clothing_part_settings(
        self, weighted_material_name: str, target_name: str, target_primitive: str, target_group: int, 
        target_abb: str, cbones: List[int], clothing_cnt: Dict[str, int]
    ) -> Dict[str, Any]:
        """衣服パーツの設定を作成"""
        material_name = self.model.materials[weighted_material_name].name
        back_material_names = self._get_back_material_names(material_name)
        settings = {}

        for direction, parent_bone_name in (("L", "左足"), ("R", "右足")):
            target_bones = []

            for nidx, vertical_name in enumerate(
                [
                    f"{direction}{target_name}Back",
                    f"{direction}{target_name}Side",
                    f"{direction}{target_name}Front",
                ]
            ):
                is_reset = True
                for bidx in sorted(cbones):
                    bname = self.model.bone_indexes[bidx]
                    if vertical_name in bname:
                        if is_reset:
                            target_bones.append([])
                            is_reset = False
                        target_bones[-1].append(bname)

                if target_bones and target_bones[-1]:
                    while self.model.bones[target_bones[-1][-1]].tail_index >= 0:
                        # 末端ボーンまでを入れる
                        target_bones[-1].append(
                            self.model.bone_indexes[self.model.bones[target_bones[-1][-1]].tail_index]
                        )

            if target_name == "_Coat" and not target_bones:
                # CoatはCoatSkirtと被るので、うまく取れなければスルー
                continue

            settings[
                (
                    target_name,
                    direction,
                    clothing_cnt[target_name],
                    target_primitive,
                    weighted_material_name,
                )
            ] = {
                "material_name": material_name,
                "abb_name": f"{target_abb}{direction}{clothing_cnt[target_name]}",
                "parent_bone_name": parent_bone_name,
                "group": str(target_group),
                "direction": logger.transtext("下"),
                "primitive": target_primitive,
                "exist_physics_clear": logger.transtext("再利用"),
                "target_bones": target_bones,
                "back_extend_material_names": back_material_names,
                "rigidbody_root_thick": 0.35,
                "rigidbody_end_thick": 0.5,
            }

            logger.info(
                "-- -- 衣服の設定作成 (%s)", ", ".join((target_name, direction, material_name))
            )

        return settings

    def _get_back_material_names(self, material_name: str) -> List[str]:
        """裏面材質名リストを取得"""
        back_material_names = []
        if not material_name:
            return back_material_names
            
        if f"{material_name}_エッジ" in self.model.materials:
            back_material_names.append(f"{material_name}_エッジ")
        if f"{material_name}_裏" in self.model.materials:
            back_material_names.append(f"{material_name}_裏")
        return back_material_names

    def _save_clothing_settings(self, pmx_tailor_settings: Dict[str, Any], setting_dir_path: str) -> None:
        """衣服の設定を保存"""
        for setting_name, pmx_tailor_setting in pmx_tailor_settings.items():
            if pmx_tailor_setting["material_name"] and pmx_tailor_setting["target_bones"]:
                with open(
                    os.path.join(setting_dir_path, f"{pmx_tailor_setting['abb_name']}.json"), "w", encoding="utf-8"
                ) as jf:
                    json.dump(pmx_tailor_setting, jf, ensure_ascii=False, indent=4, separators=(",", ": "))
            else:
                logger.warning(
                    "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s",
                    setting_name,
                    pmx_tailor_setting["material_name"],
                    pmx_tailor_setting["target_bones"],
                ) 