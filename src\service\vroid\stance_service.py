# -*- coding: utf-8 -*-
#
import logging
import numpy as np
from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel, Bdef1, Bdef2, Bdef4, Vertex
from mmd.VmdData import VmdMotion
from module.MMath import MVector3D, MQuaternion, MMatrix4x4
from module.MParams import BoneLinks
from utils.MLogger import <PERSON><PERSON>ogger
from utils import MServiceUtils
from service.vroid.constants import BONE_PAIRS

logger = MLogger(__name__, level=1)

class StanceService:
    def __init__(self, model: PmxModel):
        self.model = model
        self.BONE_PAIRS = BONE_PAIRS

    def transfer_stance(self) -> PmxModel:
        """姿勢を変更"""
        logger.info("-- 姿勢変更開始")

        # 各頂点の相対位置を計算
        all_vertex_relative_poses = self._calculate_vertex_relative_poses()

        # ボーンの変換行列を計算
        trans_bone_vecs, trans_bone_mats = self._calculate_bone_transforms()

        # ボーンの位置を更新
        for bone_name, bone_vec in trans_bone_vecs.items():
            self.model.bones[bone_name].position = bone_vec

        # ボーンのローカル軸を更新
        self._update_bone_local_axes(trans_bone_mats)

        # 頂点位置を更新
        self._update_vertex_positions(all_vertex_relative_poses, trans_bone_mats)

        logger.info("-- 姿勢変更終了")

        return self.model

    def _calculate_vertex_relative_poses(self) -> Dict[int, List[MVector3D]]:
        """頂点の相対位置を計算"""
        all_vertex_relative_poses = {}
        for vertex in self.model.vertex_dict.values():
            if not hasattr(vertex, 'deform') or vertex.deform is None:
                logger.warning(f"Vertex {vertex.index} has no deform attribute or deform is None")
                continue

            if type(vertex.deform) is Bdef1:
                bone_index = vertex.deform.bone_index
                if bone_index in self.model.bones:
                    all_vertex_relative_poses[vertex.index] = [
                        vertex.position - self.model.bones[bone_index].position
                    ]
            elif type(vertex.deform) is Bdef2:
                if vertex.deform.bone_index1 in self.model.bones and vertex.deform.bone_index2 in self.model.bones:
                    all_vertex_relative_poses[vertex.index] = [
                        vertex.position - self.model.bones[vertex.deform.bone_index1].position,
                        vertex.position - self.model.bones[vertex.deform.bone_index2].position,
                    ]
            elif type(vertex.deform) is Bdef4:
                if all(idx in self.model.bones for idx in [vertex.deform.bone_index1, vertex.deform.bone_index2, vertex.deform.bone_index3, vertex.deform.bone_index4]):
                    all_vertex_relative_poses[vertex.index] = [
                        vertex.position - self.model.bones[vertex.deform.bone_index1].position,
                        vertex.position - self.model.bones[vertex.deform.bone_index2].position,
                        vertex.position - self.model.bones[vertex.deform.bone_index3].position,
                        vertex.position - self.model.bones[vertex.deform.bone_index4].position,
                    ]
        return all_vertex_relative_poses

    def _calculate_bone_transforms(self) -> Tuple[Dict[str, MVector3D], Dict[str, MMatrix4x4]]:
        """Calculate bone transformation matrices"""
        trans_bone_vecs = {}
        trans_bone_mats = {}

        trans_bone_vecs["全ての親"] = MVector3D()
        trans_bone_mats["全ての親"] = MMatrix4x4()
        trans_bone_mats["全ての親"].setToIdentity()

        bone_names = ["頭"]

        for direction in ["右", "左"]:
            bone_names.extend(
                [
                    f"{direction}親指先",
                    f"{direction}人指先",
                    f"{direction}中指先",
                    f"{direction}薬指先",
                    f"{direction}小指先",
                    f"{direction}胸先",
                    f"{direction}腕捩1",
                    f"{direction}腕捩2",
                    f"{direction}腕捩3",
                    f"{direction}手捩1",
                    f"{direction}手捩2",
                    f"{direction}手捩3",
                ]
            )

        # Process decorative bones after human bones
        for bname in self.model.bones.keys():
            if "装飾_" in bname:
                bone_names.append(bname)

        for end_bone_name in bone_names:
            try:
                # Try to create bone links using the defined parent-child relationships
                bone_links = self.model.create_link_2_top_one(end_bone_name, is_defined=False)
                if bone_links is None:
                    # If that fails, try to create links using the actual bone hierarchy
                    bone = self.model.bones.get(end_bone_name)
                    if bone is None:
                        logger.warning("Bone not found: %s", end_bone_name)
                        continue

                    # Create links by following parent indices
                    bone_links = BoneLinks()
                    current_bone = bone
                    while current_bone is not None and current_bone.parent_index >= 0:
                        bone_links.append(current_bone)
                        current_bone = self.model.bones.get(self.model.bone_indexes.get(current_bone.parent_index))
                        if current_bone and current_bone.name == "上半身":
                            break

                if not bone_links or len(bone_links.all()) == 0:
                    logger.warning("No bone links found for bone: %s", end_bone_name)
                    continue

                # Calculate transformations
                trans_vs = MServiceUtils.calc_relative_position(self.model, bone_links, VmdMotion(), 0)

                if "右" in ",".join(list(bone_links.all().keys())):
                    arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, 35)
                    arm_bone_name = "右腕"
                    thumb0_stance_qq = MQuaternion.fromEulerAngles(0, 8, 0)
                    thumb0_bone_name = "右親指０"
                    thumb1_stance_qq = MQuaternion.fromEulerAngles(0, 24, 0)
                    thumb1_bone_name = "右親指１"
                elif "左" in ",".join(list(bone_links.all().keys())):
                    arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, -35)
                    arm_bone_name = "左腕"
                    thumb0_stance_qq = MQuaternion.fromEulerAngles(0, -8, 0)
                    thumb0_bone_name = "左親指０"
                    thumb1_stance_qq = MQuaternion.fromEulerAngles(0, -24, 0)
                    thumb1_bone_name = "左親指１"
                else:
                    arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, 0)
                    arm_bone_name = ""
                    thumb0_bone_name = ""
                    thumb1_bone_name = ""

                mat = MMatrix4x4()
                mat.setToIdentity()
                for vi, (bone_name, trans_v) in enumerate(zip(bone_links.all().keys(), trans_vs)):
                    mat.translate(trans_v)
                    if bone_name == arm_bone_name:
                        # Rotate arm
                        mat.rotate(arm_astance_qq)
                    elif bone_name == thumb0_bone_name:
                        # Rotate thumb0
                        mat.rotate(thumb0_stance_qq)
                    elif bone_name == thumb1_bone_name:
                        # Rotate thumb1
                        mat.rotate(thumb1_stance_qq)

                    if bone_name not in trans_bone_vecs:
                        trans_bone_vecs[bone_name] = mat * MVector3D()
                        trans_bone_mats[bone_name] = mat.copy()

            except Exception as e:
                logger.warning("Failed to process bone %s: %s", end_bone_name, str(e))
                continue

        if not trans_bone_vecs or not trans_bone_mats:
            logger.error("No bone transformations could be calculated")
            raise RuntimeError("Failed to calculate bone transformations")

        return trans_bone_vecs, trans_bone_mats

    def _update_bone_local_axes(self, trans_bone_mats: Dict[str, MMatrix4x4]) -> None:
        """ボーンのローカル軸を更新"""
        local_y_vector = MVector3D(0, -1, 0)
        for bone_name in trans_bone_mats.keys():
            bone = self.model.bones[bone_name]
            direction = bone_name[0]
            arm_bone_name = f"{direction}腕"
            elbow_bone_name = f"{direction}ひじ"
            wrist_bone_name = f"{direction}手首"
            finger_bone_name = f"{direction}中指１"

            # ローカル軸
            if bone.name in ["右肩", "左肩"] and arm_bone_name in self.model.bones:
                bone.local_x_vector = (
                    self.model.bones[arm_bone_name].position - self.model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()
            if bone.name in ["右腕", "左腕"] and elbow_bone_name in self.model.bones:
                bone.local_x_vector = (
                    self.model.bones[elbow_bone_name].position - self.model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()
            if bone.name in ["右ひじ", "左ひじ"] and wrist_bone_name in self.model.bones:
                # ローカルYで曲げる
                bone.local_x_vector = (
                    self.model.bones[wrist_bone_name].position - self.model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(local_y_vector, bone.local_x_vector).normalized()
            if bone.name in ["右手首", "左手首"] and finger_bone_name in self.model.bones:
                bone.local_x_vector = (
                    self.model.bones[finger_bone_name].position - self.model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()
            # 捩り
            if bone.name in ["右腕捩", "左腕捩"] and arm_bone_name in self.model.bones and elbow_bone_name in self.model.bones:
                bone.fixed_axis = (
                    self.model.bones[elbow_bone_name].position - self.model.bones[arm_bone_name].position
                ).normalized()
                bone.local_x_vector = (
                    self.model.bones[elbow_bone_name].position - self.model.bones[arm_bone_name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()
            if bone.name in ["右手捩", "左手捩"] and elbow_bone_name in self.model.bones and wrist_bone_name in self.model.bones:
                bone.fixed_axis = (
                    self.model.bones[wrist_bone_name].position - self.model.bones[elbow_bone_name].position
                ).normalized()
                bone.local_x_vector = (
                    self.model.bones[wrist_bone_name].position - self.model.bones[elbow_bone_name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()
            # 指
            if (
                bone.english_name in self.BONE_PAIRS
                and self.BONE_PAIRS[bone.english_name]["display"]
                and "指" in self.BONE_PAIRS[bone.english_name]["display"]
            ):
                bone.local_x_vector = (
                    self.model.bones[self.model.bone_indexes[bone.tail_index]].position
                    - self.model.bones[self.model.bone_indexes[bone.parent_index]].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

    def _update_vertex_positions(
        self, all_vertex_relative_poses: Dict[int, List[MVector3D]], trans_bone_mats: Dict[str, MMatrix4x4]
    ) -> None:
        """頂点位置を更新"""
        trans_vertex_vecs = {}
        trans_normal_vecs = {}

        for vertex_idx, vertex_relative_poses in all_vertex_relative_poses.items():
            if vertex_idx not in trans_vertex_vecs:
                vertex = self.model.vertex_dict[vertex_idx]
                if type(vertex.deform) is Bdef1 and self.model.bone_indexes[vertex.deform.index0] in trans_bone_mats:
                    trans_vertex_vecs[vertex.index] = (
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index0]] * vertex_relative_poses[0]
                    )
                    trans_normal_vecs[vertex.index] = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index0]], vertex.normal
                    )
                elif type(vertex.deform) is Bdef2 and (
                    self.model.bone_indexes[vertex.deform.bone_index1] in trans_bone_mats
                    and self.model.bone_indexes[vertex.deform.bone_index2] in trans_bone_mats
                ):
                    v0_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.bone_index1]] * vertex_relative_poses[0]
                    v1_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.bone_index2]] * vertex_relative_poses[1]
                    trans_vertex_vecs[vertex.index] = (v0_vec * vertex.deform.bone_weight) + (
                        v1_vec * (1 - vertex.deform.bone_weight)
                    )

                    v0_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.bone_index1]], vertex.normal
                    )
                    v1_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.bone_index2]], vertex.normal
                    )
                    trans_normal_vecs[vertex.index] = (v0_normal * vertex.deform.bone_weight) + (
                        v1_normal * (1 - vertex.deform.bone_weight)
                    )
                elif type(vertex.deform) is Bdef4 and (
                    self.model.bone_indexes[vertex.deform.index0] in trans_bone_mats
                    and self.model.bone_indexes[vertex.deform.index1] in trans_bone_mats
                    and self.model.bone_indexes[vertex.deform.index2] in trans_bone_mats
                    and self.model.bone_indexes[vertex.deform.index3] in trans_bone_mats
                ):
                    v0_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.index0]] * vertex_relative_poses[0]
                    v1_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.index1]] * vertex_relative_poses[1]
                    v2_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.index2]] * vertex_relative_poses[2]
                    v3_vec = trans_bone_mats[self.model.bone_indexes[vertex.deform.index3]] * vertex_relative_poses[3]
                    trans_vertex_vecs[vertex.index] = (
                        v0_vec * vertex.deform.weight0
                        + v1_vec * vertex.deform.weight1
                        + v2_vec * vertex.deform.weight2
                        + v3_vec * vertex.deform.weight3
                    )

                    v0_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index0]], vertex.normal
                    )
                    v1_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index1]], vertex.normal
                    )
                    v2_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index2]], vertex.normal
                    )
                    v3_normal = self._calc_normal(
                        trans_bone_mats[self.model.bone_indexes[vertex.deform.index3]], vertex.normal
                    )
                    trans_normal_vecs[vertex.index] = (
                        v0_normal * vertex.deform.weight0
                        + v1_normal * vertex.deform.weight1
                        + v2_normal * vertex.deform.weight2
                        + v3_normal * vertex.deform.weight3
                    )

        # 頂点位置を更新
        for vertex_idx, vertex_vec in trans_vertex_vecs.items():
            self.model.vertex_dict[vertex_idx].position = vertex_vec
            self.model.vertex_dict[vertex_idx].normal = trans_normal_vecs[vertex_idx].normalized()

    def _calc_normal(self, mat: MMatrix4x4, normal: MVector3D) -> MVector3D:
        """法線を計算"""
        """
        将模型的骨骼姿态应用到顶点位置和法线上。这个过程包括：
        1. 计算所有骨骼的世界坐标矩阵
        2. 根据骨骼权重转换顶点位置
        3. 转换顶点法线

        Args:
            model (PmxModel): 要处理的PMX模型

        Returns:
            Optional[PmxModel]: 处理后的模型，如果处理失败则返回None
        """
        logger.info("转换姿态", decoration=MLogger.DECORATION_LINE)

        try:
            # 获取所有骨骼的世界坐标矩阵
            bone_matrixes = self._calculate_bone_matrices(self.model)

            # 转换顶点位置
            self._transform_vertices(self.model, bone_matrixes)

            # 转换法线
            self._transform_normals(self.model, bone_matrixes)

            return self.model
        except Exception as e:
            logger.error("姿态转换失败: %s", str(e))
            return None

    def _calculate_bone_matrices(self, model: PmxModel) -> Dict[str, MMatrix4x4]:
        """计算所有骨骼的世界坐标矩阵

        Args:
            model (PmxModel): PMX模型

        Returns:
            Dict[str, MMatrix4x4]: 骨骼名称到其世界坐标矩阵的映射
        """
        bone_matrixes = {}
        for bone in model.bones.values():
            if bone.parent_index < 0:
                # 根骨骼
                bone_matrixes[bone.name] = MMatrix4x4()
                bone_matrixes[bone.name].translate(bone.position)
            else:
                # 子骨骼
                parent_bone = model.bones[bone.parent_index]
                parent_matrix = bone_matrixes[parent_bone.name]
                
                local_matrix = MMatrix4x4()
                local_matrix.translate(bone.position - parent_bone.position)
                
                bone_matrixes[bone.name] = parent_matrix * local_matrix
        
        return bone_matrixes

    def _transform_vertices(self, model: PmxModel, bone_matrixes: Dict[str, MMatrix4x4]) -> None:
        """转换顶点位置

        Args:
            model (PmxModel): PMX模型
            bone_matrixes (Dict[str, MMatrix4x4]): 骨骼矩阵字典
        """
        for vertex in model.vertices:
            # 获取骨骼权重
            bone_weights = self._get_bone_weights(vertex)

            # 计算新的顶点位置
            new_position = MVector3D()
            for bone_idx, weight in bone_weights:
                if weight > 0:
                    bone = model.bones[bone_idx]
                    bone_matrix = bone_matrixes[bone.name]
                    transformed_pos = bone_matrix.transform(vertex.position)
                    new_position += transformed_pos * weight

            vertex.position = new_position

    def _transform_normals(self, model: PmxModel, bone_matrixes: Dict[str, MMatrix4x4]) -> None:
        """转换法线

        Args:
            model (PmxModel): PMX模型
            bone_matrixes (Dict[str, MMatrix4x4]): 骨骼矩阵字典
        """
        for vertex in model.vertices:
            if vertex.normal:
                vertex.normal = self.calc_normal(
                    bone_matrixes[model.bones[vertex.deform.index0].name],
                    vertex.normal
                )

    def _get_bone_weights(self, vertex: Vertex) -> list:
        """获取顶点的骨骼权重

        Args:
            vertex (Vertex): 顶点对象

        Returns:
            list: 骨骼索引和权重的列表，每个元素是一个元组(bone_index, weight)
        """
        if vertex.deform.__class__.__name__ == "Bdef1":
            return [(vertex.deform.bone_index, 1.0)]
        elif vertex.deform.__class__.__name__ == "Bdef2":
            return [
                (vertex.deform.bone_index1, vertex.deform.bone_weight),
                (vertex.deform.bone_index2, 1.0 - vertex.deform.bone_weight)
            ]
        elif vertex.deform.__class__.__name__ == "Bdef4":
            return [
                (vertex.deform.bone_index1, vertex.deform.bone_weight1),
                (vertex.deform.bone_index2, vertex.deform.bone_weight2),
                (vertex.deform.bone_index3, vertex.deform.bone_weight3),
                (vertex.deform.bone_index4, vertex.deform.bone_weight4)
            ]
        elif vertex.deform.__class__.__name__ == "Sdef":
            return [
                (vertex.deform.bone_index1, vertex.deform.bone_weight),
                (vertex.deform.bone_index2, 1.0 - vertex.deform.bone_weight)
            ]
        return []

    def calc_normal(self, bone_mat: MMatrix4x4, normal: MVector3D) -> MVector3D:
        """计算变换后的法线

        Args:
            bone_mat (MMatrix4x4): 骨骼矩阵
            normal (MVector3D): 原始法线

        Returns:
            MVector3D: 变换后的法线
        """
        return bone_mat.transform_normal(normal) 