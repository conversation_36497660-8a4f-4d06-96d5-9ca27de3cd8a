#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 添加src目录到路径
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_weight_assignments():
    print("🔍 测试权重类型赋值修复...")
    
    try:
        from mmd.PmxData import Vertex, Bdef1, Bdef2, Bdef4, Sdef
        from module.MMath import MVector3D, MVector2D
        
        print("  📦 模块导入成功")
        
        # Test Bdef1
        vertex1 = Vertex()
        vertex1.deform = Bdef1(0)
        print(f"  ✅ Bdef1: {vertex1.deform}")
        
        # Test Bdef2  
        vertex2 = Vertex()
        vertex2.deform = Bdef2(0, 1, 0.5)
        print(f"  ✅ Bdef2: {vertex2.deform}")
        
        # Test Bdef4
        vertex3 = Vertex()
        vertex3.deform = Bdef4(0, 1, 2, 3, 0.4, 0.3, 0.2, 0.1)
        print(f"  ✅ Bdef4: {vertex3.deform}")
        
        # Test Sdef
        vertex4 = Vertex()
        vertex4.deform = Sdef(0, 1, 0.5, MVector3D(0,0,0), MVector3D(1,0,0), MVector3D(0,1,0))
        print(f"  ✅ Sdef: {vertex4.deform}")
        
        print("\n🎉 所有权重类型赋值测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_weight_assignments()
    if success:
        print("\n✨ 权重类型修复验证完成！")
        print("🚀 现在可以尝试运行VRoid到PMX转换了!")
    else:
        print("\n⚠️  权重类型修复可能还有问题")
