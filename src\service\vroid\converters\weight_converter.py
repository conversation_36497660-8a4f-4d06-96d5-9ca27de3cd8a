from typing import Dict, Any, List, Tuple, Optional
import numpy as np

from .base_converter import BaseConverter
from mmd.PmxData import Vertex, Bdef1, Bdef2, Bdef4, Sdef
from ..utils.math_utils import MathUtils

class WeightConverter(BaseConverter):
    # 权重类型常量
    DEFORM_BDEF1 = 0
    DEFORM_BDEF2 = 1
    DEFORM_BDEF4 = 2
    DEFORM_SDEF = 3
    DEFORM_QDEF = 4

    def __init__(self, options):
        super().__init__(options)
        self.weight_threshold = 0.0001  # 权重阈值，小于此值的权重将被忽略
        self.max_influences = 4  # 每个顶点最大影响骨骼数

    def convert(self, weight_data: Dict[str, Any]) -> Dict[int, Any]:
        """
        转换权重数据
        
        Args:
            weight_data: 包含权重信息的字典，格式为：
                {
                    "vertex_id": {
                        "weights": [(bone_index, weight_value), ...],
                        "additional_info": {...}  # SDEF等特殊权重的额外信息
                    }
                }
            
        Returns:
            转换后的权重数据，格式为：
                {vertex_id: Bdef1|Bdef2|Bdef4|Sdef}
        """
        try:
            self.log_progress("开始转换权重数据")
            
            # 预处理
            processed_data = self.pre_process(weight_data)
            
            # 验证输入
            if not self.validate_input(processed_data):
                raise ValueError("无效的权重数据")
            
            # 转换权重
            weight_results = {}
            total = len(processed_data)
            
            for idx, (vertex_id, weight_info) in enumerate(processed_data.items(), 1):
                weight_obj = self._convert_single_vertex_weight(vertex_id, weight_info)
                if weight_obj:
                    weight_results[vertex_id] = weight_obj
                self.log_progress("权重转换进度", (idx / total) * 100)
            
            # 后处理
            result = self.post_process(weight_results)
            
            self.log_progress("权重转换完成")
            return result
            
        except Exception as e:
            self.handle_error(e)

    def _convert_single_vertex_weight(self, vertex_id: int, weight_info: Dict[str, Any]) -> Optional[Any]:
        """
        转换单个顶点的权重数据
        
        Args:
            vertex_id: 顶点ID
            weight_info: 权重信息
            
        Returns:
            转换后的权重对象（Bdef1、Bdef2、Bdef4或Sdef）
        """
        try:
            weights = weight_info.get("weights", [])
            if not weights:
                # 如果没有权重，默认使用BDEF1并绑定到根骨骼
                return Bdef1(0)
            
            # 过滤和规范化权重
            filtered_weights = self._filter_and_normalize_weights(weights)
            
            # 根据权重数量选择适当的权重类型
            if len(filtered_weights) == 0:
                return Bdef1(0)  # 默认绑定到根骨骼
            elif len(filtered_weights) == 1:
                return Bdef1(filtered_weights[0][0])
            elif len(filtered_weights) == 2:
                # 检查是否有SDEF信息并且权重接近均匀分布
                if "sdef_info" in weight_info and abs(filtered_weights[0][1] - 0.5) < 0.1:
                    sdef_info = weight_info["sdef_info"]
                    sdef_c = sdef_info.get("c", MVector3D(0, 0, 0))
                    sdef_r0 = sdef_info.get("r0", MVector3D(0, 0, 0))
                    sdef_r1 = sdef_info.get("r1", MVector3D(0, 0, 0))
                    return Sdef(
                        filtered_weights[0][0],
                        filtered_weights[1][0],
                        filtered_weights[0][1],
                        sdef_c,
                        sdef_r0,
                        sdef_r1
                    )
                else:
                    return Bdef2(
                        filtered_weights[0][0],
                        filtered_weights[1][0],
                        filtered_weights[0][1]
                    )
            else:
                # Bdef4 - 最多使用4个权重
                weights_to_use = filtered_weights[:4]
                bone_indices = [w[0] for w in weights_to_use]
                weight_values = [w[1] for w in weights_to_use]
                
                # 如果不足4个权重，用0填充
                while len(bone_indices) < 4:
                    bone_indices.append(0)
                    weight_values.append(0.0)
                
                # 确保权重总和为1
                total_weight = sum(weight_values)
                if total_weight > 0:
                    weight_values = [w / total_weight for w in weight_values]
                
                # 创建Bdef4对象
                deform = Bdef4(
                    bone_indices[0],
                    bone_indices[1],
                    bone_indices[2],
                    bone_indices[3],
                    weight_values[0],
                    weight_values[1],
                    weight_values[2],
                    weight_values[3]
                )
                
                # 设置正确的索引属性
                deform.index0 = bone_indices[0]
                deform.index1 = bone_indices[1]
                deform.index2 = bone_indices[2]
                deform.index3 = bone_indices[3]
                
                # 设置正确的权重属性
                deform.weight0 = weight_values[0]
                deform.weight1 = weight_values[1]
                deform.weight2 = weight_values[2]
                deform.weight3 = weight_values[3]
                
                return deform
            
        except Exception as e:
            self.logger.error(f"转换顶点 {vertex_id} 的权重时发生错误: {str(e)}")
            # 发生错误时返回默认权重
            return Bdef1(0)

    def _filter_and_normalize_weights(self, weights: List[Tuple[int, float]]) -> List[Tuple[int, float]]:
        """
        过滤和规范化权重数据
        
        Args:
            weights: 原始权重列表 [(bone_index, weight_value), ...]
            
        Returns:
            过滤和规范化后的权重列表
        """
        # 过滤掉低于阈值的权重
        filtered_weights = [(bi, w) for bi, w in weights if w > self.weight_threshold]
        
        # 按权重值排序（降序）
        filtered_weights.sort(key=lambda x: x[1], reverse=True)
        
        # 限制影响骨骼数量
        filtered_weights = filtered_weights[:self.max_influences]
        
        # 如果没有权重，返回空列表
        if not filtered_weights:
            return []
        
        # 规范化权重，使总和为1
        total_weight = sum(w for _, w in filtered_weights)
        if total_weight > 0:
            normalized_weights = [(bi, w / total_weight) for bi, w in filtered_weights]
            return normalized_weights
        
        return filtered_weights

    def validate_input(self, data: Dict[str, Any]) -> bool:
        """
        验证权重数据
        
        Args:
            data: 要验证的权重数据
            
        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, dict):
            return False
        
        for vertex_id, weight_info in data.items():
            if not isinstance(weight_info, dict):
                return False
            if "weights" not in weight_info:
                return False
            if not isinstance(weight_info["weights"], list):
                return False
            for weight_item in weight_info["weights"]:
                if not isinstance(weight_item, tuple) or len(weight_item) != 2:
                    return False
                if not isinstance(weight_item[0], int) or not isinstance(weight_item[1], (int, float)):
                    return False
        
        return True

    def pre_process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理权重数据
        
        Args:
            data: 要预处理的权重数据
            
        Returns:
            预处理后的权重数据
        """
        processed_data = {}
        for vertex_id, weight_info in data.items():
            # 确保权重值为浮点数
            weights = [(int(bi), float(w)) for bi, w in weight_info.get("weights", [])]
            
            processed_info = weight_info.copy()
            processed_info["weights"] = weights
            processed_data[vertex_id] = processed_info
        
        return processed_data

    def post_process(self, data: Dict[int, Any]) -> Dict[int, Any]:
        """
        后处理权重数据
        
        Args:
            data: 转换后的权重数据
            
        Returns:
            后处理后的权重数据
        """
        # 验证所有权重对象
        for vertex_id, weight_obj in data.items():
            if isinstance(weight_obj, (Bdef2, Bdef4)):
                # 确保权重和为1
                total = sum(weight_obj.get_weights())
                if abs(total - 1.0) > 0.0001:
                    self.logger.warning(f"顶点 {vertex_id} 的权重和不为1: {total}")
        
        return data

    def get_metadata(self) -> Dict[str, Any]:
        """
        获取转换器元数据
        
        Returns:
            包含转换器元数据的字典
        """
        metadata = super().get_metadata()
        metadata.update({
            "weight_threshold": self.weight_threshold,
            "max_influences": self.max_influences,
            "supported_weight_types": ["Bdef1", "Bdef2", "Bdef4", "Sdef"]
        })
        return metadata 