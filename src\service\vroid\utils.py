"""
Utility functions for VRoid to PMX conversion
"""

import numpy as np
import random
import string
import itertools
import struct
import logging
import os
import traceback
from typing import Any, List, Tuple, Dict, Optional, Union
from module.MMath import MVector3D, MVector4D, MMatrix4x4
from utils.MLogger import MLogger

logger = MLogger(__name__)

def calc_ratio(ratio: float, oldmin: float, oldmax: float, newmin: float, newmax: float) -> float:
    """将一个数值从一个范围映射到另一个范围，保持比例不变

    Args:
        ratio (float): 要转换的值
        oldmin (float): 原始范围的最小值
        oldmax (float): 原始范围的最大值
        newmin (float): 新范围的最小值
        newmax (float): 新范围的最大值

    Returns:
        float: 映射后的值
    """
    return ((ratio - oldmin) * (newmax - newmin)) / (oldmax - oldmin) + newmin

def randomname(n: int) -> str:
    """生成指定长度的随机字符串

    Args:
        n (int): 字符串长度

    Returns:
        str: 生成的随机字符串，包含字母和数字
    """
    return ''.join(random.choices(string.ascii_letters + string.digits, k=n))

def calc_intersect_point(p0: np.ndarray, p1: np.ndarray, p_co: np.ndarray, p_no: np.ndarray, epsilon: float = 1e-6) -> np.ndarray:
    """计算射线与平面的交点

    Args:
        p0 (np.ndarray): 射线起点
        p1 (np.ndarray): 射线终点
        p_co (np.ndarray): 平面上的一点
        p_no (np.ndarray): 平面法线
        epsilon (float, optional): 精度阈值. 默认为 1e-6

    Returns:
        np.ndarray: 交点坐标。如果射线与平面平行，返回射线起点
    """
    u = p1 - p0
    dot = np.dot(p_no, u)

    if abs(dot) > epsilon:
        # 射线与平面相交
        w = p0 - p_co
        fac = -np.dot(p_no, w) / dot
        u = u * fac
        return p0 + u
    else:
        # 射线与平面平行
        return p0

def calc_nearest_point(p0: np.ndarray, p1: np.ndarray, p2: np.ndarray, p: np.ndarray) -> np.ndarray:
    """计算点到三角形的最近点

    Args:
        p0 (np.ndarray): 三角形顶点1
        p1 (np.ndarray): 三角形顶点2
        p2 (np.ndarray): 三角形顶点3
        p (np.ndarray): 目标点

    Returns:
        np.ndarray: 三角形上的最近点
    """
    # 计算三角形的两个边向量
    v0 = p2 - p0
    v1 = p1 - p0
    v2 = p - p0

    # 计算点积
    d00 = np.dot(v0, v0)
    d01 = np.dot(v0, v1)
    d02 = np.dot(v0, v2)
    d11 = np.dot(v1, v1)
    d12 = np.dot(v1, v2)

    # 计算重心坐标
    denom = d00 * d11 - d01 * d01
    if denom != 0:
        v = (d11 * d02 - d01 * d12) / denom
        w = (d00 * d12 - d01 * d02) / denom
        u = 1.0 - v - w
    else:
        v, w, u = 0, 0, 0

    return v0 * v + v1 * w + p0

def polyfit2d(x: np.ndarray, y: np.ndarray, z: np.ndarray, order: int = 3) -> np.ndarray:
    """二维多项式拟合

    Args:
        x (np.ndarray): x坐标数组
        y (np.ndarray): y坐标数组
        z (np.ndarray): z值数组
        order (int, optional): 多项式阶数. 默认为 3

    Returns:
        np.ndarray: 拟合系数
    """
    ncols = (order + 1) ** 2
    G = np.zeros((x.size, ncols))
    ij = itertools.product(range(order + 1), range(order + 1))
    for k, (i, j) in enumerate(ij):
        G[:, k] = x ** i * y ** j
    m = np.linalg.lstsq(G, z, rcond=None)[0]
    return m

def polyval2d(x: np.ndarray, y: np.ndarray, m: np.ndarray) -> np.ndarray:
    """二维多项式求值

    Args:
        x (np.ndarray): x坐标数组
        y (np.ndarray): y坐标数组
        m (np.ndarray): 多项式系数

    Returns:
        np.ndarray: 计算得到的z值数组
    """
    order = int(np.sqrt(len(m))) - 1
    ij = itertools.product(range(order + 1), range(order + 1))
    z = np.zeros_like(x)
    for a, (i, j) in zip(m, ij):
        z += a * x ** i * y ** j
    return z

def read_text(buffer: bytes, offset: int, format_size: int) -> Tuple[str, int]:
    """Read text from buffer"""
    text = buffer[offset:offset + format_size].decode('utf-8')
    return text, offset + format_size

def unpack(buffer: bytes, offset: int, format_size: int, format_str: str) -> Tuple[Any, int]:
    """Unpack data from buffer"""
    data = struct.unpack(format_str, buffer[offset:offset + format_size])
    return data[0] if len(data) == 1 else data, offset + format_size

def define_buf_type(component_type: int) -> Tuple[str, int]:
    """Define buffer type based on component type"""
    if component_type == 5120:
        return 'b', 1  # BYTE
    elif component_type == 5121:
        return 'B', 1  # UNSIGNED_BYTE
    elif component_type == 5122:
        return 'h', 2  # SHORT
    elif component_type == 5123:
        return 'H', 2  # UNSIGNED_SHORT
    elif component_type == 5125:
        return 'I', 4  # UNSIGNED_INT
    elif component_type == 5126:
        return 'f', 4  # FLOAT
    return 'f', 4  # Default to FLOAT 