# UPBGE MMD摄像机转换器使用说明

这是一个专门为UPBGE游戏引擎设计的MMD摄像机转换组件，将场景中现有的摄像机转换为MMD父子摄像机结构。

## 核心特点

- **转换现有摄像机**: 获取场景中已有的摄像机，为其创建MMD父对象结构
- **参考camera.py**: 使用与`camera.py`相同的`convertToMMDCamera`逻辑
- **保留原摄像机**: 不创建新摄像机，而是转换现有摄像机
- **智能查找**: 自动查找场景中的摄像机对象
- **完整MMD结构**: 创建标准的MMD父子摄像机（Empty父对象 + 现有Camera子对象）

## 工作原理

### 转换过程

1. **查找现有摄像机**: 
   - 优先查找指定名称的摄像机
   - 查找默认的"Camera"对象
   - 查找场景中第一个摄像机
   - 从游戏场景中查找摄像机

2. **检查现有结构**: 如果摄像机已经是MMD摄像机，直接使用

3. **创建父对象**: 使用Blender API创建Empty父对象

4. **设置父子关系**: 将现有摄像机设为子对象

5. **配置MMD属性**: 按照MMD标准设置所有属性

## 快速开始

### 前提条件
确保场景中有至少一个摄像机对象

### 使用方法

```python
# 方法1: 组件方式
# 添加 UPBGEMMDCameraCreator 组件到任意游戏对象
# 设置 camera_name 为现有摄像机名称（默认"Camera"）
# 运行游戏，自动转换

# 方法2: 键盘控制
# 添加 UPBGECameraKeyboardControl 组件
# C键转换摄像机，D键销毁MMD结构

# 方法3: 代码调用
from mmd_camera_creator_upbge import create_upbge_mmd_camera
result = create_upbge_mmd_camera(camera_name="Camera")
```

## 组件参数

- `parent_name`: 新创建的父对象名称（默认"MMD_Camera"）
- `camera_name`: 现有摄像机名称（默认"Camera"）
- `initial_position`: 父对象初始位置[x, y, z]
- `initial_rotation`: 父对象初始旋转[rx, ry, rz]度
- `camera_distance`: 摄像机距离（默认-45.0）
- `camera_lens`: 摄像机镜头焦距（默认35.0）
- `auto_create`: 是否自动转换（默认True）
- `set_as_active`: 是否设置为活动摄像机（默认True）
- `scale`: 缩放比例（默认1.0）

## API使用示例

### 基本转换

```python
# 转换默认摄像机
result = create_upbge_mmd_camera()

# 转换指定摄像机
result = create_upbge_mmd_camera(
    parent_name="MyMMDCamera",
    camera_name="MyExistingCamera",  # 场景中现有的摄像机
    position=(0, 0, 15),
    rotation=(0, 0, 0),
    distance=-40.0
)

if result and result['created']:
    print("转换成功!")
    parent_obj = result['parent']      # MMD父对象
    camera_obj = result['camera']      # 转换后的摄像机
else:
    print("转换失败!")
```

### 组件控制

```python
# 获取转换器组件
converter = obj['UPBGEMMDCameraCreator']

# 手动转换
success = converter.create_mmd_camera()

# 销毁MMD结构（恢复原摄像机）
converter.destroy_camera()

# 获取转换状态
info = converter.get_camera_objects()
print(f"转换状态: {info['created']}")
```

## 销毁和恢复

### 销毁MMD结构

```python
converter.destroy_camera()
```

销毁操作会：
- 删除MMD父对象
- 解除摄像机的父子关系
- 重置摄像机位置和旋转
- 解除所有锁定
- 恢复摄像机为独立对象

### 查看转换状态

```python
def check_camera_status():
    """检查摄像机转换状态"""
    scene = bge.logic.getCurrentScene()
    
    for obj in scene.objects:
        if hasattr(obj, 'camera') and obj.camera:
            # 获取对应的Blender对象
            blender_obj = bpy.data.objects.get(obj.name)
            if blender_obj:
                if blender_obj.parent and blender_obj.parent.type == 'EMPTY':
                    print(f"摄像机 {obj.name} 已转换为MMD摄像机")
                else:
                    print(f"摄像机 {obj.name} 是独立摄像机")
```

## 与控制器配合

转换后的MMD摄像机可以直接与`MMDCameraController`配合：

```python
# 1. 先转换摄像机
result = create_upbge_mmd_camera("AnimCamera", "Camera")

if result['created']:
    # 2. 添加动画控制器
    # 在UPBGE中添加 MMDCameraController 组件，参数：
    # camera_name: "AnimCamera"  (转换后的摄像机名)
    # parent_name: "MMD_Camera"  (新创建的父对象名)
    pass
```

## 故障排除

### 常见问题

1. **"未找到可用的摄像机对象"**
   - 确保场景中有摄像机对象
   - 检查摄像机名称是否正确

2. **转换失败**
   - 确保Blender API可用
   - 检查是否有权限创建对象

3. **摄像机行为异常**
   - 检查是否已经是MMD摄像机
   - 确认父子关系设置正确

### 调试信息

组件会输出详细的调试信息：
```
找到摄像机: Camera，开始转换为MMD摄像机
摄像机属性设置完成: 焦距=35.0
已设置 Camera 为活动摄像机
MMD摄像机转换成功: 父对象=MMD_Camera, 摄像机=Camera
```

## 优势

相比从零创建摄像机的方法：

1. **无需模板**: 不需要预先准备摄像机模板
2. **保留设置**: 保留现有摄像机的基本配置
3. **更稳定**: 避免了对象创建的层级限制问题
4. **易于理解**: 符合MMD的工作流程（转换而非创建）

## 使用场景

- 将Blender场景中的摄像机快速转换为MMD摄像机
- 为现有项目添加MMD摄像机功能
- 在UPBGE游戏中动态管理摄像机结构
- 与MMD动画数据配合使用

这个转换器完美解决了UPBGE中创建MMD摄像机的所有问题，提供了稳定可靠的摄像机转换功能！ 