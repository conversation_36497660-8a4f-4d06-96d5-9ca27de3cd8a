from typing import Optional, Union, Any
import struct
from mmd.PmxData import PmxModel

class AccessorService:
    """访问器服务，用于处理VRM文件中的二进制数据访问"""

    def __init__(self):
        """初始化访问器服务"""
        self.offset: int = 0
        self.buffer: Optional[bytes] = None

    def read_from_accessor(self, model: PmxModel, accessor_idx: int) -> Optional[Union[float, int]]:
        """从访问器读取数据

        Args:
            model (PmxModel): PMX模型对象
            accessor_idx (int): 访问器索引

        Returns:
            Optional[Union[float, int]]: 读取的数据值，如果读取失败则返回None
        """
        accessor = model.json_data["accessors"][accessor_idx]
        buffer_view = model.json_data["bufferViews"][accessor["bufferView"]]
        
        if "byteOffset" in buffer_view:
            self.offset = buffer_view["byteOffset"]
        else:
            self.offset = 0
            
        if "buffer" in buffer_view:
            self.buffer = model.buffers[buffer_view["buffer"]]
        
        format_size = 0
        if accessor["componentType"] == 5126:
            # FLOAT
            format_size = 4
        elif accessor["componentType"] == 5123:
            # UNSIGNED_SHORT
            format_size = 2
        elif accessor["componentType"] == 5121:
            # UNSIGNED_BYTE
            format_size = 1
            
        return self.read_text(format_size)

    def define_buf_type(self, componentType: int) -> str:
        """定义缓冲区类型

        Args:
            componentType (int): 组件类型值
                - 5126: FLOAT
                - 5123: UNSIGNED_SHORT
                - 5121: UNSIGNED_BYTE

        Returns:
            str: 对应的格式字符串，如果类型未知则返回空字符串
        """
        if componentType == 5126:
            # FLOAT
            return "f"
        elif componentType == 5123:
            # UNSIGNED_SHORT
            return "H"
        elif componentType == 5121:
            # UNSIGNED_BYTE
            return "B"
        return ""

    def read_text(self, format_size: int) -> Optional[Union[float, int]]:
        """读取文本数据

        Args:
            format_size (int): 数据格式的大小

        Returns:
            Optional[Union[float, int]]: 读取的数据值，如果读取失败则返回None
        """
        format = self.define_buf_type(format_size)
        if not format:
            return None
            
        return self.unpack(format_size, format)

    def unpack(self, format_size: int, format: str) -> Union[float, int]:
        """解包数据

        Args:
            format_size (int): 数据格式的大小
            format (str): 数据格式字符串

        Returns:
            Union[float, int]: 解包后的数据值
        """
        result = struct.unpack_from(format, self.buffer, self.offset)[0]
        self.offset += format_size
        return result 