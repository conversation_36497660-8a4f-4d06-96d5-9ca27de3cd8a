#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试已编译的Cython模块 (避免quaternion依赖)
"""

import os
import sys

# 添加src目录到路径
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_basic_modules():
    print("🔍 测试基础Cython模块（无quaternion依赖）...")
    
    try:
        # 测试参数模块
        print("  ⚙️  测试 module.MParams...")
        from module.MParams import BoneLinks
        links = BoneLinks()
        print(f"    ✅ BoneLinks 创建成功")
        
        # 测试MMD数据模块
        print("  💃 测试 mmd.VmdData...")
        from mmd.VmdData import VmdBoneFrame
        frame = VmdBoneFrame(100)
        frame.set_name("测试骨骼")
        print(f"    ✅ VmdBoneFrame 创建: {frame.name} at frame {frame.fno}")
        
        # 测试PMX数据模块  
        print("  🎭 测试 mmd.PmxData...")
        from mmd.PmxData import PmxModel
        model = PmxModel()
        print(f"    ✅ PmxModel 创建成功")
        
        print("\n🎉 基础模块测试通过！")
        
        # 现在尝试数学模块
        print("\n  📐 尝试测试 module.MMath（可能会因为numpy兼容性问题失败）...")
        try:
            from module.MMath import MVector3D, MMatrix4x4
            # 不使用MQuaternion，因为它依赖quaternion库
            v1 = MVector3D(1, 2, 3)
            v2 = MVector3D(4, 5, 6)
            v3 = v1 + v2
            print(f"    ✅ MVector3D 加法成功: {v1} + {v2} = {v3}")
            
            # 测试矩阵
            mat = MMatrix4x4()
            print(f"    ✅ MMatrix4x4 创建成功")
            
        except Exception as math_error:
            print(f"    ⚠️  数学模块测试失败（预期的numpy兼容性问题）: {math_error}")
        
        print("\n✨ 编译验证基本完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_modules()
    if success:
        print("\n🎯 编译结果总结:")
        print("  ✅ Cython模块编译成功")
        print("  ✅ 基础模块可以正常导入和使用")
        print("  ⚠️  存在NumPy版本兼容性问题，影响quaternion相关功能")
        print("\n📝 建议解决方案:")
        print("  1. 降级NumPy到兼容版本 (>=1.22.4,<2.3.0)")
        print("  2. 或者更新SciPy到兼容最新NumPy的版本")
        print("  3. 程序的核心功能应该仍然可用")
    else:
        print("\n⚠️  编译或测试存在问题") 