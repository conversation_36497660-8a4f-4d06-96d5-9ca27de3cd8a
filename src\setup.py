from setuptools import setup, find_packages
from Cython.Distutils import build_ext
from Cython.Build import cythonize
import setup_ext
import os
import sys
import site

from Cython.Compiler.Options import get_directive_defaults
directive_defaults = get_directive_defaults()
directive_defaults['linetrace'] = True
directive_defaults['binding'] = True

# 确保src目录在Python路径中
src_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, src_path)

# 确保正确的编译顺序
ext_modules = setup_ext.get_ext()

# 合并setup_ext.kwargs和其他参数
setup_kwargs = {
    'name': "vroid2pmx",
    'cmdclass': {"build_ext": build_ext},
    'ext_modules': cythonize(
        ext_modules,
        annotate=True,
        compiler_directives={
            'language_level': "3",
            'profile': True,
            'linetrace': True,
            'binding': True,
            'embedsignature': True
        },
        build_dir="build",
        include_path=["."]
    ),
    'package_dir': {'': '.'},
    'packages': find_packages(where="."),
    'include_package_data': True,
    'zip_safe': False
}

setup(**setup_kwargs)