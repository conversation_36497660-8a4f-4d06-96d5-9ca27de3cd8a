schema_version = "1.0.0"

id = "mmd_tools"
version = "4.3.7"
name = "MMD Tools"
tagline = "Utility tools for MMD model editing"
maintainer = "UuuNyaa <<EMAIL>>"
type = "add-on"

website = "https://mmd-blender.fandom.com/wiki/MMD_Tools"

# Optional list defined by Blender and server, see:
# https://docs.blender.org/manual/en/dev/advanced/extensions/tags.html
tags = [
  "3D View",
  "Camera",
  "Import-Export",
  "Material",
  "Mesh",
  "Object",
  "Physics",
]

blender_version_min = "4.2.0"
blender_version_max = "5.0.0"

license = ["SPDX:GPL-3.0-or-later"]
copyright = ["2021 UuuNyaa", "2014-2022 powroupi", "2012-2015 sugiany"]

## Optional: add-ons can list which resources they will require:
## * files (for access of any filesystem operations)
## * network (for internet access)
## * clipboard (to read and/or write the system clipboard)
## * camera (to capture photos and videos)
## * microphone (to capture audio)
##
## If using network, remember to also check `bpy.app.online_access`
## https://docs.blender.org/manual/en/dev/advanced/extensions/addons.html#internet-access
##
## For each permission it is important to also specify the reason why it is required.
## Keep this a single short sentence without a period (.) at the end.
## For longer explanations use the documentation or detail page.
[permissions]
files = "Import/export PMX/PMD/VMD/VPD from/to disk"

# Optional: build settings.
# https://docs.blender.org/manual/en/dev/advanced/extensions/command_line_arguments.html#command-line-args-extension-build
[build]
paths_exclude_pattern = ["__pycache__/", ".vscode/", ".venv/"]
