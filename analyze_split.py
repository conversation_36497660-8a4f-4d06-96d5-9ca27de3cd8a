import os
import re

def read_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def extract_functions(content):
    # 使用正则表达式匹配函数定义
    pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    return set(re.findall(pattern, content))

def analyze_split():
    # 读取原始文件
    original_file = 'src/service/VroidExportService.py.bak'
    original_content = read_file(original_file)
    original_functions = extract_functions(original_content)
    
    # 读取拆分后的文件
    split_dir = 'src/service/vroid'
    split_functions = set()
    function_locations = {}
    
    for file in os.listdir(split_dir):
        if file.endswith('.py'):
            file_path = os.path.join(split_dir, file)
            content = read_file(file_path)
            funcs = extract_functions(content)
            for func in funcs:
                split_functions.add(func)
                if func in function_locations:
                    function_locations[func].append(file)
                else:
                    function_locations[func] = [file]
    
    # 分析结果
    print('=== 分析结果 ===')
    print('\n1. 原始文件中的函数:')
    for func in sorted(original_functions):
        print(f'  - {func}')
    
    print('\n2. 拆分后的函数:')
    for func in sorted(split_functions):
        files = function_locations[func]
        if len(files) > 1:
            print(f'  - {func} (重复! 在文件: {', '.join(files)})')
        else:
            print(f'  - {func} (在文件: {files[0]})')
    
    print('\n3. 未拆分的函数:')
    not_split = original_functions - split_functions
    for func in sorted(not_split):
        print(f'  - {func}')
    
    print('\n4. 新增的函数:')
    new_funcs = split_functions - original_functions
    for func in sorted(new_funcs):
        print(f'  - {func} (在文件: {', '.join(function_locations[func])})')

if __name__ == '__main__':
    analyze_split()