# -*- coding: utf-8 -*-
#
import logging
from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class BoneGroupService:
    def __init__(self, model: PmxModel):
        self.model = model
        self.bone_materials = {}

    def prepare_bone_material_mapping(self, bone_materials: Dict[str, List[Tuple[int, str]]]):
        """材質・ボーン・頂点INDEXの対応表を設定"""
        self.bone_materials = bone_materials

    def create_hair_bone_settings(self) -> Dict[str, Any]:
        """髪の毛のボーン設定を作成"""
        logger.info("-- 髪の毛のボーン設定作成")
        
        hair_bones = self._collect_hair_bones()
        pmx_tailor_settings = {}
        
        HAIR_AHOGE = logger.transtext("髪(アホ毛)")
        HAIR_SHORT = logger.transtext("髪(ショート)")
        HAIR_LONG = logger.transtext("髪(ロング)")
        ahoge_cnt = 1
        short_cnt = 1
        long_cnt = 1

        for bname, hbones in hair_bones.items():
            _, material_name = list(reversed(sorted(self.bone_materials.get(hbones[0], (["", ""],)))))[0]
            material_name = self.model.materials[material_name].name if material_name else None
            
            if len(hbones) > 1 and (self.model.bones[hbones[0]].position - self.model.bones[hbones[1]].position).y() < 0:
                # アホ毛
                if (HAIR_AHOGE, material_name) not in pmx_tailor_settings:
                    pmx_tailor_settings[(HAIR_AHOGE, material_name)] = self._create_ahoge_settings(ahoge_cnt, hbones, material_name)
                    logger.info("-- -- 髪の毛のボーン設定作成 (%s)", f"髪H{ahoge_cnt}")
                    ahoge_cnt += 1
                else:
                    pmx_tailor_settings[(HAIR_AHOGE, material_name)]["target_bones"].append(hbones)
            elif len(hbones) < 4:
                # ショートヘア
                if (HAIR_SHORT, material_name) not in pmx_tailor_settings:
                    pmx_tailor_settings[(HAIR_SHORT, material_name)] = self._create_short_hair_settings(short_cnt, hbones, material_name)
                    logger.info("-- -- 髪の毛のボーン設定作成 (%s)", f"髪S{short_cnt}")
                    short_cnt += 1
                else:
                    pmx_tailor_settings[(HAIR_SHORT, material_name)]["target_bones"].append(hbones)
            else:
                # ロングヘア
                if (HAIR_LONG, material_name) not in pmx_tailor_settings:
                    pmx_tailor_settings[(HAIR_LONG, material_name)] = self._create_long_hair_settings(long_cnt, hbones, material_name)
                    logger.info("-- -- 髪の毛のボーン設定作成 (%s)", f"髪L{long_cnt}")
                    long_cnt += 1
                else:
                    pmx_tailor_settings[(HAIR_LONG, material_name)]["target_bones"].append(hbones)

        return pmx_tailor_settings

    def create_clothing_bone_settings(self) -> Dict[str, Any]:
        """装飾のボーン設定を作成"""
        logger.info("-- 装飾のボーン設定作成")
        
        clothing_bones = self._collect_clothing_bones()
        pmx_tailor_settings = {}
        
        clothing_cnt = 1
        for bname, cbones in clothing_bones.items():
            _, material_name = list(reversed(sorted(self.bone_materials.get(cbones[0], (["", ""],)))))[0]
            material_name = self.model.materials[material_name].name if material_name else None
            
            if material_name:
                settings = self._create_clothing_settings(clothing_cnt, cbones, material_name)
                pmx_tailor_settings[(logger.transtext("装飾"), material_name)] = settings
                logger.info("-- -- 装飾のボーン設定作成 (%s)", f"装飾{clothing_cnt}")
                clothing_cnt += 1

        return pmx_tailor_settings

    def _collect_hair_bones(self) -> Dict[str, List[str]]:
        """髪の毛のボーンを収集"""
        hair_bones = {}
        HAIR_NAME = "髪"
        
        for bname in self.model.bones.keys():
            if HAIR_NAME in bname:
                if bname[:4] not in hair_bones:
                    hair_bones[bname[:4]] = []
                hair_bones[bname[:4]].append(bname)
                
        return hair_bones

    def _collect_clothing_bones(self) -> Dict[str, List[str]]:
        """装飾のボーンを収集"""
        clothing_bones = {}
        
        for bname in self.model.bones.keys():
            if "装飾_" in bname:
                if bname[:9] not in clothing_bones:
                    clothing_bones[bname[:9]] = []
                clothing_bones[bname[:9]].append(bname)
                
        return clothing_bones

    def _create_ahoge_settings(self, cnt: int, bones: List[str], material_name: str) -> Dict[str, Any]:
        """アホ毛の設定を作成"""
        return {
            "material_name": material_name,
            "abb_name": f"髪H{cnt}",
            "parent_bone_name": "頭",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("髪(アホ毛)"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [bones],
            "back_extend_material_names": self._get_back_material_names(material_name),
            "rigidbody_root_thick": 0.2,
            "rigidbody_end_thick": 0.4,
        }

    def _create_short_hair_settings(self, cnt: int, bones: List[str], material_name: str) -> Dict[str, Any]:
        """ショートヘアの設定を作成"""
        return {
            "material_name": material_name,
            "abb_name": f"髪S{cnt}",
            "parent_bone_name": "頭",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("髪(ショート)"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [bones],
            "back_extend_material_names": self._get_back_material_names(material_name),
            "rigidbody_root_thick": 0.3,
            "rigidbody_end_thick": 1.2,
        }

    def _create_long_hair_settings(self, cnt: int, bones: List[str], material_name: str) -> Dict[str, Any]:
        """ロングヘアの設定を作成"""
        return {
            "material_name": material_name,
            "abb_name": f"髪L{cnt}",
            "parent_bone_name": "頭",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("髪(ロング)"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [bones],
            "back_extend_material_names": self._get_back_material_names(material_name),
            "rigidbody_root_thick": 0.3,
            "rigidbody_end_thick": 1.2,
        }

    def _create_clothing_settings(self, cnt: int, bones: List[str], material_name: str) -> Dict[str, Any]:
        """装飾の設定を作成"""
        return {
            "material_name": material_name,
            "abb_name": f"装飾{cnt}",
            "parent_bone_name": "上半身3",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("単一揺れ物"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [bones],
            "back_extend_material_names": self._get_back_material_names(material_name),
            "rigidbody_root_thick": 0.3,
            "rigidbody_end_thick": 0.5,
        }

    def _get_back_material_names(self, material_name: str) -> List[str]:
        """裏面材質名リストを取得"""
        back_material_names = []
        if not material_name:
            return back_material_names
            
        if f"{material_name}_エッジ" in self.model.materials:
            back_material_names.append(f"{material_name}_エッジ")
        if f"{material_name}_裏" in self.model.materials:
            back_material_names.append(f"{material_name}_裏")
        return back_material_names 