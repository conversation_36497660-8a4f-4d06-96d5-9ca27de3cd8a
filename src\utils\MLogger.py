# -*- coding: utf-8 -*-
#
from datetime import datetime
import logging
import traceback
import threading
import sys
import os
import json
import locale

import cython

from utils.MException import MKilledException


class MLogger:

    DECORATION_IN_BOX = "in_box"
    DECORATION_BOX = "box"
    DECORATION_LINE = "line"
    DEFAULT_FORMAT = "%(message)s [%(funcName)s][P-%(process)s](%(asctime)s)"

    DEBUG_FULL = 2
    TEST = 5
    TIMER = 12
    FULL = 15
    DEBUG_INFO = 16
    INFO_DEBUG = 22
    DEBUG = logging.DEBUG  # 10
    INFO = logging.INFO  # 20
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

    # 翻訳モード
    # 読み取り専用：翻訳リストにない文字列は入力文字列をそのまま出力する
    MODE_READONLY = 0
    # 更新あり：翻訳リストにない文字列は出力する
    MODE_UPDATE = 1

    total_level = logging.INFO
    is_file = False
    mode = MODE_READONLY
    outout_datetime = ""

    # 翻訳モード
    mode = MODE_READONLY
    # 翻訳言語優先順位
    langs = ["en_US", "ja_JP", "zh_CN"]
    # 出力対象言語
    target_lang = "ja_JP"

    messages = {}
    logger = None

    def __init__(self, module_name, level=logging.INFO):
        self.module_name = module_name
        self.default_level = level
        self.child = False

        # ロガー
        self.logger = logging.getLogger("VmdSizing").getChild(self.module_name)

        # 标准输出处理器
        sh = logging.StreamHandler()
        sh.setLevel(level)
        # sh.setFormatter(logging.Formatter(self.DEFAULT_FORMAT))
        # sh.setStream(sys.stdout)
        self.logger.addHandler(sh)

    def copy(self, options):
        self.is_file = options.is_file
        self.outout_datetime = options.outout_datetime
        self.monitor = options.monitor
        self.child = True

        for f in self.logger.handlers:
            if isinstance(f, logging.StreamHandler):
                f.setStream(options.monitor)

    def time(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = self.TIMER
        kwargs["time"] = True
        self.print_logger(msg, *args, **kwargs)

    def info_debug(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = self.INFO_DEBUG
        kwargs["time"] = True
        self.print_logger(msg, *args, **kwargs)

    def debug_info(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = self.DEBUG_INFO
        kwargs["time"] = True
        self.print_logger(msg, *args, **kwargs)

    def test(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = self.TEST
        kwargs["time"] = True
        self.print_logger(msg, *args, **kwargs)

    def debug(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = logging.DEBUG
        kwargs["time"] = True
        self.print_logger(msg, *args, **kwargs)

    def info(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = logging.INFO
        self.print_logger(msg, *args, **kwargs)

    # ログレベルカウント
    def count(self, msg, fno, fnos, *args, **kwargs):
        last_fno = 0

        if fnos and len(fnos) > 0 and fnos[-1] > 0:
            last_fno = fnos[-1]

        if not fnos and kwargs and "last_fno" in kwargs and kwargs["last_fno"] > 0:
            last_fno = kwargs["last_fno"]

        if last_fno > 0:
            if not kwargs:
                kwargs = {}

            kwargs["level"] = logging.INFO
            log_msg = "-- {0}フレーム目:終了({1}％){2}".format(fno, round((fno / last_fno) * 100, 3), msg)
            self.print_logger(log_msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = logging.WARNING
        self.print_logger(msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = logging.ERROR
        self.print_logger(msg, *args, **kwargs)

    def critical(self, msg, *args, **kwargs):
        if not kwargs:
            kwargs = {}

        kwargs["level"] = logging.CRITICAL
        self.print_logger(msg, *args, **kwargs)

    def exception(self, msg, *args, **kwargs):
        """Log an exception with traceback."""
        if not kwargs:
            kwargs = {}
        kwargs["level"] = logging.ERROR
        exc_info = sys.exc_info()
        if "exc_info" not in kwargs:
            kwargs["exc_info"] = exc_info
        self.print_logger(msg, *args, **kwargs)

    # 实际输出的实体
    def print_logger(self, org_msg, *args, **kwargs):

        if "is_killed" in threading.current_thread()._kwargs and threading.current_thread()._kwargs["is_killed"]:
            # 停止命令が出ている場合、エラー
            raise MKilledException()

        target_level = kwargs.pop("level", logging.INFO)
        # if self.logger.isEnabledFor(target_level) and self.default_level <= target_level:
        if self.total_level <= target_level and self.default_level <= target_level:

            if self.is_file:
                for f in self.logger.handlers:
                    if isinstance(f, logging.FileHandler):
                        # 既存のファイルハンドラはすべて削除
                        self.logger.removeHandler(f)

                # ファイル出力ありの場合、ハンドラ紐付け
                # ファイル出力ハンドラ
                fh = logging.FileHandler("log/VmdSizing_{0}.log".format(self.outout_datetime), encoding='utf-8')
                fh.setLevel(self.default_level)
                fh.setFormatter(logging.Formatter(self.DEFAULT_FORMAT))
                self.logger.addHandler(fh)

            # モジュール名を出力するよう追加
            extra_args = {}
            extra_args["module_name"] = self.module_name

            # 翻訳有無で出力メッセージ取得
            is_translate = kwargs.pop("translate", True)
            msg = self.transtext(org_msg) if is_translate and target_level >= self.INFO else org_msg

            # ログレコード生成
            if args and isinstance(args[0], Exception):
                record = self.logger.makeRecord(
                    self.logger.name,
                    target_level,
                    "(unknown file)",
                    0,
                    "{0}\n\n{1}".format(msg, traceback.format_exc()),
                    None,
                    None,
                    self.module_name,
                    extra=extra_args,
                )
            else:
                record = self.logger.makeRecord(
                    self.logger.name,
                    target_level,
                    "(unknown file)",
                    0,
                    msg,
                    args,
                    None,
                    self.module_name,
                    extra=extra_args,
                )

            # 装飾有無
            decoration = kwargs.pop("decoration", None)
            if decoration:
                if decoration == self.DECORATION_BOX:
                    record.msg = self.create_box_message(record.msg, target_level)
                elif decoration == self.DECORATION_LINE:
                    record.msg = self.create_line_message(record.msg, target_level)
                elif decoration == self.DECORATION_IN_BOX:
                    record.msg = self.create_in_box_message(record.msg, target_level)
                else:
                    record.msg = self.create_simple_message(record.msg, target_level)

            # 出力
            self.logger.handle(record)

    def create_box_message(self, msg, level, title=None):
        """ボックス状に装飾する

        Parameters
        ----------
        msg : str
            メッセージ
        level : int
            ログレベル
        title : str, optional
            タイトル, by default None

        Returns
        -------
        str
            装飾後のメッセージ
        """
        msg_list = msg.split("\n")
        msg_list = [m for m in msg_list if len(m.strip()) > 0]

        # 最大文字数
        max_length = max([len(m) for m in msg_list])

        # タイトルがある場合、追加
        if title:
            msg_list.insert(0, title)

        # 先頭に空行追加
        msg_list.insert(0, "")

        # 最後に空行追加
        msg_list.append("")

        # 横線追加
        msg_list.insert(0, "-" * (max_length + 4))
        msg_list.append("-" * (max_length + 4))

        # 縦線追加
        msg_list = [f"| {m}" + " " * (max_length - len(m)) + " |" for m in msg_list]

        return "\n" + "\n".join(msg_list) + "\n"

    def create_line_message(self, msg, level, title=None):
        """ラインで装飾する

        Parameters
        ----------
        msg : str
            メッセージ
        level : int
            ログレベル
        title : str, optional
            タイトル, by default None

        Returns
        -------
        str
            装飾後のメッセージ
        """
        msg_list = msg.split("\n")
        msg_list = [m for m in msg_list if len(m.strip()) > 0]

        # 最大文字数
        max_length = max([len(m) for m in msg_list])

        # タイトルがある場合、追加
        if title:
            msg_list.insert(0, title)

        # 先頭に空行追加
        msg_list.insert(0, "")

        # 最後に空行追加
        msg_list.append("")

        # 横線追加
        msg_list.insert(0, "-" * max_length)
        msg_list.append("-" * max_length)

        return "\n" + "\n".join(msg_list) + "\n"

    def create_in_box_message(self, msg, level, title=None):
        """ボックス内に装飾する

        Parameters
        ----------
        msg : str
            メッセージ
        level : int
            ログレベル
        title : str, optional
            タイトル, by default None

        Returns
        -------
        str
            装飾後のメッセージ
        """
        msg_list = msg.split("\n")
        msg_list = [m for m in msg_list if len(m.strip()) > 0]

        # 最大文字数
        max_length = max([len(m) for m in msg_list])

        # タイトルがある場合、追加
        if title:
            msg_list.insert(0, title)

        # 先頭に空行追加
        msg_list.insert(0, "")

        # 最後に空行追加
        msg_list.append("")

        # 横線追加
        msg_list.insert(0, "-" * max_length)
        msg_list.append("-" * max_length)

        # 縦線追加
        msg_list = [f"| {m}" + " " * (max_length - len(m)) + " |" for m in msg_list]

        return "\n" + "\n".join(msg_list) + "\n"

    def create_simple_message(self, msg, level, title=None):
        """シンプルに装飾する

        Parameters
        ----------
        msg : str
            メッセージ
        level : int
            ログレベル
        title : str, optional
            タイトル, by default None

        Returns
        -------
        str
            装飾後のメッセージ
        """
        msg_list = msg.split("\n")
        msg_list = [m for m in msg_list if len(m.strip()) > 0]

        # タイトルがある場合、追加
        if title:
            msg_list.insert(0, title)

        return "\n" + "\n".join(msg_list) + "\n"

    def transtext(self, msg):
        """翻訳する

        Parameters
        ----------
        msg : str
            メッセージ

        Returns
        -------
        str
            翻訳後のメッセージ
        """
        if not msg:
            return msg

        if not self.messages:
            return msg

        if msg in self.messages:
            return self.messages[msg]

        if self.mode == self.MODE_UPDATE:
            # 更新モードの場合、メッセージを追加
            self.messages[msg] = msg

            # 翻訳ファイルを更新
            for lang in self.langs:
                message_path = self.get_message_path(lang)
                if os.path.exists(message_path):
                    with open(message_path, "w", encoding="utf-8") as f:
                        json.dump(self.messages, f, ensure_ascii=False, indent=4)

        return msg

    @classmethod
    def initialize(cls, level=logging.INFO, is_file=False, target_lang=None, mode=MODE_READONLY):
        # logging.basicConfig(level=level)
        cls.total_level = level
        cls.is_file = is_file
        cls.mode = mode

        if target_lang:
            cls.target_lang = target_lang
        else:
            # システムのデフォルト言語を取得
            system_lang = locale.getdefaultlocale()[0]
            if system_lang in cls.langs:
                cls.target_lang = system_lang
            else:
                cls.target_lang = cls.langs[0]

        # 翻訳ファイルを読み込む
        message_path = cls.get_message_path(cls.target_lang)
        if os.path.exists(message_path):
            try:
                with open(message_path, "r", encoding="utf-8") as f:
                    cls.messages = json.load(f)
            except Exception:
                # UTF-8で読み込めなかった場合、デフォルトで読み込んでUTF-8変換
                try:
                    with open(message_path, "r") as f:
                        cls.messages = json.load(f)

                    # 一旦UTF-8で出力
                    with open(message_path, "w", encoding="utf-8") as f:
                        json.dump(cls.messages, f, ensure_ascii=False, indent=4)

                    # UTF-8で読み込みし直し
                    with open(message_path, "r", encoding="utf-8") as f:
                        cls.messages = json.load(f)
                except Exception:
                    cls.messages = {}

    @classmethod
    def get_message_path(cls, lang):
        """翻訳ファイルのパスを取得する

        Parameters
        ----------
        lang : str
            言語

        Returns
        -------
        str
            翻訳ファイルのパス
        """
        return resource_path(os.path.join("locale", f"{lang}.json"))

def resource_path(relative):
    """リソースファイルのパスを取得する

    Parameters
    ----------
    relative : str
        相対パス

    Returns
    -------
    str
        リソースファイルのパス
    """
    if hasattr(sys, "_MEIPASS"):
        return os.path.join(sys._MEIPASS, relative)
    return os.path.join(relative)

@cython.ccall
def print_message(msg: str, target_level: int):
    """メッセージを出力する

    Parameters
    ----------
    msg : str
        メッセージ
    target_level : int
        ログレベル
    """
    if target_level >= logging.INFO:
        print(msg)
