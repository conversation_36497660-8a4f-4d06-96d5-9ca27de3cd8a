import copy
import re
import numpy as np
from typing import Dict, List, Any, Tuple
from mmd.PmxData import PmxModel, Morph, GroupMorphData, VertexMorphOffset
from module.MMath import MVector3D
from utils.MLogger import <PERSON><PERSON>ogger
from utils.MServiceUtils import calc_ratio

logger = MLogger(__name__, level=1)

class MorphProcessor:
    # 变形对应关系
    MORPH_PAIRS = {
        "Fcl_MTH_A": {"name": "あ", "panel": 2},
        "Fcl_MTH_I": {"name": "い", "panel": 2},
        "Fcl_MTH_U": {"name": "う", "panel": 2},
        "Fcl_MTH_E": {"name": "え", "panel": 2},
        "Fcl_MTH_O": {"name": "お", "panel": 2},
        "Fcl_MTH_Angry": {"name": "▲", "panel": 2},
        "Fcl_MTH_Neutral": {"name": "□", "panel": 2},
        "Fcl_MTH_Smile": {"name": "∧", "panel": 2},
        "Fcl_MTH_Sorrow": {"name": "∨", "panel": 2},
        "Fcl_MTH_Up": {"name": "↑", "panel": 2},
        "Fcl_MTH_Down": {"name": "↓", "panel": 2},
        "Fcl_EYE_Close": {"name": "まばたき", "panel": 1},
        "Fcl_EYE_Close_L": {"name": "左まばたき", "panel": 1},
        "Fcl_EYE_Close_R": {"name": "右まばたき", "panel": 1},
        "Fcl_EYE_Joy": {"name": "笑い", "panel": 1},
        "Fcl_EYE_Joy_L": {"name": "左笑い", "panel": 1},
        "Fcl_EYE_Joy_R": {"name": "右笑い", "panel": 1},
        "Fcl_EYE_Angry": {"name": "怒り", "panel": 1},
        "Fcl_EYE_Angry_L": {"name": "左怒り", "panel": 1},
        "Fcl_EYE_Angry_R": {"name": "右怒り", "panel": 1},
        "Fcl_EYE_Sorrow": {"name": "悲しい", "panel": 1},
        "Fcl_EYE_Sorrow_L": {"name": "左悲しい", "panel": 1},
        "Fcl_EYE_Sorrow_R": {"name": "右悲しい", "panel": 1},
        "Fcl_EYE_Spread": {"name": "びっくり", "panel": 1},
        "Fcl_EYE_Spread_L": {"name": "左びっくり", "panel": 1},
        "Fcl_EYE_Spread_R": {"name": "右びっくり", "panel": 1},
        "Fcl_EYE_Shy": {"name": "恥ずかしい", "panel": 1},
        "Fcl_EYE_Shy_L": {"name": "左恥ずかしい", "panel": 1},
        "Fcl_EYE_Shy_R": {"name": "右恥ずかしい", "panel": 1},
        "Fcl_EYE_Worry": {"name": "困る", "panel": 1},
        "Fcl_EYE_Worry_L": {"name": "左困る", "panel": 1},
        "Fcl_EYE_Worry_R": {"name": "右困る", "panel": 1},
        "Fcl_EYE_Extra": {"name": "はぅ", "panel": 1},
        "Fcl_EYE_Extra_L": {"name": "左はぅ", "panel": 1},
        "Fcl_EYE_Extra_R": {"name": "右はぅ", "panel": 1},
        "Fcl_BRW_Up": {"name": "眉上げ", "panel": 1},
        "Fcl_BRW_Up_L": {"name": "左眉上げ", "panel": 1},
        "Fcl_BRW_Up_R": {"name": "右眉上げ", "panel": 1},
        "Fcl_BRW_Down": {"name": "眉下げ", "panel": 1},
        "Fcl_BRW_Down_L": {"name": "左眉下げ", "panel": 1},
        "Fcl_BRW_Down_R": {"name": "右眉下げ", "panel": 1},
        "Fcl_ALL_Joy": {"name": "喜び", "panel": 4, "binds": ["Fcl_EYE_Joy", "Fcl_MTH_Smile"]},
        "Fcl_ALL_Angry": {"name": "怒り", "panel": 4, "binds": ["Fcl_EYE_Angry", "Fcl_MTH_Angry", "Fcl_BRW_Down"]},
        "Fcl_ALL_Sorrow": {"name": "悲しい", "panel": 4, "binds": ["Fcl_EYE_Sorrow", "Fcl_MTH_Sorrow", "Fcl_BRW_Up"]},
        "Fcl_ALL_Fun": {"name": "楽しい", "panel": 4, "binds": ["Fcl_EYE_Joy", "Fcl_MTH_Smile", "Fcl_BRW_Up"]},
        "Fcl_ALL_Surprised": {
            "name": "驚き",
            "panel": 4,
            "binds": ["Fcl_EYE_Spread", "Fcl_MTH_O", "Fcl_BRW_Up"],
            "ratios": [1, 0.5, 1],
        },
        "Fcl_ALL_Shy": {"name": "恥ずかしい", "panel": 4, "binds": ["Fcl_EYE_Shy", "Fcl_MTH_Neutral", "Fcl_BRW_Up"]},
        "Fcl_ALL_Worry": {"name": "困る", "panel": 4, "binds": ["Fcl_EYE_Worry", "Fcl_MTH_Neutral", "Fcl_BRW_Up"]},
        "Fcl_ALL_Extra": {"name": "はぅ", "panel": 4, "binds": ["Fcl_EYE_Extra", "Fcl_MTH_Up", "Fcl_BRW_Up"]},
    }

    @staticmethod
    def convert_morph(model: PmxModel, is_vroid1: bool) -> PmxModel:
        """转换变形

        Args:
            model (PmxModel): PMX模型
            is_vroid1 (bool): 是否为VRoid1

        Returns:
            PmxModel: 处理后的模型
        """
        # 检查是否有变形组定义
        if (
            "extensions" not in model.json_data
            or "VRM" not in model.json_data["extensions"]
            or "blendShapeMaster" not in model.json_data["extensions"]["VRM"]
            or "blendShapeGroups" not in model.json_data["extensions"]["VRM"]["blendShapeMaster"]
        ):
            return model

        # 复制并清空原始变形
        vertex_morphs = copy.deepcopy(model.org_morphs)
        target_morphs = copy.deepcopy(model.org_morphs)
        model.org_morphs = {}

        logger.info("-- -- モーフ調整準備")

        # 处理面部闭合
        face_close_dict = MorphProcessor._process_face_close(model, is_vroid1, vertex_morphs)

        # 获取面部材质顶点
        face_vertices = MorphProcessor._get_face_vertices(model, face_close_dict)

        # 处理预定义的变形组
        target_morphs = MorphProcessor._process_predefined_morphs(model, target_morphs)

        # 处理自定义变形组
        target_morphs = MorphProcessor._process_custom_morphs(model, target_morphs)

        # 更新模型的变形
        model.org_morphs = target_morphs

        return model

    @staticmethod
    def _process_face_close(
        model: PmxModel, is_vroid1: bool, vertex_morphs: Dict[str, Morph]
    ) -> Dict[int, np.ndarray]:
        """处理面部闭合

        Args:
            model (PmxModel): PMX模型
            is_vroid1 (bool): 是否为VRoid1
            vertex_morphs (Dict[str, Morph]): 顶点变形映射

        Returns:
            Dict[int, np.ndarray]: 面部闭合字典
        """
        face_close_dict = {}
        if is_vroid1:
            for base_offset in vertex_morphs["Fcl_EYE_Close"].offsets:
                face_close_dict[base_offset.vertex_index] = base_offset.position_offset.copy().data()
        return face_close_dict

    @staticmethod
    def _get_face_vertices(model: PmxModel, face_close_dict: Dict[int, np.ndarray]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """获取面部顶点

        Args:
            model (PmxModel): PMX模型
            face_close_dict (Dict[int, np.ndarray]): 面部闭合字典

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 面部材质顶点、左侧闭合顶点、右侧闭合顶点
        """
        face_material_index_vertices = []
        face_left_close_index_vertices = []
        face_right_close_index_vertices = []

        for mat_name, mat_idxs in model.material_indices.items():
            if "_Face_" in mat_name:
                for index_idx in mat_idxs:
                    face_material_index_vertices.append(
                        [
                            model.vertex_dict[model.indices[index_idx][0]].position.data(),
                            model.vertex_dict[model.indices[index_idx][1]].position.data(),
                            model.vertex_dict[model.indices[index_idx][2]].position.data(),
                        ]
                    )

                    close_poses = [
                        model.vertex_dict[model.indices[index_idx][0]].position.data()
                        + face_close_dict.get(model.indices[index_idx][0], np.zeros(3)),
                        model.vertex_dict[model.indices[index_idx][1]].position.data()
                        + face_close_dict.get(model.indices[index_idx][1], np.zeros(3)),
                        model.vertex_dict[model.indices[index_idx][2]].position.data()
                        + face_close_dict.get(model.indices[index_idx][2], np.zeros(3)),
                    ]

                    if np.mean(close_poses, axis=0)[0] < 0:
                        face_right_close_index_vertices.append(close_poses)
                    else:
                        face_left_close_index_vertices.append(close_poses)
                break

        return (
            np.array(face_material_index_vertices),
            np.array(face_left_close_index_vertices),
            np.array(face_right_close_index_vertices),
        )

    @staticmethod
    def _process_predefined_morphs(model: PmxModel, target_morphs: Dict[str, Morph]) -> Dict[str, Morph]:
        """处理预定义的变形组

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        for sidx, shape in enumerate(model.json_data["extensions"]["VRM"]["blendShapeMaster"]["blendShapeGroups"]):
            if len(shape["binds"]) == 0:
                continue

            if sidx > 0 and sidx % 10 == 0:
                logger.info("-- -- モーフ調整: %s個目", sidx)

            morph_name = shape["name"]
            morph_panel = 4
            if shape["name"] in MorphProcessor.MORPH_PAIRS:
                morph_name = MorphProcessor.MORPH_PAIRS[shape["name"]]["name"]
                morph_panel = MorphProcessor.MORPH_PAIRS[shape["name"]]["panel"]
            morph = Morph(morph_name, shape["name"], morph_panel, 0)
            morph.index = len(target_morphs)

            if shape["name"] in MorphProcessor.MORPH_PAIRS and "binds" in MorphProcessor.MORPH_PAIRS[shape["name"]]:
                for bind in MorphProcessor.MORPH_PAIRS[shape["name"]]["binds"]:
                    morph.offsets.append(GroupMorphData(target_morphs[bind].index, 1))
            else:
                for bind in shape["binds"]:
                    morph.offsets.append(GroupMorphData(bind["index"], bind["weight"] / 100))
            target_morphs[morph_name] = morph

        logger.info("-- -- モーフ調整: %s個目", sidx)
        return target_morphs

    @staticmethod
    def _process_custom_morphs(model: PmxModel, target_morphs: Dict[str, Morph]) -> Dict[str, Morph]:
        """处理自定义变形组

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        for sidx, (morph_name, morph_pair) in enumerate(MorphProcessor.MORPH_PAIRS.items()):
            if sidx > 0 and sidx % 20 == 0:
                logger.info("-- -- 拡張モーフ調整: %s個目", sidx)

            if "binds" in morph_pair:
                # 处理组合变形
                morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 0)
                morph.index = len(target_morphs)
                ratios = (
                    morph_pair["ratios"] if "ratios" in morph_pair else [1 for _ in range(len(morph_pair["binds"]))]
                )
                for bind_name, bind_ratio in zip(morph_pair["binds"], ratios):
                    if bind_name in target_morphs:
                        bind_morph = target_morphs[bind_name]
                        morph.offsets.append(GroupMorphData(bind_morph.index, bind_ratio))
                if len(morph.offsets) > 0:
                    target_morphs[morph_name] = morph
            elif "split" in morph_pair:
                # 处理分割变形
                if morph_pair["split"] in target_morphs:
                    org_morph = target_morphs[morph_pair["split"]]
                    target_offset = []
                    if org_morph.morph_type == 1:
                        if re.search(r"raiseEyelid_", morph_name):
                            # 按眼睛上下分割
                            target_offset = MorphProcessor._process_eyelid_split(model, org_morph)
                        else:
                            # 按左右分割
                            target_offset = MorphProcessor._process_lr_split(model, org_morph, morph_name, morph_pair)
                    if target_offset:
                        morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 1)
                        morph.index = len(target_morphs)
                        morph.offsets = target_offset
                        target_morphs[morph_name] = morph

        return target_morphs

    @staticmethod
    def _process_eyelid_split(model: PmxModel, org_morph: Morph) -> List[VertexMorphOffset]:
        """处理眼睑分割

        Args:
            model (PmxModel): PMX模型
            org_morph (Morph): 原始变形

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        target_offset = []
        vposes = []
        for offset in org_morph.offsets:
            if offset.position_offset == MVector3D():
                continue
            vertex = model.vertex_dict[offset.vertex_index]
            vposes.append(vertex.position.data())

        # 计算变形中心
        min_vertex = np.min(vposes, axis=0)
        max_vertex = np.max(vposes, axis=0)
        mean_vertex = np.mean(vposes, axis=0)
        min_limit_y = np.mean([min_vertex[1], mean_vertex[1]])
        max_limit_y = np.mean([max_vertex[1], mean_vertex[1]])

        for offset in org_morph.offsets:
            if offset.position_offset == MVector3D():
                continue
            vertex = model.vertex_dict[offset.vertex_index]
            if vertex.position.y() <= min_limit_y:
                ratio = (
                    1
                    if vertex.position.y() < max_limit_y
                    else calc_ratio(vertex.position.y(), min_vertex[1], max_limit_y, 0, 1)
                )
                target_offset.append(VertexMorphOffset(offset.vertex_index, offset.position_offset * ratio))

        return target_offset

    @staticmethod
    def _process_lr_split(
        model: PmxModel, org_morph: Morph, morph_name: str, morph_pair: Dict[str, Any]
    ) -> List[VertexMorphOffset]:
        """处理左右分割

        Args:
            model (PmxModel): PMX模型
            org_morph (Morph): 原始变形
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        target_offset = []
        for offset in org_morph.offsets:
            if offset.position_offset == MVector3D():
                continue
            vertex = model.vertex_dict[offset.vertex_index]
            if ("_R" == morph_name[-2:] and vertex.position.x() < 0) or (
                "_L" == morph_name[-2:] and vertex.position.x() > 0
            ):
                if morph_pair["panel"] == 2:  # MORPH_LIP
                    # 嘴唇变形根据到中心的距离渐变
                    ratio = (
                        1
                        if abs(vertex.position.x()) >= 0.2
                        else calc_ratio(abs(vertex.position.x()), 0, 0.2, 0, 1)
                    )
                    target_offset.append(VertexMorphOffset(offset.vertex_index, offset.position_offset * ratio))
                else:
                    target_offset.append(VertexMorphOffset(offset.vertex_index, offset.position_offset.copy()))

        return target_offset 