"""眼睛变形处理器模块

此模块提供了处理眼睛变形的功能，包括：
- 眼睛变形的创建
- 眼睛材质的处理
- 眼睛纹理的处理
"""

import copy
import numpy as np
from typing import Dict, List, Any, Tuple
from mmd.PmxData import PmxModel, Morph, VertexMorphOffset, MaterialMorphData
from module.MMath import MVector3D, MVector4D
from utils.MLogger import <PERSON>Logger
from utils.MServiceUtils import calc_intersect_point

logger = MLogger(__name__, level=1)

class EyeMorphProcessor:
    @staticmethod
    def process_eye_morphs(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        morph_pair: Dict[str, Any],
        face_material_index_vertices: np.ndarray,
        face_left_close_index_vertices: np.ndarray,
        face_right_close_index_vertices: np.ndarray
    ) -> List[VertexMorphOffset]:
        """处理眼睛变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对
            face_material_index_vertices (np.ndarray): 面部材质顶点
            face_left_close_index_vertices (np.ndarray): 左侧闭合顶点
            face_right_close_index_vertices (np.ndarray): 右侧闭合顶点

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        target_material_vertices = []
        hide_material_vertices = []
        face_material_vertices = None

        # 获取目标顶点
        for mat_name, mat_vert in model.material_vertices.items():
            for create_mat_name in morph_pair["creates"]:
                if create_mat_name in mat_name:
                    target_material_vertices.extend(mat_vert)
            for hide_mat_name in morph_pair.get("hides", []):
                if hide_mat_name in mat_name:
                    hide_material_vertices.extend(mat_vert)
            if "_Face_" in mat_name:
                face_material_vertices = mat_vert

        target_material_vertices = list(set(target_material_vertices))
        face_material_vertices = list(set(face_material_vertices))

        if not target_material_vertices or not face_material_vertices:
            return []

        # 获取目标位置数据
        target_poses = []
        for vidx in target_material_vertices:
            vertex = model.vertex_dict[vidx]
            target_poses.append(vertex.position.data())

        # 将目标顶点分为左右两部分
        left_target_poses = np.array(target_poses)[np.where(np.array(target_poses)[:, 0] > 0)]
        right_target_poses = np.array(target_poses)[np.where(np.array(target_poses)[:, 0] < 0)]

        # 根据变形类型处理
        if "eye_Small" in morph_name:
            return EyeMorphProcessor._process_small_eye(model, target_morphs, morph_name, target_material_vertices)
        elif "eye_Big" in morph_name:
            return EyeMorphProcessor._process_big_eye(model, target_morphs, morph_name, target_material_vertices)
        elif "eye_Hide_Vertex" in morph_name:
            return EyeMorphProcessor._process_hide_eye(
                model,
                target_morphs,
                target_material_vertices,
                hide_material_vertices,
                face_left_close_index_vertices,
                face_right_close_index_vertices,
                left_target_poses,
                right_target_poses
            )
        else:
            return []

    @staticmethod
    def _process_small_eye(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        target_material_vertices: List[int]
    ) -> List[VertexMorphOffset]:
        """处理瞳孔缩小变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            target_material_vertices (List[int]): 目标材质顶点列表

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        if not ("Fcl_EYE_Surprised_R" in target_morphs and "Fcl_EYE_Surprised_L" in target_morphs):
            logger.warning("Fcl_EYE_Surprised モーフがなかったため、瞳小モーフ生成をスルーします", decoration=MLogger.DECORATION_BOX)
            return []

        target_offset = []
        base_morph = (
            target_morphs["Fcl_EYE_Surprised_R"]
            if "eye_Small_R" == morph_name
            else target_morphs["Fcl_EYE_Surprised_L"]
        )
        for base_offset in base_morph.offsets:
            if base_offset.vertex_index in target_material_vertices:
                target_offset.append(copy.deepcopy(base_offset))

        return target_offset

    @staticmethod
    def _process_big_eye(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        target_material_vertices: List[int]
    ) -> List[VertexMorphOffset]:
        """处理瞳孔放大变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            target_material_vertices (List[int]): 目标材质顶点列表

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        if not ("Fcl_EYE_Surprised_R" in target_morphs and "Fcl_EYE_Surprised_L" in target_morphs):
            logger.warning("Fcl_EYE_Surprised モーフがなかったため、瞳大モーフ生成をスルーします", decoration=MLogger.DECORATION_BOX)
            return []

        target_offset = []
        base_morph = (
            target_morphs["Fcl_EYE_Surprised_R"]
            if "eye_Big_R" == morph_name
            else target_morphs["Fcl_EYE_Surprised_L"]
        )
        for base_offset in base_morph.offsets:
            if base_offset.vertex_index in target_material_vertices:
                target_offset.append(
                    VertexMorphOffset(base_offset.vertex_index, base_offset.position_offset * -1)
                )

        return target_offset

    @staticmethod
    def _process_hide_eye(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        target_material_vertices: List[int],
        hide_material_vertices: List[int],
        face_left_close_index_vertices: np.ndarray,
        face_right_close_index_vertices: np.ndarray,
        left_target_poses: np.ndarray,
        right_target_poses: np.ndarray
    ) -> List[VertexMorphOffset]:
        """处理眼睛隐藏变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            target_material_vertices (List[int]): 目标材质顶点列表
            hide_material_vertices (List[int]): 隐藏材质顶点列表
            face_left_close_index_vertices (np.ndarray): 左侧闭合顶点
            face_right_close_index_vertices (np.ndarray): 右侧闭合顶点
            left_target_poses (np.ndarray): 左侧目标位置
            right_target_poses (np.ndarray): 右侧目标位置

        Returns:
            List[VertexMorphOffset]: 变形偏移列表
        """
        if not ("Fcl_EYE_Close" in target_morphs):
            logger.warning("Fcl_EYE_Close モーフがなかったため、目隠し頂点モーフ生成をスルーします", decoration=MLogger.DECORATION_BOX)
            return []

        target_offset = []

        # 处理眼睛闭合
        for base_offset in target_morphs["Fcl_EYE_Close"].offsets:
            vertex = model.vertex_dict[base_offset.vertex_index]
            if base_offset.vertex_index in hide_material_vertices:
                # 眼线对齐到双眼骨骼位置
                target_offset.append(
                    VertexMorphOffset(vertex.index, model.bones["両目"].position - vertex.position)
                )
            else:
                # 其他部分保持眨眼变形
                target_offset.append(
                    VertexMorphOffset(
                        vertex.index, base_offset.position_offset * MVector3D(1, 1.05, 1)
                    )
                )

        # 处理白眼材质
        for vidx in target_material_vertices:
            vertex = model.vertex_dict[vidx]
            morph_offset = VertexMorphOffset(vertex.index, MVector3D())

            # 将面部顶点分为左右两部分
            face_target_material_index_vertices = (
                face_left_close_index_vertices
                if np.sign(vertex.position.x()) > 0
                else face_right_close_index_vertices
            )

            # 将白眼材质扩展为圆形
            vertex_target_poses = left_target_poses if np.sign(vertex.position.x()) > 0 else right_target_poses
            mean_target_pos = np.mean(vertex_target_poses, axis=0)
            min_target_pos = np.min(vertex_target_poses, axis=0)
            max_target_pos = np.max(vertex_target_poses, axis=0)
            target_diff = (max_target_pos - min_target_pos)[:2]

            if target_diff[0] > target_diff[1]:
                # 处理细长的眼睛
                morph_offset.position_offset.setX(
                    abs(
                        (
                            abs(vertex.position.x() - mean_target_pos[0])
                            * target_diff[1]
                            / target_diff[0]
                        )
                        - abs(vertex.position.x() - mean_target_pos[0])
                    )
                    * np.sign(mean_target_pos[0] - vertex.position.x())
                )
            else:
                # 处理竖长的眼睛
                morph_offset.position_offset.setY(
                    abs(
                        (
                            abs(vertex.position.y() - mean_target_pos[1])
                            * target_diff[0]
                            / target_diff[1]
                        )
                        - abs(vertex.position.y() - mean_target_pos[1])
                    )
                    * np.sign(mean_target_pos[1] - vertex.position.y())
                )

            # 计算Z轴偏移
            morphed_pos = (vertex.position + morph_offset.position_offset).data()
            face_index_distances = np.linalg.norm(
                (face_target_material_index_vertices[:, :, :2] - morphed_pos[:2]),
                ord=2,
                axis=2,
            )
            nearest_face_index_vertices = face_target_material_index_vertices[
                np.argmin(np.sum(face_index_distances, axis=1))
            ]

            # 计算面法线
            v1 = nearest_face_index_vertices[1] - nearest_face_index_vertices[0]
            v2 = nearest_face_index_vertices[2] - nearest_face_index_vertices[1]
            surface_vector = MVector3D.crossProduct(MVector3D(v1), MVector3D(v2))
            surface_normal = surface_vector.normalized()

            # 计算交点
            morphed_nearest_pos = calc_intersect_point(
                morphed_pos + np.array([0, 0, -1000]),
                morphed_pos + np.array([0, 0, 1000]),
                np.mean(nearest_face_index_vertices, axis=0),
                surface_normal.data(),
            )

            # 更新Z轴偏移
            morph_offset.position_offset.setZ(morphed_nearest_pos[2] - morphed_pos[2] - 0.02)
            target_offset.append(morph_offset)

        return target_offset

    @staticmethod
    def _process_eye_material(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        morph_pair: Dict[str, Any]
    ) -> Dict[str, Morph]:
        """处理眼睛材质变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        morph = None
        for material_index, material in enumerate(model.materials.values()):
            if "eye" in material.name.lower():
                # 如果是眼睛材质
                if not morph:
                    morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 8)
                    morph.index = len(target_morphs)

                # 根据变形类型设置不同的参数
                if morph_pair.get("type") == "close":
                    # 闭眼变形
                    morph.offsets.append(
                        MaterialMorphData(
                            material_index,
                            0,  # 乘算
                            MVector4D(0, 0, 0, 0),  # 完全透明
                            MVector3D(),
                            0,
                            MVector3D(),
                            MVector4D(),
                            0,
                            MVector4D(1, 1, 1, 1),
                            MVector4D(1, 1, 1, 1),
                            MVector4D(1, 1, 1, 1),
                        )
                    )
                elif morph_pair.get("type") == "highlight":
                    # 高光变形
                    morph.offsets.append(
                        MaterialMorphData(
                            material_index,
                            1,  # 加算
                            MVector4D(1, 1, 1, 0),  # 增加亮度
                            MVector3D(),
                            1,
                            MVector3D(),
                            MVector4D(1, 1, 1, 1),
                            0,
                            MVector4D(1.2, 1.2, 1.2, 1),  # 增加环境光
                            MVector4D(1.2, 1.2, 1.2, 1),  # 增加漫反射
                            MVector4D(1.5, 1.5, 1.5, 1),  # 增加高光
                        )
                    )

        if morph:
            target_morphs[morph_name] = morph

        return target_morphs 