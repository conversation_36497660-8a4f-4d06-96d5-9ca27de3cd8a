from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class HairService:
    @staticmethod
    def process_hair_settings(
        model: PmxModel, 
        hair_bones: Dict[str, List[str]], 
        bone_materials: Dict[str, List[Tuple[int, str]]]
    ) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """处理头发设置

        Args:
            model (PmxModel): PMX模型
            hair_bones (Dict[str, List[str]]): 头发骨骼映射
            bone_materials (Dict[str, List[Tuple[int, str]]]): 骨骼材质映射

        Returns:
            Dict[Tuple[str, str], Dict[str, Any]]: 头发设置
        """
        HAIR_AHOGE = logger.transtext("髪(アホ毛)")
        HAIR_SHORT = logger.transtext("髪(ショート)")
        HAIR_LONG = logger.transtext("髪(ロング)")
        ahoge_cnt = 1
        short_cnt = 1
        long_cnt = 1
        pmx_tailor_settings = {}

        for bname, hbones in hair_bones.items():
            # 获取材质名称
            _, material_name = list(reversed(sorted(bone_materials.get(hbones[0], (["", ""],)))))[0]
            material_name = model.materials[material_name].name if material_name else None

            if len(hbones) > 1 and (model.bones[hbones[0]].position - model.bones[hbones[1]].position).y() < 0:
                # 呆毛设置
                pmx_tailor_settings = HairService._add_ahoge_settings(
                    pmx_tailor_settings,
                    HAIR_AHOGE,
                    material_name,
                    hbones,
                    ahoge_cnt
                )
                ahoge_cnt += 1
            elif len(hbones) < 4:
                # 短发设置
                pmx_tailor_settings = HairService._add_short_hair_settings(
                    pmx_tailor_settings,
                    HAIR_SHORT,
                    material_name,
                    hbones,
                    short_cnt
                )
                short_cnt += 1
            else:
                # 长发设置
                pmx_tailor_settings = HairService._add_long_hair_settings(
                    pmx_tailor_settings,
                    HAIR_LONG,
                    material_name,
                    hbones,
                    long_cnt
                )
                long_cnt += 1

        return pmx_tailor_settings

    @staticmethod
    def _add_ahoge_settings(
        settings: Dict[Tuple[str, str], Dict[str, Any]],
        hair_type: str,
        material_name: str,
        hbones: List[str],
        cnt: int
    ) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """添加呆毛设置

        Args:
            settings (Dict[Tuple[str, str], Dict[str, Any]]): 现有设置
            hair_type (str): 头发类型
            material_name (str): 材质名称
            hbones (List[str]): 骨骼列表
            cnt (int): 计数器

        Returns:
            Dict[Tuple[str, str], Dict[str, Any]]: 更新后的设置
        """
        if (hair_type, material_name) not in settings:
            settings[(hair_type, material_name)] = {
                "material_name": material_name,
                "abb_name": f"髪H{cnt}",
                "parent_bone_name": "頭",
                "group": "4",
                "direction": logger.transtext("下"),
                "primitive": hair_type,
                "exist_physics_clear": logger.transtext("再利用"),
                "target_bones": [hbones],
                "back_extend_material_names": [],
                "rigidbody_root_thick": 0.2,
                "rigidbody_end_thick": 0.4,
            }
            logger.info("-- -- PmxTailor用設定ファイル出力準備2 (%s)", f"髪H{cnt}")
        else:
            settings[(hair_type, material_name)]["target_bones"].append(hbones)
        return settings

    @staticmethod
    def _add_short_hair_settings(
        settings: Dict[Tuple[str, str], Dict[str, Any]],
        hair_type: str,
        material_name: str,
        hbones: List[str],
        cnt: int
    ) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """添加短发设置

        Args:
            settings (Dict[Tuple[str, str], Dict[str, Any]]): 现有设置
            hair_type (str): 头发类型
            material_name (str): 材质名称
            hbones (List[str]): 骨骼列表
            cnt (int): 计数器

        Returns:
            Dict[Tuple[str, str], Dict[str, Any]]: 更新后的设置
        """
        if (hair_type, material_name) not in settings:
            settings[(hair_type, material_name)] = {
                "material_name": material_name,
                "abb_name": f"髪S{cnt}",
                "parent_bone_name": "頭",
                "group": "4",
                "direction": logger.transtext("下"),
                "primitive": hair_type,
                "exist_physics_clear": logger.transtext("再利用"),
                "target_bones": [hbones],
                "back_extend_material_names": [],
                "rigidbody_root_thick": 0.3,
                "rigidbody_end_thick": 1.2,
            }
            logger.info("-- -- PmxTailor用設定ファイル出力準備2 (%s)", f"髪S{cnt}")
        else:
            settings[(hair_type, material_name)]["target_bones"].append(hbones)
        return settings

    @staticmethod
    def _add_long_hair_settings(
        settings: Dict[Tuple[str, str], Dict[str, Any]],
        hair_type: str,
        material_name: str,
        hbones: List[str],
        cnt: int
    ) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """添加长发设置

        Args:
            settings (Dict[Tuple[str, str], Dict[str, Any]]): 现有设置
            hair_type (str): 头发类型
            material_name (str): 材质名称
            hbones (List[str]): 骨骼列表
            cnt (int): 计数器

        Returns:
            Dict[Tuple[str, str], Dict[str, Any]]: 更新后的设置
        """
        if (hair_type, material_name) not in settings:
            settings[(hair_type, material_name)] = {
                "material_name": material_name,
                "abb_name": f"髪L{cnt}",
                "parent_bone_name": "頭",
                "group": "4",
                "direction": logger.transtext("下"),
                "primitive": hair_type,
                "exist_physics_clear": logger.transtext("再利用"),
                "target_bones": [hbones],
                "back_extend_material_names": [],
                "rigidbody_root_thick": 0.2,
                "rigidbody_end_thick": 0.5,
            }
            logger.info("-- -- PmxTailor用設定ファイル出力準備2 (%s)", f"髪L{cnt}")
        else:
            settings[(hair_type, material_name)]["target_bones"].append(hbones)
        return settings 