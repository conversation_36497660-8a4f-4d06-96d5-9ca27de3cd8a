# import bge
# import bpy
# import mathutils
# import math

# class UPBGEMMDCameraCreator(bge.types.KX_PythonComponent):
#     """
#     UPBGE MMD摄像机创建器
#     参考camera.py的方法，使用正确的Blender API创建MMD父子摄像机结构
#     """
    
#     # 组件参数
#     args = {
#         "parent_name": "MMD_Camera",        # 父对象名称
#         "camera_name": "Camera",            # 摄像机名称
#         "initial_position": [0.0, 0.0, 10.0],  # 父对象初始位置
#         "initial_rotation": [0.0, 0.0, 0.0],   # 父对象初始旋转（度）
#         "camera_distance": -45.0,           # 摄像机距离
#         "camera_lens": 35.0,                # 摄像机镜头焦距
#         "auto_create": True,                # 是否自动创建
#         "set_as_active": True,              # 是否设置为活动摄像机
#         "scale": 1.0,                       # 缩放比例
#     }
    
#     def start(self, args):
#         """初始化组件"""
#         print(f"[UPBGEMMDCameraCreator] 开始初始化摄像机创建器...")
        
#         self.parent_name = args.get("parent_name", "MMD_Camera")
#         self.camera_name = args.get("camera_name", "Camera")
#         self.initial_position = args.get("initial_position", [0.0, 0.0, 10.0])
#         self.initial_rotation = args.get("initial_rotation", [0.0, 0.0, 0.0])
#         self.camera_distance = args.get("camera_distance", -45.0)
#         self.camera_lens = args.get("camera_lens", 35.0)
#         self.auto_create = args.get("auto_create", True)
#         self.set_as_active = args.get("set_as_active", True)
#         self.scale = args.get("scale", 1.0)
        
#         print(f"[UPBGEMMDCameraCreator] 参数设置:")
#         print(f"  - 父对象名称: {self.parent_name}")
#         print(f"  - 摄像机名称: {self.camera_name}")
#         print(f"  - 初始位置: {self.initial_position}")
#         print(f"  - 初始旋转: {self.initial_rotation}")
#         print(f"  - 摄像机距离: {self.camera_distance}")
#         print(f"  - 镜头焦距: {self.camera_lens}")
#         print(f"  - 自动创建: {self.auto_create}")
#         print(f"  - 设为活动: {self.set_as_active}")
#         print(f"  - 缩放比例: {self.scale}")
        
#         # 创建状态
#         self.created = False
#         self.parent_obj = None
#         self.camera_obj = None
        
#         print(f"[UPBGEMMDCameraCreator] 初始化完成")
        
#         if self.auto_create:
#             print(f"[UPBGEMMDCameraCreator] 自动创建模式，开始创建摄像机...")
#             self.create_mmd_camera()
    
#     def create_mmd_camera(self):
#         """创建MMD父子摄像机结构 - 参考camera.py的方法"""
#         print(f"[UPBGEMMDCameraCreator] 开始创建MMD摄像机结构...")
        
#         if self.created:
#             print("[UPBGEMMDCameraCreator] MMD摄像机已经创建过了，跳过创建")
#             return True
        
#         try:
#             print(f"[UPBGEMMDCameraCreator] 1. 检查现有对象...")
#             # 检查是否已存在同名对象
#             if self._check_existing_objects():
#                 print(f"[UPBGEMMDCameraCreator] 检查失败，取消创建")
#                 return False
            
#             print(f"[UPBGEMMDCameraCreator] 2. 使用Blender API创建摄像机...")
#             # 使用Blender API创建对象（参考camera.py的convertToMMDCamera方法）
#             success = self._create_camera_using_blender_api()
            
#             if success:
#                 print(f"[UPBGEMMDCameraCreator] 3. 设置摄像机属性...")
#                 self._setup_camera_properties()
                
#                 print(f"[UPBGEMMDCameraCreator] 4. 设置为活动摄像机...")
#                 self._set_active_camera()
                
#                 self.created = True
#                 print(f"[UPBGEMMDCameraCreator] ✅ MMD摄像机创建成功!")
#                 print(f"  - 父对象: {self.parent_name}")
#                 print(f"  - 摄像机: {self.camera_name}")
#                 print(f"  - 游戏中父对象: {self.parent_obj.name if self.parent_obj else 'None'}")
#                 print(f"  - 游戏中摄像机: {self.camera_obj.name if self.camera_obj else 'None'}")
#                 return True
#             else:
#                 print("[UPBGEMMDCameraCreator] ❌ MMD摄像机创建失败")
#                 return False
                
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ❌ 创建MMD摄像机时发生错误: {e}")
#             import traceback
#             traceback.print_exc()
#             return False
    
#     def _check_existing_objects(self):
#         """检查是否已存在同名父对象"""
#         print(f"[UPBGEMMDCameraCreator] 检查现有对象...")
        
#         scene = bge.logic.getCurrentScene()
#         print(f"[UPBGEMMDCameraCreator] 游戏场景对象数量: {len(scene.objects)}")
        
#         existing_parent = scene.objects.get(self.parent_name)
#         if existing_parent:
#             print(f"[UPBGEMMDCameraCreator] ⚠️ 警告: 场景中已存在名为 '{self.parent_name}' 的对象")
#             return True
        
#         # 检查Blender场景中的对象
#         blender_parent = bpy.data.objects.get(self.parent_name)
#         if blender_parent:
#             print(f"[UPBGEMMDCameraCreator] ⚠️ 警告: Blender数据中已存在名为 '{self.parent_name}' 的对象")
#             return True
        
#         print(f"[UPBGEMMDCameraCreator] ✅ 没有发现同名对象冲突")
#         return False
    
#     def _find_existing_camera(self):
#         """查找场景中的现有摄像机"""
#         print(f"[UPBGEMMDCameraCreator] 开始查找现有摄像机...")
        
#         # 打印所有Blender对象
#         print(f"[UPBGEMMDCameraCreator] Blender场景中的所有对象:")
#         for i, obj in enumerate(bpy.data.objects):
#             print(f"  {i+1}. {obj.name} (类型: {obj.type})")
        
#         # 1. 优先查找指定名称的摄像机
#         if self.camera_name != "Camera":  # 如果指定了特定名称
#             print(f"[UPBGEMMDCameraCreator] 1. 查找指定名称摄像机: {self.camera_name}")
#             camera_obj = bpy.data.objects.get(self.camera_name)
#             if camera_obj and camera_obj.type == 'CAMERA':
#                 print(f"[UPBGEMMDCameraCreator] ✅ 找到指定摄像机: {camera_obj.name}")
#                 return camera_obj
#             else:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 未找到指定名称的摄像机")
        
#         # 2. 查找默认摄像机
#         print(f"[UPBGEMMDCameraCreator] 2. 查找默认摄像机: Camera")
#         camera_obj = bpy.data.objects.get("Camera")
#         if camera_obj and camera_obj.type == 'CAMERA':
#             print(f"[UPBGEMMDCameraCreator] ✅ 找到默认摄像机: {camera_obj.name}")
#             return camera_obj
#         else:
#             print(f"[UPBGEMMDCameraCreator] ❌ 未找到默认摄像机")
        
#         # 3. 查找场景中第一个摄像机
#         print(f"[UPBGEMMDCameraCreator] 3. 查找第一个摄像机对象...")
#         camera_count = 0
#         for obj in bpy.data.objects:
#             if obj.type == 'CAMERA':
#                 camera_count += 1
#                 print(f"[UPBGEMMDCameraCreator] ✅ 找到摄像机: {obj.name}")
#                 return obj
        
#         print(f"[UPBGEMMDCameraCreator] 场景中摄像机总数: {camera_count}")
        
#         # 4. 从游戏场景中查找摄像机（通过检查Blender对象类型）
#         print(f"[UPBGEMMDCameraCreator] 4. 从游戏场景查找摄像机...")
#         scene = bge.logic.getCurrentScene()
#         print(f"[UPBGEMMDCameraCreator] 游戏场景对象:")
#         for i, obj in enumerate(scene.objects):
#             print(f"  {i+1}. {obj.name}")
#             # 通过游戏对象名称找到对应的Blender对象
#             blender_obj = bpy.data.objects.get(obj.name)
#             if blender_obj and blender_obj.type == 'CAMERA':
#                 print(f"[UPBGEMMDCameraCreator] ✅ 从游戏场景找到摄像机: {blender_obj.name}")
#                 return blender_obj
        
#         print(f"[UPBGEMMDCameraCreator] ❌ 未找到任何可用的摄像机对象")
#         return None
    
#     def _get_game_object(self, blender_obj):
#         """从Blender对象获取游戏对象"""
#         print(f"[UPBGEMMDCameraCreator] 获取Blender对象 '{blender_obj.name}' 的游戏引用...")
        
#         scene = bge.logic.getCurrentScene()
        
#         # 多次尝试获取游戏对象，等待同步
#         max_attempts = 10
#         for attempt in range(max_attempts):
#             game_obj = scene.objects.get(blender_obj.name)
            
#             if game_obj:
#                 print(f"[UPBGEMMDCameraCreator] ✅ 第{attempt + 1}次尝试成功获取游戏对象: {game_obj.name}")
#                 return game_obj
#             else:
#                 print(f"[UPBGEMMDCameraCreator] 第{attempt + 1}次尝试未找到游戏对象，等待同步...")
                
#                 # 如果是最后一次尝试，打印详细信息
#                 if attempt == max_attempts - 1:
#                     print(f"[UPBGEMMDCameraCreator] ❌ 所有尝试都失败，未找到对应的游戏对象: {blender_obj.name}")
                    
#                     # 打印当前游戏场景中的所有对象
#                     print(f"[UPBGEMMDCameraCreator] 当前游戏场景中的对象:")
#                     for i, obj in enumerate(scene.objects):
#                         print(f"  {i+1}. {obj.name}")
                    
#                     # 检查Blender数据中是否存在该对象
#                     blender_check = bpy.data.objects.get(blender_obj.name)
#                     print(f"[UPBGEMMDCameraCreator] Blender数据中的对象检查: {'存在' if blender_check else '不存在'}")
                    
#                     return None
                
#                 # 短暂等待，让对象有时间同步到游戏引擎
#                 # 在UPBGE中，我们不能直接sleep，但可以通过其他方式延迟
#                 continue
        
#         return None
    
#     def _wait_for_object_sync(self):
#         """等待对象同步到游戏引擎 - 通过强制更新场景"""
#         try:
#             print(f"[UPBGEMMDCameraCreator] 尝试强制更新场景以同步对象...")
            
#             # 方法1: 更新Blender场景
#             if hasattr(bpy.context, 'view_layer'):
#                 bpy.context.view_layer.update()
#                 print(f"[UPBGEMMDCameraCreator] ✅ 已更新view_layer")
            
#             # 方法2: 更新场景依赖图
#             if hasattr(bpy.context, 'scene') and hasattr(bpy.context.scene, 'update'):
#                 bpy.context.scene.update()
#                 print(f"[UPBGEMMDCameraCreator] ✅ 已更新场景")
            
#             # 方法3: 强制重新计算
#             if hasattr(bpy.data, 'scenes'):
#                 for scene in bpy.data.scenes:
#                     if hasattr(scene, 'frame_set'):
#                         current_frame = scene.frame_current
#                         scene.frame_set(current_frame)
#                         print(f"[UPBGEMMDCameraCreator] ✅ 已重新设置帧到 {current_frame}")
#                         break
            
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ⚠️ 强制更新场景失败: {e}")
    
#     def _create_camera_using_blender_api(self):
#         """转换现有摄像机为MMD摄像机 - 参考camera.py的convertToMMDCamera"""
#         print(f"[UPBGEMMDCameraCreator] 开始转换摄像机为MMD摄像机...")
        
#         try:
#             # 1. 获取场景中的摄像机对象
#             print(f"[UPBGEMMDCameraCreator] 步骤1: 查找摄像机对象...")
#             camera_obj = self._find_existing_camera()
#             if not camera_obj:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 错误: 未找到可用的摄像机对象")
#                 return False
            
#             print(f"[UPBGEMMDCameraCreator] ✅ 找到摄像机: {camera_obj.name}，开始转换为MMD摄像机")
            
#             # 2. 检查是否已经是MMD摄像机
#             print(f"[UPBGEMMDCameraCreator] 步骤2: 检查是否已经是MMD摄像机...")
#             if camera_obj.parent and camera_obj.parent.type == 'EMPTY':
#                 print(f"[UPBGEMMDCameraCreator] ⚠️ 摄像机 {camera_obj.name} 可能已经是MMD摄像机 (父对象: {camera_obj.parent.name})")
#                 self.parent_obj = self._get_game_object(camera_obj.parent)
#                 self.camera_obj = self._get_game_object(camera_obj)
#                 print(f"[UPBGEMMDCameraCreator] 获取到游戏对象 - 父对象: {self.parent_obj.name if self.parent_obj else 'None'}")
#                 print(f"[UPBGEMMDCameraCreator] 获取到游戏对象 - 摄像机: {self.camera_obj.name if self.camera_obj else 'None'}")
#                 return True
            
#             # 3. 创建Empty父对象（参考camera.py第125行）
#             print(f"[UPBGEMMDCameraCreator] 步骤3: 创建Empty父对象 '{self.parent_name}'...")
#             empty = bpy.data.objects.new(name=self.parent_name, object_data=None)
#             print(f"[UPBGEMMDCameraCreator] ✅ Empty对象创建成功: {empty.name}")
            
#             print(f"[UPBGEMMDCameraCreator] 步骤4: 链接Empty对象到场景...")
#             self._link_object_to_scene(empty)
            
#             # 4. 设置摄像机为子对象（参考camera.py第127行）
#             print(f"[UPBGEMMDCameraCreator] 步骤5: 设置摄像机为子对象...")
#             camera_obj.parent = empty
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机 {camera_obj.name} 已设置父对象为 {empty.name}")
            
#             # 5. 设置摄像机基本属性（参考camera.py第128-136行）
#             print(f"[UPBGEMMDCameraCreator] 步骤6: 设置摄像机基本属性...")
#             camera_data = camera_obj.data
#             camera_data.sensor_fit = "VERTICAL"
#             camera_data.lens_unit = "MILLIMETERS"
#             camera_data.ortho_scale = 25 * self.scale
#             camera_data.clip_end = 500 * self.scale
#             if hasattr(camera_data, 'display_size'):
#                 camera_data.display_size = 5 * self.scale
#                 print(f"[UPBGEMMDCameraCreator] ✅ 摄像机显示大小设置: {5 * self.scale}")
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机基本属性设置完成")
            
#             # 6. 设置摄像机位置和旋转（参考camera.py第137-142行）
#             print(f"[UPBGEMMDCameraCreator] 步骤7: 设置摄像机位置和旋转...")
#             camera_obj.location = (0, self.camera_distance * self.scale, 0)
#             camera_obj.rotation_mode = "XYZ"
#             camera_obj.rotation_euler = (math.radians(90), 0, 0)
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机位置: {camera_obj.location}")
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机旋转: {camera_obj.rotation_euler}")
            
#             # 7. 设置锁定（参考camera.py第143-145行）
#             print(f"[UPBGEMMDCameraCreator] 步骤8: 设置摄像机锁定...")
#             camera_obj.lock_location = (True, False, True)
#             camera_obj.lock_rotation = (True, True, True)
#             camera_obj.lock_scale = (True, True, True)
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机锁定设置完成")
            
#             # 8. 设置DOF焦点对象（参考camera.py第146行）
#             print(f"[UPBGEMMDCameraCreator] 步骤9: 设置DOF焦点对象...")
#             camera_data.dof.focus_object = empty
#             print(f"[UPBGEMMDCameraCreator] ✅ DOF焦点对象设置为: {empty.name}")
            
#             # 9. 设置Empty对象属性（参考camera.py第149-156行）
#             print(f"[UPBGEMMDCameraCreator] 步骤10: 设置Empty对象属性...")
#             empty.location = self.initial_position
#             empty.rotation_mode = "YXZ"
#             if hasattr(empty, 'empty_display_size'):
#                 empty.empty_display_size = 5 * self.scale
#                 print(f"[UPBGEMMDCameraCreator] ✅ Empty显示大小: {5 * self.scale}")
#             empty.lock_scale = (True, True, True)
#             print(f"[UPBGEMMDCameraCreator] ✅ Empty位置: {empty.location}")
#             print(f"[UPBGEMMDCameraCreator] ✅ Empty旋转模式: {empty.rotation_mode}")
            
#             # 10. 设置旋转
#             print(f"[UPBGEMMDCameraCreator] 步骤11: 设置Empty旋转...")
#             rotation_rad = [math.radians(angle) for angle in self.initial_rotation]
#             empty.rotation_euler = rotation_rad
#             print(f"[UPBGEMMDCameraCreator] ✅ Empty旋转: {empty.rotation_euler} (弧度)")
            
#             # 11. 更新摄像机名称（如果需要）
#             print(f"[UPBGEMMDCameraCreator] 步骤12: 检查摄像机名称...")
#             if camera_obj.name != self.camera_name:
#                 old_name = camera_obj.name
#                 camera_obj.name = self.camera_name
#                 print(f"[UPBGEMMDCameraCreator] ✅ 摄像机名称已更新: {old_name} -> {camera_obj.name}")
#             else:
#                 print(f"[UPBGEMMDCameraCreator] ✅ 摄像机名称无需更新: {camera_obj.name}")
            
#             # 12. 强制同步对象到游戏引擎
#             print(f"[UPBGEMMDCameraCreator] 步骤13: 强制同步对象到游戏引擎...")
#             self._wait_for_object_sync()
            
#             # 13. 存储引用 - 重试获取游戏对象
#             print(f"[UPBGEMMDCameraCreator] 步骤14: 获取游戏对象引用...")
#             self.parent_obj = self._get_game_object(empty)
#             self.camera_obj = self._get_game_object(camera_obj)
            
#             if not self.parent_obj:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 警告: 无法获取父对象的游戏引用")
#                 # 尝试通过名称直接获取
#                 scene = bge.logic.getCurrentScene()
#                 self.parent_obj = scene.objects.get(self.parent_name)
#                 if self.parent_obj:
#                     print(f"[UPBGEMMDCameraCreator] ✅ 通过名称成功获取父对象: {self.parent_obj.name}")
#             else:
#                 print(f"[UPBGEMMDCameraCreator] ✅ 父对象游戏引用: {self.parent_obj.name}")
            
#             if not self.camera_obj:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 警告: 无法获取摄像机的游戏引用")
#                 # 尝试通过名称直接获取
#                 scene = bge.logic.getCurrentScene()
#                 self.camera_obj = scene.objects.get(self.camera_name)
#                 if self.camera_obj:
#                     print(f"[UPBGEMMDCameraCreator] ✅ 通过名称成功获取摄像机: {self.camera_obj.name}")
#             else:
#                 print(f"[UPBGEMMDCameraCreator] ✅ 摄像机游戏引用: {self.camera_obj.name}")
            
#             print(f"[UPBGEMMDCameraCreator] ✅ MMD摄像机转换完成!")
#             return True
            
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ❌ 转换摄像机为MMD摄像机失败: {e}")
#             import traceback
#             traceback.print_exc()
#             return False
    
#     def _link_object_to_scene(self, obj):
#         """将对象链接到场景中"""
#         print(f"[UPBGEMMDCameraCreator] 开始链接对象 '{obj.name}' 到场景...")
        
#         try:
#             # 在Blender 2.8+中，使用collection来管理对象
#             if hasattr(bpy.context, 'collection'):
#                 print(f"[UPBGEMMDCameraCreator] 方法1: 使用 bpy.context.collection")
#                 bpy.context.collection.objects.link(obj)
#                 print(f"[UPBGEMMDCameraCreator] ✅ 成功使用 context.collection 链接对象")
#             elif hasattr(bpy.context, 'scene'):
#                 print(f"[UPBGEMMDCameraCreator] 方法2: 使用 bpy.context.scene")
#                 bpy.context.scene.objects.link(obj)
#                 print(f"[UPBGEMMDCameraCreator] ✅ 成功使用 context.scene 链接对象")
#             else:
#                 # 尝试获取活动场景
#                 print(f"[UPBGEMMDCameraCreator] 方法3: 尝试获取活动场景")
#                 scene = bpy.data.scenes.get('Scene') or bpy.context.scene
#                 if hasattr(scene, 'collection'):
#                     print(f"[UPBGEMMDCameraCreator] 使用场景集合链接")
#                     scene.collection.objects.link(obj)
#                     print(f"[UPBGEMMDCameraCreator] ✅ 成功使用 scene.collection 链接对象")
#                 else:
#                     print(f"[UPBGEMMDCameraCreator] 使用场景对象链接")
#                     scene.objects.link(obj)
#                     print(f"[UPBGEMMDCameraCreator] ✅ 成功使用 scene.objects 链接对象")
                    
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ❌ 链接对象到场景失败: {e}")
#             # 尝试备用方法
#             try:
#                 print(f"[UPBGEMMDCameraCreator] 尝试备用方法: bpy.data.scenes[0]")
#                 bpy.data.scenes[0].collection.objects.link(obj)
#                 print(f"[UPBGEMMDCameraCreator] ✅ 备用方法成功")
#             except Exception as e2:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 备用方法也失败: {e2}")
#                 print("[UPBGEMMDCameraCreator] ❌ 所有链接方法都失败了")
#                 raise e
    
#     def _setup_camera_properties(self):
#         """设置摄像机属性 - 通过Blender API"""
#         print(f"[UPBGEMMDCameraCreator] 开始设置摄像机属性...")
        
#         if not self.camera_obj:
#             print("[UPBGEMMDCameraCreator] ❌ 警告: 摄像机对象无效，跳过属性设置")
#             return
        
#         try:
#             # 通过Blender API获取摄像机数据
#             camera_blender_obj = bpy.data.objects.get(self.camera_obj.name)
#             if not camera_blender_obj or camera_blender_obj.type != 'CAMERA':
#                 print(f"[UPBGEMMDCameraCreator] ❌ 警告: 无法找到对应的Blender摄像机对象 '{self.camera_obj.name}'")
#                 return
            
#             print(f"[UPBGEMMDCameraCreator] ✅ 找到Blender摄像机对象: {camera_blender_obj.name}")
#             camera_data = camera_blender_obj.data
            
#             # 设置镜头焦距
#             if hasattr(camera_data, 'lens'):
#                 old_lens = camera_data.lens
#                 camera_data.lens = self.camera_lens
#                 print(f"[UPBGEMMDCameraCreator] ✅ 镜头焦距设置: {old_lens} -> {self.camera_lens}")
#             else:
#                 print(f"[UPBGEMMDCameraCreator] ❌ 摄像机数据没有lens属性")
            
#             print(f"[UPBGEMMDCameraCreator] ✅ 摄像机属性设置完成")
            
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ❌ 设置摄像机属性失败: {e}")
    
#     def _set_active_camera(self):
#         """设置为活动摄像机"""
#         print(f"[UPBGEMMDCameraCreator] 开始设置活动摄像机...")
        
#         if not self.set_as_active:
#             print(f"[UPBGEMMDCameraCreator] 跳过设置活动摄像机 (set_as_active=False)")
#             return
            
#         if not self.camera_obj:
#             print(f"[UPBGEMMDCameraCreator] ❌ 摄像机对象无效，无法设置为活动摄像机")
#             return
        
#         try:
#             scene = bge.logic.getCurrentScene()
#             old_camera = scene.active_camera
#             scene.active_camera = self.camera_obj
#             print(f"[UPBGEMMDCameraCreator] ✅ 活动摄像机已更改:")
#             print(f"  - 旧摄像机: {old_camera.name if old_camera else 'None'}")
#             print(f"  - 新摄像机: {self.camera_obj.name}")
            
#         except Exception as e:
#             print(f"[UPBGEMMDCameraCreator] ❌ 设置活动摄像机失败: {e}")
    
#     def destroy_camera(self):
#         """销毁MMD摄像机结构（保留原摄像机）"""
#         if not self.created:
#             print("没有MMD摄像机结构可以销毁")
#             return
        
#         try:
#             scene = bge.logic.getCurrentScene()
            
#             # 解除父子关系，恢复摄像机的独立状态
#             if self.camera_obj:
#                 camera_blender_obj = bpy.data.objects.get(self.camera_obj.name)
#                 if camera_blender_obj:
#                     # 移除父对象，但保留摄像机
#                     camera_blender_obj.parent = None
                    
#                     # 重置摄像机的位置和旋转到世界坐标
#                     camera_blender_obj.location = (0, 0, 0)
#                     camera_blender_obj.rotation_euler = (0, 0, 0)
                    
#                     # 解除锁定
#                     camera_blender_obj.lock_location = (False, False, False)
#                     camera_blender_obj.lock_rotation = (False, False, False)
#                     camera_blender_obj.lock_scale = (False, False, False)
                    
#                     print(f"摄像机 {camera_blender_obj.name} 已恢复为独立摄像机")
            
#             # 删除父对象
#             if self.parent_obj:
#                 parent_blender_obj = bpy.data.objects.get(self.parent_name)
#                 if parent_blender_obj:
#                     bpy.data.objects.remove(parent_blender_obj, do_unlink=True)
#                     print(f"已删除父对象: {self.parent_name}")
#                 self.parent_obj = None
            
#             self.camera_obj = None
#             self.created = False
#             print("MMD摄像机结构已销毁，原摄像机已恢复")
            
#         except Exception as e:
#             print(f"销毁MMD摄像机结构失败: {e}")
#             import traceback
#             traceback.print_exc()
    
#     def get_camera_objects(self):
#         """获取创建的摄像机对象"""
#         # 如果对象还没有创建，尝试重新获取
#         if self.created and (not self.parent_obj or not self.camera_obj):
#             print(f"[UPBGEMMDCameraCreator] 对象已创建但引用丢失，尝试重新获取...")
#             scene = bge.logic.getCurrentScene()
            
#             if not self.parent_obj:
#                 self.parent_obj = scene.objects.get(self.parent_name)
#                 if self.parent_obj:
#                     print(f"[UPBGEMMDCameraCreator] ✅ 重新获取父对象: {self.parent_obj.name}")
#                 else:
#                     print(f"[UPBGEMMDCameraCreator] ❌ 仍无法获取父对象: {self.parent_name}")
            
#             if not self.camera_obj:
#                 self.camera_obj = scene.objects.get(self.camera_name)
#                 if self.camera_obj:
#                     print(f"[UPBGEMMDCameraCreator] ✅ 重新获取摄像机: {self.camera_obj.name}")
#                 else:
#                     print(f"[UPBGEMMDCameraCreator] ❌ 仍无法获取摄像机: {self.camera_name}")
        
#         return {
#             'parent': self.parent_obj,
#             'camera': self.camera_obj,
#             'created': self.created,
#             'parent_name': self.parent_name,
#             'camera_name': self.camera_name
#         }
    
#     def get_object_names(self):
#         """获取创建的对象名称（用于外部查找）"""
#         return {
#             'parent_name': self.parent_name,
#             'camera_name': self.camera_name,
#             'created': self.created
#         }
    
#     def update(self):
#         """每帧更新"""
#         # 检查对象是否仍然有效
#         if self.created:
#             scene = bge.logic.getCurrentScene()
            
#             if self.parent_obj and self.parent_obj.invalid:
#                 print("父对象已失效")
#                 self.created = False
            
#             if self.camera_obj and self.camera_obj.invalid:
#                 print("摄像机对象已失效")
#                 self.created = False


# # 键盘控制组件
# class UPBGECameraKeyboardControl(bge.types.KX_PythonComponent):
#     """键盘控制摄像机创建的组件"""
    
#     args = {}
    
#     def start(self, args):
#         self.keyboard = bge.logic.keyboard
#         self.creator = None
#         print("UPBGE摄像机键盘控制已启动: C键创建, D键销毁")
    
#     def update(self):
#         # C键创建摄像机
#         if self.keyboard.events[bge.events.CKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if not self.creator:
#                 self.creator = UPBGEMMDCameraCreator()
#                 self.creator.object = self.object
#                 self.creator.start({
#                     "parent_name": "MMD_Camera",
#                     "camera_name": "Camera",
#                     "auto_create": False,
#                 })
            
#             self.creator.create_mmd_camera()
        
#         # D键销毁摄像机
#         if self.keyboard.events[bge.events.DKEY] == bge.logic.KX_INPUT_JUST_ACTIVATED:
#             if self.creator:
#                 self.creator.destroy_camera()


# # 运行时转换函数
# def create_upbge_mmd_camera(parent_name="MMD_Camera", camera_name="Camera", 
#                            position=(0, 0, 10), rotation=(0, 0, 0), 
#                            distance=-45.0, scale=1.0):
#     """
#     运行时转换现有摄像机为MMD摄像机的函数
#     将场景中的现有摄像机转换为MMD父子摄像机结构
#     """
#     try:
#         # 找一个参考对象来承载组件
#         scene = bge.logic.getCurrentScene()
#         reference_obj = None
#         for obj in scene.objects:
#             reference_obj = obj
#             break
        
#         if not reference_obj:
#             print("错误: 场景中没有对象可以作为参考")
#             return None
        
#         # 创建组件实例
#         creator = UPBGEMMDCameraCreator()
#         creator.object = reference_obj
#         creator.start({
#             "parent_name": parent_name,
#             "camera_name": camera_name,
#             "initial_position": list(position),
#             "initial_rotation": list(rotation),
#             "camera_distance": distance,
#             "scale": scale,
#             "auto_create": False,
#             "set_as_active": True,
#         })
        
#         # 创建摄像机
#         if creator.create_mmd_camera():
#             return creator.get_camera_objects()
#         else:
#             return None
            
#     except Exception as e:
#         print(f"运行时创建摄像机失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return None


# # 使用示例
# def demo_upbge_camera():
#     """演示如何在UPBGE中转换现有摄像机为MMD摄像机"""
#     print("开始转换现有摄像机为MMD摄像机...")
    
#     result = create_upbge_mmd_camera(
#         parent_name="Demo_MMD_Camera",
#         camera_name="Camera",  # 使用现有摄像机名称
#         position=(0, 0, 12),
#         rotation=(0, 0, 0),
#         distance=-50.0,
#         scale=1.0
#     )
    
#     if result and result['created']:
#         print("MMD摄像机转换成功!")
#         print(f"父对象: {result['parent'].name if result['parent'] else 'None'}")
#         print(f"摄像机: {result['camera'].name if result['camera'] else 'None'}")
#         return result
#     else:
#         print("MMD摄像机转换失败!")
#         return None 