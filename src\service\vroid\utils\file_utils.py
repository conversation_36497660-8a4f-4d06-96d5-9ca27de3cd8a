import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Union
from PIL import Image

class FileUtils:
    @staticmethod
    def ensure_directory(directory: Union[str, Path]) -> Path:
        """确保目录存在，如果不存在则创建"""
        path = Path(directory)
        path.mkdir(parents=True, exist_ok=True)
        return path

    @staticmethod
    def get_file_extension(file_path: Union[str, Path]) -> str:
        """获取文件扩展名（不包含点号）"""
        return Path(file_path).suffix.lstrip('.')

    @staticmethod
    def load_json(file_path: Union[str, Path]) -> Dict[str, Any]:
        """加载JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def save_json(data: Dict[str, Any], file_path: Union[str, Path], indent: int = 4) -> None:
        """保存JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)

    @staticmethod
    def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
        """复制文件"""
        shutil.copy2(src, dst)

    @staticmethod
    def move_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
        """移动文件"""
        shutil.move(src, dst)

    @staticmethod
    def delete_file(file_path: Union[str, Path]) -> None:
        """删除文件"""
        try:
            os.remove(file_path)
        except FileNotFoundError:
            pass

    @staticmethod
    def get_image_size(image_path: Union[str, Path]) -> tuple[int, int]:
        """获取图片尺寸"""
        with Image.open(image_path) as img:
            return img.size

    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """判断文件是否为图片"""
        try:
            Image.open(file_path)
            return True
        except:
            return False

    @staticmethod
    def get_relative_path(path: Union[str, Path], base_path: Union[str, Path]) -> str:
        """获取相对路径"""
        return os.path.relpath(str(path), str(base_path))

    @staticmethod
    def get_absolute_path(path: Union[str, Path]) -> str:
        """获取绝对路径"""
        return os.path.abspath(str(path))

    @staticmethod
    def list_files(directory: Union[str, Path], pattern: Optional[str] = None) -> list[Path]:
        """列出目录中的文件"""
        path = Path(directory)
        if pattern:
            return list(path.glob(pattern))
        return [f for f in path.iterdir() if f.is_file()]

    @staticmethod
    def read_text_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """读取文本文件"""
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()

    @staticmethod
    def write_text_file(content: str, file_path: Union[str, Path], encoding: str = 'utf-8') -> None:
        """写入文本文件"""
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)

    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小（字节）"""
        return os.path.getsize(file_path)

    @staticmethod
    def get_file_modification_time(file_path: Union[str, Path]) -> float:
        """获取文件最后修改时间（时间戳）"""
        return os.path.getmtime(file_path) 