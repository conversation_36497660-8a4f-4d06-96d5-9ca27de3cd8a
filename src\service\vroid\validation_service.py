from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import numpy as np

from mmd.PmxData import PmxModel, Vertex, Material, Bone, Morph, RigidBody, Joint
from utils.MLogger import MLogger
from utils.MException import SizingException

class ValidationService:
    def __init__(self):
        self.logger = MLogger(self.__class__.__name__)
        self.errors: List[str] = []
        self.warnings: List[str] = []

    def validate_model(self, model: PmxModel) -> bool:
        """
        验证整个模型
        
        Args:
            model: PMX模型对象
            
        Returns:
            bool: 模型是否有效
        """
        self.errors.clear()
        self.warnings.clear()
        
        try:
            # 验证基本属性
            self._validate_basic_properties(model)
            
            # 验证顶点数据
            self._validate_vertices(model)
            
            # 验证材质数据
            self._validate_materials(model)
            
            # 验证骨骼数据
            self._validate_bones(model)
            
            # 验证变形数据
            self._validate_morphs(model)
            
            # 验证刚体和关节
            self._validate_physics(model)
            
            # 如果有错误，返回False
            if self.errors:
                error_msg = "\n".join(self.errors)
                raise SizingException(f"模型验证失败:\n{error_msg}")
            
            # 输出警告信息
            if self.warnings:
                warning_msg = "\n".join(self.warnings)
                self.logger.warning(f"模型验证警告:\n{warning_msg}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"模型验证过程中发生错误: {str(e)}")
            return False

    def _validate_basic_properties(self, model: PmxModel) -> None:
        """验证模型的基本属性"""
        if not model.name:
            self.errors.append("模型名称不能为空")
        
        if not model.english_name:
            self.warnings.append("模型英文名称为空")
        
        if not model.comment:
            self.warnings.append("模型说明为空")

    def _validate_vertices(self, model: PmxModel) -> None:
        """验证顶点数据"""
        if not model.vertex_dict:
            self.errors.append("模型没有顶点数据")
            return
        
        # 检查顶点索引范围
        max_vertex_idx = max(model.vertex_dict.keys())
        if max_vertex_idx >= len(model.vertex_dict):
            self.errors.append(f"顶点索引超出范围: {max_vertex_idx}")
        
        # 验证每个顶点
        for idx, vertex in model.vertex_dict.items():
            # 检查位置 - MVector3D对象
            if hasattr(vertex.position, 'x') and hasattr(vertex.position, 'y') and hasattr(vertex.position, 'z'):
                pos_values = [vertex.position.x(), vertex.position.y(), vertex.position.z()]
                if not all(isinstance(x, (int, float)) for x in pos_values):
                    self.errors.append(f"顶点 {idx} 的位置数据无效")
            else:
                self.errors.append(f"顶点 {idx} 的位置对象无效")

            # 检查法线 - MVector3D对象
            if hasattr(vertex.normal, 'x') and hasattr(vertex.normal, 'y') and hasattr(vertex.normal, 'z'):
                normal_values = [vertex.normal.x(), vertex.normal.y(), vertex.normal.z()]
                if not all(isinstance(x, (int, float)) for x in normal_values):
                    self.errors.append(f"顶点 {idx} 的法线数据无效")
            else:
                self.errors.append(f"顶点 {idx} 的法线对象无效")

            # 检查UV坐标 - MVector2D对象
            if hasattr(vertex.uv, 'x') and hasattr(vertex.uv, 'y'):
                uv_values = [vertex.uv.x(), vertex.uv.y()]
                if not all(isinstance(x, (int, float)) for x in uv_values):
                    self.errors.append(f"顶点 {idx} 的UV数据无效")
            else:
                self.errors.append(f"顶点 {idx} 的UV对象无效")

            # 检查权重
            if not vertex.deform:
                self.errors.append(f"顶点 {idx} 没有权重数据")

    def _validate_materials(self, model: PmxModel) -> None:
        """验证材质数据"""
        if not model.materials:
            self.errors.append("模型没有材质数据")
            return

        used_textures = set()

        for idx, (material_name, material) in enumerate(model.materials.items()):
            # 检查材质名称
            if not material.name:
                self.warnings.append(f"材质 {idx} 没有名称")
            
            # 检查颜色值
            for color_attr in ['diffuse_color', 'specular_color', 'ambient_color']:
                color = getattr(material, color_attr)
                # 检查颜色对象是否有效
                if hasattr(color, 'x') and hasattr(color, 'y') and hasattr(color, 'z'):
                    color_values = [color.x(), color.y(), color.z()]
                    if not all(0 <= x <= 1 for x in color_values):
                        self.errors.append(f"材质 {idx} 的 {color_attr} 颜色值超出范围")
                elif hasattr(color, '__iter__'):
                    # 如果是可迭代对象（如列表或元组）
                    if not all(0 <= x <= 1 for x in color):
                        self.errors.append(f"材质 {idx} 的 {color_attr} 颜色值超出范围")
                else:
                    self.errors.append(f"材质 {idx} 的 {color_attr} 颜色对象无效")
            
            # 检查纹理索引
            if material.texture_index >= 0:
                used_textures.add(material.texture_index)
                if material.texture_index >= len(model.textures):
                    self.errors.append(f"材质 {idx} 的纹理索引无效: {material.texture_index}")
        
        # 检查未使用的纹理
        all_textures = set(range(len(model.textures)))
        unused_textures = all_textures - used_textures
        if unused_textures:
            self.warnings.append(f"存在未使用的纹理: {unused_textures}")

    def _validate_bones(self, model: PmxModel) -> None:
        """验证骨骼数据"""
        if not model.bones:
            self.errors.append("模型没有骨骼数据")
            return

        bone_indices = set()

        for idx, (bone_name, bone) in enumerate(model.bones.items()):
            # 检查骨骼名称
            if not bone.name:
                self.warnings.append(f"骨骼 {idx} 没有名称")
            
            # 检查父骨骼索引
            if bone.parent_index >= 0:
                if bone.parent_index >= len(model.bones):
                    self.errors.append(f"骨骼 {idx} 的父骨骼索引无效: {bone.parent_index}")
                elif bone.parent_index == idx:
                    self.errors.append(f"骨骼 {idx} 的父骨骼索引指向自身")

            bone_indices.add(idx)

            # 检查IK链
            if bone.ik:
                if bone.ik.target_index >= len(model.bones):
                    self.errors.append(f"骨骼 {idx} 的IK目标骨骼索引无效")
                for link in bone.ik.link_list:
                    if link.bone_index >= len(model.bones):
                        self.errors.append(f"骨骼 {idx} 的IK链骨骼索引无效")
        
        # 检查骨骼引用
        self._validate_bone_references(model, bone_indices)

    def _validate_bone_references(self, model: PmxModel, valid_bones: Set[int]) -> None:
        """验证骨骼引用"""
        # 检查顶点权重中的骨骼引用
        for idx, vertex in model.vertex_dict.items():
            bone_indices = vertex.deform.get_idx_list()
            for bone_idx in bone_indices:
                if bone_idx not in valid_bones:
                    self.errors.append(f"顶点 {idx} 引用了无效的骨骼索引: {bone_idx}")

    def _validate_morphs(self, model: PmxModel) -> None:
        """验证变形数据"""
        if not model.morphs:
            self.warnings.append("模型没有变形数据")
            return

        for idx, (morph_name, morph) in enumerate(model.morphs.items()):
            # 检查变形名称
            if not morph.name:
                self.warnings.append(f"变形 {idx} 没有名称")
            
            # 检查变形类型
            if morph.panel < 0 or morph.panel > 4:
                self.errors.append(f"变形 {idx} 的面板类型无效: {morph.panel}")
            
            # 检查变形偏移数据
            if not morph.offset_list:
                self.warnings.append(f"变形 {idx} 没有偏移数据")

    def _validate_physics(self, model: PmxModel) -> None:
        """验证物理数据"""
        if not model.rigidbodies:
            self.warnings.append("模型没有刚体数据")
        else:
            self._validate_rigid_bodies(model)

        if not model.joints:
            self.warnings.append("模型没有关节数据")
        else:
            self._validate_joints(model)

    def _validate_rigid_bodies(self, model: PmxModel) -> None:
        """验证刚体数据"""
        for idx, (body_name, body) in enumerate(model.rigidbodies.items()):
            # 检查刚体名称
            if not body.name:
                self.warnings.append(f"刚体 {idx} 没有名称")
            
            # 检查关联骨骼
            if body.bone_index >= 0 and body.bone_index >= len(model.bones):
                self.errors.append(f"刚体 {idx} 的关联骨骼索引无效: {body.bone_index}")
            
            # 检查形状类型
            if body.shape_type not in [0, 1, 2]:
                self.errors.append(f"刚体 {idx} 的形状类型无效: {body.shape_type}")
            
            # 检查大小参数
            if hasattr(body.size, 'x') and hasattr(body.size, 'y') and hasattr(body.size, 'z'):
                size_values = [body.size.x(), body.size.y(), body.size.z()]
                if any(x <= 0 for x in size_values):
                    self.errors.append(f"刚体 {idx} 的大小参数无效: {size_values}")
            elif hasattr(body.size, '__iter__'):
                if any(x <= 0 for x in body.size):
                    self.errors.append(f"刚体 {idx} 的大小参数无效: {body.size}")
            else:
                self.errors.append(f"刚体 {idx} 的大小对象无效")

    def _validate_joints(self, model: PmxModel) -> None:
        """验证关节数据"""
        for idx, (joint_name, joint) in enumerate(model.joints.items()):
            # 检查关节名称
            if not joint.name:
                self.warnings.append(f"关节 {idx} 没有名称")
            
            # 检查关联刚体
            if joint.rigid_body_index_a >= len(model.rigidbodies) or \
               joint.rigid_body_index_b >= len(model.rigidbodies):
                self.errors.append(f"关节 {idx} 的关联刚体索引无效")
            
            # 检查运动限制
            for limit_name, limit in [('movement_limit_min', joint.movement_limit_min),
                                    ('movement_limit_max', joint.movement_limit_max),
                                    ('rotation_limit_min', joint.rotation_limit_min),
                                    ('rotation_limit_max', joint.rotation_limit_max)]:
                if hasattr(limit, 'x') and hasattr(limit, 'y') and hasattr(limit, 'z'):
                    limit_values = [limit.x(), limit.y(), limit.z()]
                    if any(not isinstance(x, (int, float)) for x in limit_values):
                        self.errors.append(f"关节 {idx} 的 {limit_name} 参数无效")
                elif hasattr(limit, '__iter__'):
                    if any(not isinstance(x, (int, float)) for x in limit):
                        self.errors.append(f"关节 {idx} 的 {limit_name} 参数无效")
                else:
                    self.errors.append(f"关节 {idx} 的 {limit_name} 对象无效")