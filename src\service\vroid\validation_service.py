from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import numpy as np

from mmd.PmxData import PmxModel, Vertex, Material, Bone, Morph, RigidBody, Joint
from utils.MLogger import MLogger
from utils.MException import SizingException

class ValidationService:
    def __init__(self):
        self.logger = MLogger(self.__class__.__name__)
        self.errors: List[str] = []
        self.warnings: List[str] = []

    def validate_model(self, model: PmxModel) -> bool:
        """
        验证整个模型
        
        Args:
            model: PMX模型对象
            
        Returns:
            bool: 模型是否有效
        """
        self.errors.clear()
        self.warnings.clear()
        
        try:
            # 验证基本属性
            self._validate_basic_properties(model)
            
            # 验证顶点数据
            self._validate_vertices(model)
            
            # 验证材质数据
            self._validate_materials(model)
            
            # 验证骨骼数据
            self._validate_bones(model)
            
            # 验证变形数据
            self._validate_morphs(model)
            
            # 验证刚体和关节
            self._validate_physics(model)
            
            # 如果有错误，返回False
            if self.errors:
                error_msg = "\n".join(self.errors)
                raise SizingException(f"模型验证失败:\n{error_msg}")
            
            # 输出警告信息
            if self.warnings:
                warning_msg = "\n".join(self.warnings)
                self.logger.warning(f"模型验证警告:\n{warning_msg}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"模型验证过程中发生错误: {str(e)}")
            return False

    def _validate_basic_properties(self, model: PmxModel) -> None:
        """验证模型的基本属性"""
        if not model.name:
            self.errors.append("模型名称不能为空")
        
        if not model.english_name:
            self.warnings.append("模型英文名称为空")
        
        if not model.comment:
            self.warnings.append("模型说明为空")

    def _validate_vertices(self, model: PmxModel) -> None:
        """验证顶点数据"""
        if not model.vertex_dict:
            self.errors.append("模型没有顶点数据")
            return
        
        # 检查顶点索引范围
        max_vertex_idx = max(model.vertex_dict.keys())
        if max_vertex_idx >= len(model.vertex_dict):
            self.errors.append(f"顶点索引超出范围: {max_vertex_idx}")
        
        # 验证每个顶点
        for idx, vertex in model.vertex_dict.items():
            # 检查位置
            if not all(isinstance(x, (int, float)) for x in vertex.position):
                self.errors.append(f"顶点 {idx} 的位置数据无效")
            
            # 检查法线
            if not all(isinstance(x, (int, float)) for x in vertex.normal):
                self.errors.append(f"顶点 {idx} 的法线数据无效")
            
            # 检查UV坐标
            if not all(isinstance(x, (int, float)) for x in vertex.uv):
                self.errors.append(f"顶点 {idx} 的UV数据无效")
            
            # 检查权重
            if not vertex.deform:
                self.errors.append(f"顶点 {idx} 没有权重数据")

    def _validate_materials(self, model: PmxModel) -> None:
        """验证材质数据"""
        if not model.materials:
            self.errors.append("模型没有材质数据")
            return
        
        used_textures = set()
        
        for idx, material in enumerate(model.materials):
            # 检查材质名称
            if not material.name:
                self.warnings.append(f"材质 {idx} 没有名称")
            
            # 检查颜色值
            for color_attr in ['diffuse_color', 'specular_color', 'ambient_color']:
                color = getattr(material, color_attr)
                if not all(0 <= x <= 1 for x in color):
                    self.errors.append(f"材质 {idx} 的 {color_attr} 颜色值超出范围")
            
            # 检查纹理索引
            if material.texture_index >= 0:
                used_textures.add(material.texture_index)
                if material.texture_index >= len(model.textures):
                    self.errors.append(f"材质 {idx} 的纹理索引无效: {material.texture_index}")
        
        # 检查未使用的纹理
        all_textures = set(range(len(model.textures)))
        unused_textures = all_textures - used_textures
        if unused_textures:
            self.warnings.append(f"存在未使用的纹理: {unused_textures}")

    def _validate_bones(self, model: PmxModel) -> None:
        """验证骨骼数据"""
        if not model.bones:
            self.errors.append("模型没有骨骼数据")
            return
        
        bone_indices = set()
        
        for idx, bone in enumerate(model.bones):
            # 检查骨骼名称
            if not bone.name:
                self.warnings.append(f"骨骼 {idx} 没有名称")
            
            # 检查父骨骼索引
            if bone.parent_index >= 0:
                if bone.parent_index >= len(model.bones):
                    self.errors.append(f"骨骼 {idx} 的父骨骼索引无效: {bone.parent_index}")
                elif bone.parent_index == idx:
                    self.errors.append(f"骨骼 {idx} 的父骨骼索引指向自身")
            
            bone_indices.add(idx)
            
            # 检查IK链
            if bone.ik:
                if bone.ik.target_index >= len(model.bones):
                    self.errors.append(f"骨骼 {idx} 的IK目标骨骼索引无效")
                for link in bone.ik.link_list:
                    if link.bone_index >= len(model.bones):
                        self.errors.append(f"骨骼 {idx} 的IK链骨骼索引无效")
        
        # 检查骨骼引用
        self._validate_bone_references(model, bone_indices)

    def _validate_bone_references(self, model: PmxModel, valid_bones: Set[int]) -> None:
        """验证骨骼引用"""
        # 检查顶点权重中的骨骼引用
        for idx, vertex in model.vertex_dict.items():
            bone_indices = vertex.deform.get_idx_list()
            for bone_idx in bone_indices:
                if bone_idx not in valid_bones:
                    self.errors.append(f"顶点 {idx} 引用了无效的骨骼索引: {bone_idx}")

    def _validate_morphs(self, model: PmxModel) -> None:
        """验证变形数据"""
        if not model.morphs:
            self.warnings.append("模型没有变形数据")
            return
        
        for idx, morph in enumerate(model.morphs):
            # 检查变形名称
            if not morph.name:
                self.warnings.append(f"变形 {idx} 没有名称")
            
            # 检查变形类型
            if morph.panel < 0 or morph.panel > 4:
                self.errors.append(f"变形 {idx} 的面板类型无效: {morph.panel}")
            
            # 检查变形偏移数据
            if not morph.offset_list:
                self.warnings.append(f"变形 {idx} 没有偏移数据")

    def _validate_physics(self, model: PmxModel) -> None:
        """验证物理数据"""
        if not model.rigid_bodies:
            self.warnings.append("模型没有刚体数据")
        else:
            self._validate_rigid_bodies(model)
        
        if not model.joints:
            self.warnings.append("模型没有关节数据")
        else:
            self._validate_joints(model)

    def _validate_rigid_bodies(self, model: PmxModel) -> None:
        """验证刚体数据"""
        for idx, body in enumerate(model.rigid_bodies):
            # 检查刚体名称
            if not body.name:
                self.warnings.append(f"刚体 {idx} 没有名称")
            
            # 检查关联骨骼
            if body.bone_index >= 0 and body.bone_index >= len(model.bones):
                self.errors.append(f"刚体 {idx} 的关联骨骼索引无效: {body.bone_index}")
            
            # 检查形状类型
            if body.shape_type not in [0, 1, 2]:
                self.errors.append(f"刚体 {idx} 的形状类型无效: {body.shape_type}")
            
            # 检查大小参数
            if any(x <= 0 for x in body.size):
                self.errors.append(f"刚体 {idx} 的大小参数无效: {body.size}")

    def _validate_joints(self, model: PmxModel) -> None:
        """验证关节数据"""
        for idx, joint in enumerate(model.joints):
            # 检查关节名称
            if not joint.name:
                self.warnings.append(f"关节 {idx} 没有名称")
            
            # 检查关联刚体
            if joint.rigid_body_index_a >= len(model.rigid_bodies) or \
               joint.rigid_body_index_b >= len(model.rigid_bodies):
                self.errors.append(f"关节 {idx} 的关联刚体索引无效")
            
            # 检查运动限制
            for limit in [joint.movement_limit_min, joint.movement_limit_max,
                         joint.rotation_limit_min, joint.rotation_limit_max]:
                if any(not isinstance(x, (int, float)) for x in limit):
                    self.errors.append(f"关节 {idx} 的运动限制参数无效") 