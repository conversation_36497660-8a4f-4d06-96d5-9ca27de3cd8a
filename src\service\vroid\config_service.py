"""配置服务模块，处理全局配置和常量。

此模块包含了VRoid到PMX转换过程中需要的各种配置信息，如：
- MIME类型映射
- MMD单位转换常量
- 日志配置等
"""

import logging
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import json
import os

from utils.MLogger import MLogger
from .utils.file_utils import FileUtils

# MIME类型到文件扩展名的映射
MIME_TYPE: Dict[str, str] = {
    "image/png": "png",
    "image/jpeg": "jpg",
    "image/ktx": "ktx",
    "image/ktx2": "ktx2",
    "image/webp": "webp",
    "image/vnd-ms.dds": "dds",
    "audio/wav": "wav",
}

# MMD中的单位转换常量（1cm = 0.125米克塞尔，1m = 12.5）
MIKU_METER: float = 12.5

class ConfigService:
    """配置服务类，提供全局配置管理功能"""
    
    def __init__(self):
        self.logger = MLogger(self.__class__.__name__)
        self.config: Dict[str, Any] = {}
        self.default_config: Dict[str, Any] = self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "model": {
                "scale": 1.0,
                "center_model": True,
                "normalize_weights": True,
                "weight_threshold": 0.0001,
                "max_bone_influences": 4
            },
            "physics": {
                "enable_physics": True,
                "gravity": -9.8,
                "damping": 0.5,
                "mass": 1.0,
                "collision_margin": 0.04
            },
            "materials": {
                "texture_size": 1024,
                "generate_normalmap": True,
                "texture_format": "png",
                "compress_textures": False
            },
            "bones": {
                "generate_tip_bones": True,
                "merge_similar_bones": False,
                "min_bone_length": 0.1
            },
            "morphs": {
                "generate_vertex_morphs": True,
                "generate_material_morphs": True,
                "morph_threshold": 0.00001
            },
            "export": {
                "encoding": "utf-16",
                "overwrite_existing": False,
                "backup_original": True,
                "create_pmx_folder": True
            },
            "optimization": {
                "remove_unused_vertices": True,
                "remove_unused_bones": True,
                "remove_unused_morphs": True,
                "merge_similar_materials": False
            }
        }

    def load_config(self, config_path: Union[str, Path]) -> bool:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            config_data = FileUtils.load_json(config_path)
            self.config = self._merge_with_defaults(config_data)
            self.logger.info(f"成功加载配置文件: {config_path}")
            return True
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            self.config = self.default_config.copy()
            return False

    def save_config(self, config_path: Union[str, Path]) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件保存路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            FileUtils.save_json(self.config, config_path)
            self.logger.info(f"成功保存配置文件: {config_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {str(e)}")
            return False

    def _merge_with_defaults(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将用户配置与默认配置合并
        
        Args:
            config_data: 用户配置数据
            
        Returns:
            合并后的配置
        """
        merged = self.default_config.copy()
        
        def merge_dict(target: Dict[str, Any], source: Dict[str, Any]) -> None:
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    merge_dict(target[key], value)
                else:
                    target[key] = value
        
        merge_dict(merged, config_data)
        return merged

    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径（使用.分隔，如 'model.scale'）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            value = self.config
            for key in key_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set_value(self, key_path: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 要设置的值
        """
        keys = key_path.split('.')
        target = self.config
        
        # 遍历到最后一个键之前
        for key in keys[:-1]:
            if key not in target:
                target[key] = {}
            target = target[key]
        
        # 设置最后一个键的值
        target[keys[-1]] = value

    def validate_config(self) -> List[str]:
        """
        验证配置有效性
        
        Returns:
            List[str]: 错误消息列表
        """
        errors = []
        
        # 验证模型配置
        model_config = self.config.get('model', {})
        if not isinstance(model_config.get('scale'), (int, float)) or model_config.get('scale') <= 0:
            errors.append("模型缩放比例必须大于0")
        
        # 验证物理配置
        physics_config = self.config.get('physics', {})
        if physics_config.get('enable_physics'):
            if not isinstance(physics_config.get('gravity'), (int, float)):
                errors.append("重力值必须是数字")
            if not isinstance(physics_config.get('damping'), (int, float)) or \
               not 0 <= physics_config.get('damping') <= 1:
                errors.append("阻尼值必须在0到1之间")
        
        # 验证材质配置
        materials_config = self.config.get('materials', {})
        if materials_config.get('texture_size') not in [256, 512, 1024, 2048, 4096]:
            errors.append("纹理尺寸必须是2的幂次方（256-4096）")
        
        # 验证骨骼配置
        bones_config = self.config.get('bones', {})
        if bones_config.get('min_bone_length') <= 0:
            errors.append("最小骨骼长度必须大于0")
        
        return errors

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置节
        
        Args:
            section: 配置节名称
            
        Returns:
            配置节数据
        """
        return self.config.get(section, {})

    def reset_section(self, section: str) -> None:
        """
        重置配置节到默认值
        
        Args:
            section: 配置节名称
        """
        if section in self.default_config:
            self.config[section] = self.default_config[section].copy()

    def reset_all(self) -> None:
        """重置所有配置到默认值"""
        self.config = self.default_config.copy()

    def get_modified_values(self) -> Dict[str, Any]:
        """
        获取与默认值不同的配置项
        
        Returns:
            修改过的配置项
        """
        modified = {}
        
        def compare_dict(current: Dict[str, Any], default: Dict[str, Any], path: str = "") -> None:
            for key, value in current.items():
                current_path = f"{path}.{key}" if path else key
                if key in default:
                    if isinstance(value, dict) and isinstance(default[key], dict):
                        compare_dict(value, default[key], current_path)
                    elif value != default[key]:
                        modified[current_path] = value
                else:
                    modified[current_path] = value
        
        compare_dict(self.config, self.default_config)
        return modified

    @staticmethod
    def setup_logging(options) -> None:
        """设置日志配置
        
        Args:
            options: 包含logging_level的选项对象
        """
        logging.basicConfig(
            level=options.logging_level,
            format="%(message)s [%(module_name)s]"
        )
    
    @staticmethod
    def get_file_extension(mime_type: str) -> str:
        """根据MIME类型获取文件扩展名
        
        Args:
            mime_type: MIME类型字符串
            
        Returns:
            对应的文件扩展名
        """
        return MIME_TYPE.get(mime_type, "")
    
    @staticmethod
    def convert_to_mmd_units(meters: float) -> float:
        """将米转换为MMD单位
        
        Args:
            meters: 米为单位的值
            
        Returns:
            MMD单位的值
        """
        return meters * MIKU_METER 