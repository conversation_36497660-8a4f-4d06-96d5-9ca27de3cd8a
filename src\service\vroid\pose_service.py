from typing import Dict, List, Tuple, Any
import numpy as np
from mmd.PmxData import PmxModel, Bone, Bdef1, Bdef2, Bdef4
from mmd.VmdData import VmdMotion
from module.MMath import MVector3D, MQuaternion, MMatrix4x4
from utils.MLogger import <PERSON><PERSON>ogger
from utils.MServiceUtils import MServiceUtils

logger = MLogger(__name__, level=1)

class PoseService:
    # 骨骼对应关系
    BONE_PAIRS = {
        "Root": {"name": "全ての親", "display": "全ての親"},
        "Hips": {"name": "下半身", "display": "下半身"},
        "Spine": {"name": "上半身", "display": "上半身"},
        "Chest": {"name": "上半身2", "display": "上半身2"},
        "UpperChest": {"name": "上半身3", "display": "上半身3"},
        "Neck": {"name": "首", "display": "首"},
        "Head": {"name": "頭", "display": "頭"},
        "LeftEye": {"name": "左目", "display": "目"},
        "RightEye": {"name": "右目", "display": "目"},
        "LeftShoulder": {"name": "左肩", "display": "肩"},
        "LeftUpperArm": {"name": "左腕", "display": "腕"},
        "LeftLowerArm": {"name": "左ひじ", "display": "ひじ"},
        "LeftHand": {"name": "左手首", "display": "手首"},
        "RightShoulder": {"name": "右肩", "display": "肩"},
        "RightUpperArm": {"name": "右腕", "display": "腕"},
        "RightLowerArm": {"name": "右ひじ", "display": "ひじ"},
        "RightHand": {"name": "右手首", "display": "手首"},
        "LeftUpperLeg": {"name": "左足", "display": "足"},
        "LeftLowerLeg": {"name": "左ひざ", "display": "ひざ"},
        "LeftFoot": {"name": "左足首", "display": "足首"},
        "LeftToes": {"name": "左つま先", "display": "つま先"},
        "RightUpperLeg": {"name": "右足", "display": "足"},
        "RightLowerLeg": {"name": "右ひざ", "display": "ひざ"},
        "RightFoot": {"name": "右足首", "display": "足首"},
        "RightToes": {"name": "右つま先", "display": "つま先"},
        "LeftThumbProximal": {"name": "左親指０", "display": "指"},
        "LeftThumbIntermediate": {"name": "左親指１", "display": "指"},
        "LeftThumbDistal": {"name": "左親指先", "display": "指"},
        "LeftIndexProximal": {"name": "左人指１", "display": "指"},
        "LeftIndexIntermediate": {"name": "左人指２", "display": "指"},
        "LeftIndexDistal": {"name": "左人指先", "display": "指"},
        "LeftMiddleProximal": {"name": "左中指１", "display": "指"},
        "LeftMiddleIntermediate": {"name": "左中指２", "display": "指"},
        "LeftMiddleDistal": {"name": "左中指先", "display": "指"},
        "LeftRingProximal": {"name": "左薬指１", "display": "指"},
        "LeftRingIntermediate": {"name": "左薬指２", "display": "指"},
        "LeftRingDistal": {"name": "左薬指先", "display": "指"},
        "LeftLittleProximal": {"name": "左小指１", "display": "指"},
        "LeftLittleIntermediate": {"name": "左小指２", "display": "指"},
        "LeftLittleDistal": {"name": "左小指先", "display": "指"},
        "RightThumbProximal": {"name": "右親指０", "display": "指"},
        "RightThumbIntermediate": {"name": "右親指１", "display": "指"},
        "RightThumbDistal": {"name": "右親指先", "display": "指"},
        "RightIndexProximal": {"name": "右人指１", "display": "指"},
        "RightIndexIntermediate": {"name": "右人指２", "display": "指"},
        "RightIndexDistal": {"name": "右人指先", "display": "指"},
        "RightMiddleProximal": {"name": "右中指１", "display": "指"},
        "RightMiddleIntermediate": {"name": "右中指２", "display": "指"},
        "RightMiddleDistal": {"name": "右中指先", "display": "指"},
        "RightRingProximal": {"name": "右薬指１", "display": "指"},
        "RightRingIntermediate": {"name": "右薬指２", "display": "指"},
        "RightRingDistal": {"name": "右薬指先", "display": "指"},
        "RightLittleProximal": {"name": "右小指１", "display": "指"},
        "RightLittleIntermediate": {"name": "右小指２", "display": "指"},
        "RightLittleDistal": {"name": "右小指先", "display": "指"},
    }

    @staticmethod
    def transfer_stance(model: PmxModel) -> PmxModel:
        """转换模型姿势

        Args:
            model (PmxModel): PMX模型

        Returns:
            PmxModel: 转换后的模型
        """
        # 计算所有顶点的相对位置
        all_vertex_relative_poses = PoseService._calculate_vertex_relative_poses(model)

        # 初始化变换矩阵和向量
        trans_bone_vecs = {"全ての親": MVector3D()}
        trans_bone_mats = {"全ての親": MMatrix4x4().setToIdentity()}
        trans_vertex_vecs = {}
        trans_normal_vecs = {}

        # 获取需要处理的骨骼名称
        bone_names = PoseService._get_target_bone_names(model)

        # 处理每个目标骨骼
        for end_bone_name in bone_names:
            bone_links = model.create_link_2_top_one(end_bone_name, is_defined=False).to_links("上半身")
            if len(bone_links.all().keys()) == 0:
                continue

            # 计算相对位置
            trans_vs = MServiceUtils.calc_relative_position(model, bone_links, VmdMotion(), 0)

            # 处理骨骼变换
            PoseService._process_bone_transforms(
                model, bone_links, trans_vs, trans_bone_vecs, trans_bone_mats
            )

        # 更新骨骼位置
        for bone_name, bone_vec in trans_bone_vecs.items():
            model.bones[bone_name].position = bone_vec

        # 更新骨骼方向
        PoseService._update_bone_directions(model, trans_bone_mats)

        # 更新顶点位置
        PoseService._update_vertex_positions(
            model, all_vertex_relative_poses, trans_bone_mats, trans_vertex_vecs, trans_normal_vecs
        )

        return model

    @staticmethod
    def _calculate_vertex_relative_poses(model: PmxModel) -> Dict[int, List[MVector3D]]:
        """计算顶点相对位置

        Args:
            model (PmxModel): PMX模型

        Returns:
            Dict[int, List[MVector3D]]: 顶点相对位置映射
        """
        all_vertex_relative_poses = {}
        for vertex in model.vertex_dict.values():
            if type(vertex.deform) is Bdef1:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.bone_index]].position
                ]
            elif type(vertex.deform) is Bdef2:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.bone_index1]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.bone_index2]].position,
                ]
            elif type(vertex.deform) is Bdef4:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index0]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index1]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index2]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index3]].position,
                ]
        return all_vertex_relative_poses

    @staticmethod
    def _get_target_bone_names(model: PmxModel) -> List[str]:
        """获取需要处理的骨骼名称

        Args:
            model (PmxModel): PMX模型

        Returns:
            List[str]: 骨骼名称列表
        """
        bone_names = ["頭"]

        for direction in ["右", "左"]:
            bone_names.extend(
                [
                    f"{direction}親指先",
                    f"{direction}人指先",
                    f"{direction}中指先",
                    f"{direction}薬指先",
                    f"{direction}小指先",
                    f"{direction}胸先",
                    f"{direction}腕捩1",
                    f"{direction}腕捩2",
                    f"{direction}腕捩3",
                    f"{direction}手捩1",
                    f"{direction}手捩2",
                    f"{direction}手捩3",
                ]
            )

        # 装饰骨骼放在人体后面
        for bname in model.bones.keys():
            if "装飾_" in bname:
                bone_names.append(bname)

        return bone_names

    @staticmethod
    def _process_bone_transforms(
        model: PmxModel,
        bone_links: Any,
        trans_vs: List[MVector3D],
        trans_bone_vecs: Dict[str, MVector3D],
        trans_bone_mats: Dict[str, MMatrix4x4]
    ):
        """处理骨骼变换

        Args:
            model (PmxModel): PMX模型
            bone_links (Any): 骨骼链接
            trans_vs (List[MVector3D]): 变换向量列表
            trans_bone_vecs (Dict[str, MVector3D]): 骨骼变换向量映射
            trans_bone_mats (Dict[str, MMatrix4x4]): 骨骼变换矩阵映射
        """
        # 判断是左侧还是右侧
        if "右" in ",".join(list(bone_links.all().keys())):
            arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, 35)
            arm_bone_name = "右腕"
            thumb0_stance_qq = MQuaternion.fromEulerAngles(0, 8, 0)
            thumb0_bone_name = "右親指０"
            thumb1_stance_qq = MQuaternion.fromEulerAngles(0, 24, 0)
            thumb1_bone_name = "右親指１"
        elif "左" in ",".join(list(bone_links.all().keys())):
            arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, -35)
            arm_bone_name = "左腕"
            thumb0_stance_qq = MQuaternion.fromEulerAngles(0, -8, 0)
            thumb0_bone_name = "左親指０"
            thumb1_stance_qq = MQuaternion.fromEulerAngles(0, -24, 0)
            thumb1_bone_name = "左親指１"
        else:
            arm_astance_qq = MQuaternion.fromEulerAngles(0, 0, 0)
            arm_bone_name = ""
            thumb0_bone_name = ""
            thumb1_bone_name = ""

        # 应用变换
        mat = MMatrix4x4()
        mat.setToIdentity()
        for bone_name, trans_v in zip(bone_links.all().keys(), trans_vs):
            mat.translate(trans_v)
            if bone_name == arm_bone_name:
                # 旋转手臂
                mat.rotate(arm_astance_qq)
            elif bone_name == thumb0_bone_name:
                # 旋转大拇指0
                mat.rotate(thumb0_stance_qq)
            elif bone_name == thumb1_bone_name:
                # 旋转大拇指1
                mat.rotate(thumb1_stance_qq)

            if bone_name not in trans_bone_vecs:
                trans_bone_vecs[bone_name] = mat * MVector3D()
                trans_bone_mats[bone_name] = mat.copy()

    @staticmethod
    def _update_bone_directions(model: PmxModel, trans_bone_mats: Dict[str, MMatrix4x4]):
        """更新骨骼方向

        Args:
            model (PmxModel): PMX模型
            trans_bone_mats (Dict[str, MMatrix4x4]): 骨骼变换矩阵映射
        """
        local_y_vector = MVector3D(0, -1, 0)

        for bone_name in trans_bone_mats.keys():
            bone = model.bones[bone_name]
            direction = bone_name[0]
            arm_bone_name = f"{direction}腕"
            elbow_bone_name = f"{direction}ひじ"
            wrist_bone_name = f"{direction}手首"
            finger_bone_name = f"{direction}中指１"

            # 更新局部轴
            if bone.name in ["右肩", "左肩"] and arm_bone_name in model.bones:
                bone.local_x_vector = (
                    model.bones[arm_bone_name].position - model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

            if bone.name in ["右腕", "左腕"] and elbow_bone_name in model.bones:
                bone.local_x_vector = (
                    model.bones[elbow_bone_name].position - model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

            if bone.name in ["右ひじ", "左ひじ"] and wrist_bone_name in model.bones:
                # 用局部Y轴弯曲
                bone.local_x_vector = (
                    model.bones[wrist_bone_name].position - model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(local_y_vector, bone.local_x_vector).normalized()

            if bone.name in ["右手首", "左手首"] and finger_bone_name in model.bones:
                bone.local_x_vector = (
                    model.bones[finger_bone_name].position - model.bones[bone.name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

            # 处理扭转
            if bone.name in ["右腕捩", "左腕捩"] and arm_bone_name in model.bones and elbow_bone_name in model.bones:
                bone.fixed_axis = (
                    model.bones[elbow_bone_name].position - model.bones[arm_bone_name].position
                ).normalized()
                bone.local_x_vector = (
                    model.bones[elbow_bone_name].position - model.bones[arm_bone_name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

            if bone.name in ["右手捩", "左手捩"] and elbow_bone_name in model.bones and wrist_bone_name in model.bones:
                bone.fixed_axis = (
                    model.bones[wrist_bone_name].position - model.bones[elbow_bone_name].position
                ).normalized()
                bone.local_x_vector = (
                    model.bones[wrist_bone_name].position - model.bones[elbow_bone_name].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

            # 处理手指
            if (
                bone.english_name in PoseService.BONE_PAIRS
                and PoseService.BONE_PAIRS[bone.english_name]["display"]
                and "指" in PoseService.BONE_PAIRS[bone.english_name]["display"]
            ):
                bone.local_x_vector = (
                    model.bones[model.bone_indexes[bone.tail_index]].position
                    - model.bones[model.bone_indexes[bone.parent_index]].position
                ).normalized()
                bone.local_z_vector = MVector3D.crossProduct(bone.local_x_vector, local_y_vector).normalized()

    @staticmethod
    def _update_vertex_positions(
        model: PmxModel,
        all_vertex_relative_poses: Dict[int, List[MVector3D]],
        trans_bone_mats: Dict[str, MMatrix4x4],
        trans_vertex_vecs: Dict[int, MVector3D],
        trans_normal_vecs: Dict[int, MVector3D]
    ):
        """更新顶点位置

        Args:
            model (PmxModel): PMX模型
            all_vertex_relative_poses (Dict[int, List[MVector3D]]): 顶点相对位置映射
            trans_bone_mats (Dict[str, MMatrix4x4]): 骨骼变换矩阵映射
            trans_vertex_vecs (Dict[int, MVector3D]): 顶点变换向量映射
            trans_normal_vecs (Dict[int, MVector3D]): 法线变换向量映射
        """
        for vertex_idx, vertex_relative_poses in all_vertex_relative_poses.items():
            if vertex_idx not in trans_vertex_vecs:
                vertex = model.vertex_dict[vertex_idx]
                if type(vertex.deform) is Bdef1 and model.bone_indexes[vertex.deform.index0] in trans_bone_mats:
                    trans_vertex_vecs[vertex.index] = (
                        trans_bone_mats[model.bone_indexes[vertex.deform.index0]] * vertex_relative_poses[0]
                    )
                    trans_normal_vecs[vertex.index] = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.index0]], vertex.normal
                    )
                elif type(vertex.deform) is Bdef2 and (
                    model.bone_indexes[vertex.deform.bone_index1] in trans_bone_mats
                    and model.bone_indexes[vertex.deform.bone_index2] in trans_bone_mats
                ):
                    v0_vec = trans_bone_mats[model.bone_indexes[vertex.deform.bone_index1]] * vertex_relative_poses[0]
                    v1_vec = trans_bone_mats[model.bone_indexes[vertex.deform.bone_index2]] * vertex_relative_poses[1]
                    trans_vertex_vecs[vertex.index] = (v0_vec * vertex.deform.bone_weight) + (
                        v1_vec * (1 - vertex.deform.bone_weight)
                    )

                    v0_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.bone_index1]], vertex.normal
                    )
                    v1_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.bone_index2]], vertex.normal
                    )
                    trans_normal_vecs[vertex.index] = (v0_normal * vertex.deform.bone_weight) + (
                        v1_normal * (1 - vertex.deform.bone_weight)
                    )
                elif type(vertex.deform) is Bdef4 and (
                    model.bone_indexes[vertex.deform.index0] in trans_bone_mats
                    and model.bone_indexes[vertex.deform.index1] in trans_bone_mats
                    and model.bone_indexes[vertex.deform.index2] in trans_bone_mats
                    and model.bone_indexes[vertex.deform.index3] in trans_bone_mats
                ):
                    v0_vec = trans_bone_mats[model.bone_indexes[vertex.deform.index0]] * vertex_relative_poses[0]
                    v1_vec = trans_bone_mats[model.bone_indexes[vertex.deform.index1]] * vertex_relative_poses[1]
                    v2_vec = trans_bone_mats[model.bone_indexes[vertex.deform.index2]] * vertex_relative_poses[2]
                    v3_vec = trans_bone_mats[model.bone_indexes[vertex.deform.index3]] * vertex_relative_poses[3]
                    trans_vertex_vecs[vertex.index] = (
                        v0_vec * vertex.deform.weight0
                        + v1_vec * vertex.deform.weight1
                        + v2_vec * vertex.deform.weight2
                        + v3_vec * vertex.deform.weight3
                    )

                    v0_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.index0]], vertex.normal
                    )
                    v1_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.index1]], vertex.normal
                    )
                    v2_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.index2]], vertex.normal
                    )
                    v3_normal = PoseService._calc_normal(
                        trans_bone_mats[model.bone_indexes[vertex.deform.index3]], vertex.normal
                    )
                    trans_normal_vecs[vertex.index] = (
                        v0_normal * vertex.deform.weight0
                        + v1_normal * vertex.deform.weight1
                        + v2_normal * vertex.deform.weight2
                        + v3_normal * vertex.deform.weight3
                    )

                if vertex.index in trans_vertex_vecs:
                    vertex.position = trans_vertex_vecs[vertex.index]
                    vertex.normal = trans_normal_vecs[vertex.index].normalized()

    @staticmethod
    def _calc_normal(mat: MMatrix4x4, normal: MVector3D) -> MVector3D:
        """计算法线

        Args:
            mat (MMatrix4x4): 变换矩阵
            normal (MVector3D): 法线向量

        Returns:
            MVector3D: 变换后的法线向量
        """
        return (mat * (normal + mat.toTranslation()) - mat.toTranslation()).normalized() 