import numpy as np
from typing import Dict, List, Any
from mmd.PmxData import PmxModel, Morph, MaterialMorphData
from module.MMath import MVector3D, MVector4D
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)

class MaterialMorphProcessor:
    @staticmethod
    def process_material_morphs(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        morph_pair: Dict[str, Any]
    ) -> Dict[str, Morph]:
        """处理材质变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        if "material" in morph_pair:
            # 处理材质变形
            target_morphs = MaterialMorphProcessor._process_texture_material(model, target_morphs, morph_name, morph_pair)
        elif "edge" in morph_pair:
            # 处理边缘变形
            target_morphs = MaterialMorphProcessor._process_edge_material(model, target_morphs, morph_name, morph_pair)

        return target_morphs

    @staticmethod
    def _process_texture_material(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        morph_pair: Dict[str, Any]
    ) -> Dict[str, Morph]:
        """处理纹理材质变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        morph = None
        for material_index, material in enumerate(model.materials.values()):
            if morph_pair["material"] in model.textures[material.texture_index]:
                # 如果材质名包含在纹理中，则为目标材质
                if not morph:
                    morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 8)
                    morph.index = len(target_morphs)

                morph.offsets.append(
                    MaterialMorphData(
                        material_index,
                        1,
                        MVector4D(0, 0, 0, 1),
                        MVector3D(),
                        0,
                        MVector3D(),
                        MVector4D(),
                        0,
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                    )
                )

            elif "hides" in morph_pair and np.count_nonzero(
                [material.name.endswith(hide_morph) for hide_morph in morph_pair["hides"]]
            ):
                # 如果材质名以需要隐藏的后缀结尾，则为目标材质
                if not morph:
                    morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 8)
                    morph.index = len(target_morphs)

                morph.offsets.append(
                    MaterialMorphData(
                        material_index,
                        0,
                        MVector4D(0, 0, 0, 0),
                        MVector3D(),
                        0,
                        MVector3D(),
                        MVector4D(),
                        0,
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                    )
                )

        if morph:
            target_morphs[morph_name] = morph

        return target_morphs

    @staticmethod
    def _process_edge_material(
        model: PmxModel,
        target_morphs: Dict[str, Morph],
        morph_name: str,
        morph_pair: Dict[str, Any]
    ) -> Dict[str, Morph]:
        """处理边缘材质变形

        Args:
            model (PmxModel): PMX模型
            target_morphs (Dict[str, Morph]): 目标变形映射
            morph_name (str): 变形名称
            morph_pair (Dict[str, Any]): 变形对

        Returns:
            Dict[str, Morph]: 处理后的变形映射
        """
        morph = None
        for material_index, material in enumerate(model.materials.values()):
            if (material.flag & 0x10) != 0:
                # 如果材质启用了边缘，则添加关闭边缘的变形
                if not morph:
                    morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 8)
                    morph.index = len(target_morphs)

                morph.offsets.append(
                    MaterialMorphData(
                        material_index,
                        0,
                        MVector4D(1, 1, 1, 1),
                        MVector3D(1, 1, 1),
                        1,
                        MVector3D(1, 1, 1),
                        # 边缘大小和透明度设为0
                        MVector4D(1, 1, 1, 0),
                        0,
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                    )
                )
            elif material.name.endswith("_エッジ"):
                # 如果是边缘材质，则完全关闭
                if not morph:
                    morph = Morph(morph_pair["name"], morph_name, morph_pair["panel"], 8)
                    morph.index = len(target_morphs)

                morph.offsets.append(
                    MaterialMorphData(
                        material_index,
                        0,
                        MVector4D(0, 0, 0, 0),
                        MVector3D(),
                        0,
                        MVector3D(),
                        MVector4D(),
                        0,
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                        MVector4D(1, 1, 1, 1),
                    )
                )

        if morph:
            target_morphs[morph_name] = morph

        return target_morphs 