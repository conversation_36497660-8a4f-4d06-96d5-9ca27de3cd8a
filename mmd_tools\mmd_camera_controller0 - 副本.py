# import bge
# import mathutils
# import math

# class MMDCameraController(bge.types.KX_PythonComponent):
#     """
#     MMD摄像机控制器组件
#     用于在UPBGE游戏中控制MMD父子摄像机运动
#     每update一次更新一帧动画数据
#     """
    
#     # 组件参数
#     args = {
#         "animation_speed": 1.0,      # 动画播放速度
#         "loop_animation": True,      # 是否循环播放
#         "auto_start": True,          # 是否自动开始播放
#         "camera_name": "Camera",     # 摄像机对象名称
#         "parent_name": "MMD_Camera", # 父对象名称
#     }
    
#     def start(self, args):
#         """初始化组件"""
#         print(f"[MMDCameraController] 开始初始化组件...")
        
#         self.animation_speed = args.get("animation_speed", 1.0)
#         self.loop_animation = args.get("loop_animation", True)
#         self.auto_start = args.get("auto_start", True)
#         self.camera_name = args.get("camera_name", "Camera")
#         self.parent_name = args.get("parent_name", "MMD_Camera")
        
#         print(f"[MMDCameraController] 参数设置完成:")
#         print(f"  - 动画速度: {self.animation_speed}")
#         print(f"  - 循环播放: {self.loop_animation}")
#         print(f"  - 自动开始: {self.auto_start}")
#         print(f"  - 摄像机名称: {self.camera_name}")
#         print(f"  - 父对象名称: {self.parent_name}")
        
#         # 动画状态
#         self.current_frame = 0.0
#         self.is_playing = self.auto_start
#         self.animation_data = []
#         self.total_frames = 0
        
#         # 摄像机对象（延迟获取）
#         self.camera_obj = None
#         self.parent_obj = None
#         self.objects_initialized = False
#         self.retry_count = 0
#         self.max_retry_count = 30  # 最多重试30次（约1秒）
        
#         print(f"[MMDCameraController] 摄像机对象将延迟获取...")
        
#         # 生成模拟动画数据
#         self._generate_sample_animation()
        
#         print(f"[MMDCameraController] 组件初始化完成，总帧数: {self.total_frames}")
    
#     def _try_get_camera_objects(self):
#         """尝试获取摄像机对象"""
#         if self.objects_initialized:
#             return True
            
#         self.retry_count += 1
#         print(f"[MMDCameraController] 第{self.retry_count}次尝试获取摄像机对象...")
        
#         scene = bge.logic.getCurrentScene()
        
#         # 打印当前游戏场景中的所有对象
#         print(f"[MMDCameraController] 当前游戏场景中的对象 (共{len(scene.objects)}个):")
#         for i, obj in enumerate(scene.objects):
#             print(f"  {i+1}. {obj.name}")
        
#         # 尝试获取摄像机对象
#         if not self.camera_obj:
#             # 策略1: 直接通过名称获取
#             self.camera_obj = scene.objects.get(self.camera_name)
#             if self.camera_obj:
#                 print(f"[MMDCameraController] ✅ 成功获取摄像机对象 (直接): {self.camera_name}")
#             else:
#                 print(f"[MMDCameraController] ❌ 直接获取失败: {self.camera_name}")
                
#                 # 策略2: 模糊匹配摄像机名称
#                 for obj in scene.objects:
#                     if "camera" in obj.name.lower() or obj.name == "Camera":
#                         print(f"[MMDCameraController] 🔍 发现可能的摄像机对象: {obj.name}")
#                         # 这里可以选择是否使用找到的摄像机
#                         # self.camera_obj = obj
#                         # break
        
#         # 尝试获取父对象
#         if not self.parent_obj:
#             # 策略1: 直接通过名称获取
#             self.parent_obj = scene.objects.get(self.parent_name)
#             if self.parent_obj:
#                 print(f"[MMDCameraController] ✅ 成功获取父对象 (直接): {self.parent_name}")
#             else:
#                 print(f"[MMDCameraController] ❌ 直接获取失败: {self.parent_name}")
                
#                 # 策略2: 模糊匹配父对象名称
#                 for obj in scene.objects:
#                     if "mmd" in obj.name.lower() and "camera" in obj.name.lower():
#                         print(f"[MMDCameraController] 🔍 发现可能的MMD摄像机父对象: {obj.name}")
#                         # 这里可以选择是否使用找到的父对象
#                         # self.parent_obj = obj
#                         # break
        
#         # 策略3: 检查是否有摄像机创建器组件可以提供对象信息
#         if not self.camera_obj or not self.parent_obj:
#             print(f"[MMDCameraController] 🔍 尝试从摄像机创建器获取对象信息...")
#             self._try_get_from_creator()
        
#         # 检查是否都获取到了
#         if self.camera_obj and self.parent_obj:
#             self.objects_initialized = True
#             print(f"[MMDCameraController] ✅ 所有摄像机对象获取成功！")
#             print(f"  - 父对象: {self.parent_obj.name}")
#             print(f"  - 摄像机: {self.camera_obj.name}")
#             return True
#         elif self.retry_count >= self.max_retry_count:
#             print(f"[MMDCameraController] ⚠️ 警告: 达到最大重试次数({self.max_retry_count})，仍有对象未获取到")
#             if not self.camera_obj:
#                 print(f"[MMDCameraController] ❌ 缺失摄像机对象: {self.camera_name}")
#             if not self.parent_obj:
#                 print(f"[MMDCameraController] ❌ 缺失父对象: {self.parent_name}")
            
#             # 最后尝试：使用任何可用的摄像机
#             if not self.camera_obj:
#                 print(f"[MMDCameraController] 🔄 最后尝试：使用任何可用的摄像机...")
#                 for obj in scene.objects:
#                     if "camera" in obj.name.lower():
#                         self.camera_obj = obj
#                         print(f"[MMDCameraController] ⚠️ 使用备用摄像机: {obj.name}")
#                         break
            
#             return False
        
#         return False
    
#     def _try_get_from_creator(self):
#         """尝试从摄像机创建器获取对象信息"""
#         try:
#             # 查找场景中是否有摄像机创建器组件
#             scene = bge.logic.getCurrentScene()
#             for obj in scene.objects:
#                 # 检查对象是否有UPBGEMMDCameraCreator组件
#                 if hasattr(obj, 'components'):
#                     for component in obj.components:
#                         if hasattr(component, 'get_object_names'):
#                             print(f"[MMDCameraController] 🔍 发现摄像机创建器组件在: {obj.name}")
#                             creator_info = component.get_object_names()
#                             print(f"[MMDCameraController] 创建器信息: {creator_info}")
                            
#                             if creator_info.get('created'):
#                                 # 尝试使用创建器提供的名称
#                                 if not self.parent_obj:
#                                     self.parent_obj = scene.objects.get(creator_info['parent_name'])
#                                     if self.parent_obj:
#                                         print(f"[MMDCameraController] ✅ 从创建器获取父对象: {self.parent_obj.name}")
                                
#                                 if not self.camera_obj:
#                                     self.camera_obj = scene.objects.get(creator_info['camera_name'])
#                                     if self.camera_obj:
#                                         print(f"[MMDCameraController] ✅ 从创建器获取摄像机: {self.camera_obj.name}")
                            
#                             return
#         except Exception as e:
#             print(f"[MMDCameraController] ⚠️ 从创建器获取对象信息失败: {e}")
    
#     def _generate_sample_animation(self):
#         """生成示例动画数据"""
#         print(f"[MMDCameraController] 开始生成示例动画数据...")
        
#         # 模拟一个360度环绕的摄像机动画，持续10秒（300帧 @ 30fps）
#         self.total_frames = 300
#         self.animation_data = []
        
#         for frame in range(self.total_frames):
#             # 计算时间进度 (0.0 到 1.0)
#             progress = frame / self.total_frames
            
#             # 环绕运动参数
#             radius = 15.0  # 环绕半径
#             height = 5.0 + math.sin(progress * math.pi * 4) * 2.0  # 上下波动
#             angle = progress * math.pi * 2  # 完整的360度旋转
            
#             # 计算位置 (环绕目标点)
#             target_pos = mathutils.Vector((0, 0, 3))  # 目标注视点
#             camera_pos = mathutils.Vector((
#                 target_pos.x + math.cos(angle) * radius,
#                 target_pos.y + math.sin(angle) * radius,
#                 target_pos.z + height
#             ))
            
#             # 计算旋转 (始终看向目标点)
#             direction = (target_pos - camera_pos).normalized()
            
#             # 计算欧拉角
#             # Yaw (绕Z轴旋转)
#             yaw = math.atan2(direction.y, direction.x)
#             # Pitch (绕X轴旋转)
#             pitch = math.asin(-direction.z)
#             # Roll保持为0
#             roll = 0.0
            
#             # 摄像机距离 (子对象的Y位置)
#             distance = -45.0 + math.sin(progress * math.pi * 2) * 10.0
            
#             # 视野角度变化
#             fov = math.radians(30 + math.sin(progress * math.pi * 6) * 15)
            
#             # 透视/正交切换 (可选)
#             is_perspective = True
            
#             frame_data = {
#                 'frame': frame,
#                 'location': camera_pos,
#                 'rotation': mathutils.Euler((pitch, roll, yaw), 'XYZ'),
#                 'distance': distance,
#                 'fov': fov,
#                 'is_perspective': is_perspective
#             }
            
#             self.animation_data.append(frame_data)
        
#         print(f"[MMDCameraController] 示例动画数据生成完成，共{self.total_frames}帧")
    
#     def update(self):
#         """每帧更新"""
#         # 如果摄像机对象还没有初始化，尝试获取
#         if not self.objects_initialized:
#             if not self._try_get_camera_objects():
#                 # 如果还没获取到，跳过本次更新
#                 return
        
#         if not self.is_playing or not self.animation_data:
#             return
            
#         print(f"[MMDCameraController] 更新帧: {self.current_frame:.2f}")
        
#         # 更新当前帧
#         self.current_frame += self.animation_speed
        
#         # 处理循环
#         if self.current_frame >= self.total_frames:
#             if self.loop_animation:
#                 self.current_frame = 0.0
#                 print(f"[MMDCameraController] 动画循环，重置到第0帧")
#             else:
#                 self.is_playing = False
#                 print(f"[MMDCameraController] 动画播放完成，停止播放")
#                 return
        
#         print(f"[MMDCameraController] 获取当前帧数据，帧索引: {int(self.current_frame)}")
        
#         # 获取当前帧数据
#         frame_index = int(self.current_frame) % len(self.animation_data)
#         current_data = self.animation_data[frame_index]
        
#         # 可选：插值到下一帧实现平滑动画
#         if frame_index + 1 < len(self.animation_data):
#             next_data = self.animation_data[frame_index + 1]
#             blend_factor = self.current_frame - int(self.current_frame)
#             interpolated_data = self._interpolate_frame_data(current_data, next_data, blend_factor)
#             print(f"[MMDCameraController] 执行帧间插值，混合因子: {blend_factor:.3f}")
#         else:
#             interpolated_data = current_data
#             print(f"[MMDCameraController] 使用最后一帧数据")
        
#         # 应用动画数据到摄像机
#         self._apply_camera_data(interpolated_data)
    
#     def _interpolate_frame_data(self, data1, data2, factor):
#         """在两帧数据之间进行插值"""
#         result = {}
        
#         # 位置插值
#         result['location'] = data1['location'].lerp(data2['location'], factor)
        
#         # 旋转插值 (欧拉角)
#         rot1 = data1['rotation']
#         rot2 = data2['rotation']
#         # 简单的线性插值，实际应用中可能需要更复杂的插值
#         result['rotation'] = mathutils.Euler((
#             rot1.x + (rot2.x - rot1.x) * factor,
#             rot1.y + (rot2.y - rot1.y) * factor,
#             rot1.z + (rot2.z - rot1.z) * factor
#         ), 'XYZ')
        
#         # 距离插值
#         result['distance'] = data1['distance'] + (data2['distance'] - data1['distance']) * factor
        
#         # 视野角度插值
#         result['fov'] = data1['fov'] + (data2['fov'] - data1['fov']) * factor
        
#         # 透视模式 (不插值，使用第一帧的值)
#         result['is_perspective'] = data1['is_perspective']
        
#         return result
    
#     def _apply_camera_data(self, data):
#         """将动画数据应用到摄像机对象"""
#         if not self.parent_obj or not self.camera_obj:
#             print(f"[MMDCameraController] 警告: 摄像机对象未初始化完成，跳过应用数据")
#             return
            
#         print(f"[MMDCameraController] 应用摄像机数据:")
#         print(f"  - 位置: {data['location']}")
#         print(f"  - 旋转: {data['rotation']}")
#         print(f"  - 距离: {data['distance']:.2f}")
#         print(f"  - FOV: {math.degrees(data['fov']):.1f}度")
        
#         # 设置父对象位置和旋转
#         self.parent_obj.worldPosition = data['location']
#         self.parent_obj.worldOrientation = data['rotation']
        
#         # 设置子对象距离 (通过Y轴位置)
#         local_pos = self.camera_obj.localPosition
#         self.camera_obj.localPosition = [local_pos.x, data['distance'], local_pos.z]
        
#         # 如果摄像机有lens属性，设置视野
#         if hasattr(self.camera_obj, 'lens'):
#             # 将FOV转换为lens值 (简化公式)
#             # 假设sensor_height = 32mm
#             sensor_height = 32.0
#             lens = sensor_height / (2 * math.tan(data['fov'] / 2))
#             self.camera_obj.lens = lens
#             print(f"[MMDCameraController] 设置镜头焦距: {lens:.2f}mm")
#         else:
#             print(f"[MMDCameraController] 摄像机对象没有lens属性")

#     def play(self):
#         """开始播放动画"""
#         self.is_playing = True
#         print(f"[MMDCameraController] 开始播放动画")
    
#     def pause(self):
#         """暂停动画"""
#         self.is_playing = False
#         print(f"[MMDCameraController] 暂停动画")
    
#     def stop(self):
#         """停止动画并重置到开始"""
#         self.is_playing = False
#         self.current_frame = 0.0
#         print(f"[MMDCameraController] 停止动画并重置")
    
#     def set_frame(self, frame):
#         """设置到指定帧"""
#         self.current_frame = max(0, min(frame, self.total_frames - 1))
#         print(f"[MMDCameraController] 设置到第{self.current_frame}帧")
#         if self.animation_data and self.objects_initialized:
#             frame_index = int(self.current_frame)
#             self._apply_camera_data(self.animation_data[frame_index])
    
#     def set_speed(self, speed):
#         """设置播放速度"""
#         self.animation_speed = speed
#         print(f"[MMDCameraController] 设置播放速度: {speed}")
    
#     def load_animation_data(self, animation_data):
#         """加载自定义动画数据"""
#         self.animation_data = animation_data
#         self.total_frames = len(animation_data)
#         self.current_frame = 0.0
#         print(f"[MMDCameraController] 已加载动画数据，总帧数: {self.total_frames}")


# # VMD数据结构示例 (可用于加载真实的VMD动画数据)
# class VMDCameraFrame:
#     """VMD摄像机帧数据结构"""
#     def __init__(self, frame_number, location, rotation, distance, fov, is_perspective=True):
#         self.frame_number = frame_number
#         self.location = location        # mathutils.Vector (x, y, z)
#         self.rotation = rotation        # mathutils.Euler (rx, ry, rz)
#         self.distance = distance        # float
#         self.fov = fov                 # float (弧度)
#         self.is_perspective = is_perspective  # bool


# # 使用示例函数
# def create_sample_vmd_data():
#     """创建示例VMD摄像机数据"""
#     frames = []
#     for i in range(100):
#         frame_data = {
#             'frame': i,
#             'location': mathutils.Vector((math.sin(i * 0.1) * 10, math.cos(i * 0.1) * 10, 5)),
#             'rotation': mathutils.Euler((0, 0, i * 0.1), 'XYZ'),
#             'distance': -45.0,
#             'fov': math.radians(30),
#             'is_perspective': True
#         }
#         frames.append(frame_data)
#     return frames 