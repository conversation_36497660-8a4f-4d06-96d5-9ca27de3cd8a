# -*- coding: utf-8 -*-
#
import _pickle as cPickle
from abc import ABCMeta, abstractmethod
from collections import OrderedDict
import math
import numpy as np

from module.MParams import BoneLinks # noqa
from module.MMath import MRect, MVector2D, MVector3D, MVector4D, MQuaternion, MMatrix4x4 # noqa

from utils.MException import SizingException # noqa
from utils.MLogger import MLogger # noqa

logger = MLogger(__name__, level=MLogger.DEBUG_INFO)

# Deform
cdef class Deform:
    def __init__(self):
        self.index0 = 0

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Deform index0:{0}>".format(self.index0)

# BDEF1
cdef class Bdef1:
    def __init__(self, bone_index):
        self.bone_index = bone_index

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Bdef1 bone_index:{0}>".format(self.bone_index)

# BDEF2
cdef class Bdef2:
    def __init__(self, bone_index1, bone_index2, bone_weight):
        self.bone_index1 = bone_index1
        self.bone_index2 = bone_index2
        self.bone_weight = bone_weight

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Bdef2 bone_index1:{0}, bone_index2:{1}, bone_weight:{2}>".format(
            self.bone_index1, self.bone_index2, self.bone_weight)

# BDEF4
cdef class Bdef4:
    def __init__(self, bone_index1, bone_index2, bone_index3, bone_index4, bone_weight1, bone_weight2, bone_weight3, bone_weight4):
        self.bone_index1 = bone_index1
        self.bone_index2 = bone_index2
        self.bone_index3 = bone_index3
        self.bone_index4 = bone_index4
        self.bone_weight1 = bone_weight1
        self.bone_weight2 = bone_weight2
        self.bone_weight3 = bone_weight3
        self.bone_weight4 = bone_weight4

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Bdef4 bone_index1:{0}, bone_index2:{1}, bone_index3:{2}, bone_index4:{3}, bone_weight1:{4}, bone_weight2:{5}, bone_weight3:{6}, bone_weight4:{7}>".format(
            self.bone_index1, self.bone_index2, self.bone_index3, self.bone_index4,
            self.bone_weight1, self.bone_weight2, self.bone_weight3, self.bone_weight4)

# SDEF
cdef class Sdef:
    def __init__(self, bone_index1, bone_index2, bone_weight, sdef_c, sdef_r0, sdef_r1):
        self.bone_index1 = bone_index1
        self.bone_index2 = bone_index2
        self.bone_weight = bone_weight
        self.sdef_c = sdef_c
        self.sdef_r0 = sdef_r0
        self.sdef_r1 = sdef_r1

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Sdef bone_index1:{0}, bone_index2:{1}, bone_weight:{2}, sdef_c:{3}, sdef_r0:{4}, sdef_r1:{5}>".format(
            self.bone_index1, self.bone_index2, self.bone_weight,
            self.sdef_c, self.sdef_r0, self.sdef_r1)

# QDEF
cdef class Qdef:
    def __init__(self, bone_index1, bone_index2, bone_weight):
        self.bone_index1 = bone_index1
        self.bone_index2 = bone_index2
        self.bone_weight = bone_weight

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Qdef bone_index1:{0}, bone_index2:{1}, bone_weight:{2}>".format(
            self.bone_index1, self.bone_index2, self.bone_weight)

# ジョイント
cdef class Joint:
    cdef public str name
    cdef public str english_name
    cdef public int joint_type
    cdef public int rigidbody_a_index
    cdef public int rigidbody_b_index
    cdef public MVector3D position
    cdef public MVector3D rotation
    cdef public MVector3D translation_limit_min
    cdef public MVector3D translation_limit_max
    cdef public MVector3D rotation_limit_min
    cdef public MVector3D rotation_limit_max
    cdef public MVector3D spring_constant_translation
    cdef public MVector3D spring_constant_rotation
    cdef public int JOINT_SPRING6DOF

    def __init__(self):
        self.name = ""
        self.english_name = ""
        self.joint_type = 0
        self.rigidbody_a_index = -1
        self.rigidbody_b_index = -1
        self.position = MVector3D()
        self.rotation = MVector3D()
        self.translation_limit_min = MVector3D()
        self.translation_limit_max = MVector3D()
        self.rotation_limit_min = MVector3D()
        self.rotation_limit_max = MVector3D()
        self.spring_constant_translation = MVector3D()
        self.spring_constant_rotation = MVector3D()
        self.JOINT_SPRING6DOF = 0

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Joint name:{0}, english_name:{1}, joint_type:{2}, rigidbody_a_index:{3}, rigidbody_b_index:{4}>".format(
            self.name, self.english_name, self.joint_type, self.rigidbody_a_index, self.rigidbody_b_index)

# 表示枠
cdef class DisplaySlot:
    cdef public str name
    cdef public str english_name
    cdef public int special_flag
    cdef public list references
    cdef public int BONETYPE_NORMAL
    cdef public int BONETYPE_SPECIAL

    def __init__(self):
        self.name = ""
        self.english_name = ""
        self.special_flag = 0
        self.references = []
        self.BONETYPE_NORMAL = 0
        self.BONETYPE_SPECIAL = 1

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<DisplaySlot name:{0}, english_name:{1}, special_flag:{2}, references(len):{3}>".format(
            self.name, self.english_name, self.special_flag, len(self.references))

# 变形
cdef class Morph:
    def __init__(self):
        self.name = ""
        self.english_name = ""
        self.panel = 0
        self.morph_type = 0
        self.offsets = []
        self.material_name = ""
        self.edge_flag = False

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Morph name:{0}, english_name:{1}, panel:{2}, morph_type:{3}, offsets:{4}, material_name:{5}, edge_flag:{6}>".format(
            self.name, self.english_name, self.panel, self.morph_type, self.offsets, self.material_name, self.edge_flag)

# 材质变形数据
cdef class MaterialMorphData:
    def __init__(self):
        self.material_index = -1
        self.calc_mode = 0
        self.offset_type = 0
        self.diffuse = MVector4D()
        self.specular = MVector4D()
        self.specular_factor = 0.0
        self.ambient = MVector3D()
        self.edge_color = MVector4D()
        self.edge_size = 0.0
        self.texture_factor = MVector4D()
        self.sphere_texture_factor = MVector4D()
        self.toon_texture_factor = MVector4D()
        self.uv = MVector4D()

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<MaterialMorphData material_index:{0}, calc_mode:{1}, offset_type:{2}, diffuse:{3}, specular:{4}, specular_factor:{5}, ambient:{6}, edge_color:{7}, edge_size:{8}, texture_factor:{9}, sphere_texture_factor:{10}, toon_texture_factor:{11}, uv:{12}>".format(
            self.material_index, self.calc_mode, self.offset_type, self.diffuse, self.specular, self.specular_factor, self.ambient, self.edge_color, self.edge_size, self.texture_factor, self.sphere_texture_factor, self.toon_texture_factor, self.uv)

# UV变形数据
cdef class UVMorphData:
    def __init__(self):
        self.vertex_index = -1
        self.uv_offset = MVector4D()

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

# 骨骼变形数据
cdef class BoneMorphData:
    def __init__(self):
        self.bone_index = -1
        self.position = MVector3D()
        self.rotation = MQuaternion()

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

# 组变形数据
cdef class GroupMorphData:
    def __init__(self):
        self.morph_index = -1
        self.value = 0.0

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

# 顶点变形偏移
cdef class VertexMorphOffset:
    def __init__(self):
        self.vertex_index = -1
        self.position_offset = MVector3D()

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

# 材质
cdef class Material:
    def __init__(self):
        self.name = ""
        self.english_name = ""
        self.diffuse = MVector4D(1.0, 1.0, 1.0, 1.0)  # 设置默认值，包括alpha
        self.specular = MVector3D()
        self.specular_factor = 0.0
        self.ambient = MVector3D()
        self.flag = 0
        self.edge_color = MVector4D()
        self.edge_size = 1.0
        self.texture_index = -1
        self.sphere_texture_index = -1
        self.sphere_mode = 0
        self.toon_sharing_flag = 0
        self.toon_texture_index = 0
        self.comment = ""
        self.vertex_count = 0

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Material name:{0}, english_name:{1}, diffuse:{2}, specular:{3}, specular_factor:{4}, ambient:{5}>".format(
            self.name, self.english_name, self.diffuse, self.specular, self.specular_factor, self.ambient)

# OBB（有向境界ボックス：Oriented Bounding Box）
cdef class OBB:
    def __init__(self, fno, shape_size, shape_position, shape_rotation, bone_name, bone_pos, bone_matrix, is_aliginment, is_arm_left, is_arm_upper, is_small, is_init_rot):
        self.fno = fno
        self.shape_size = shape_size
        self.shape_position = shape_position
        self.shape_rotation = shape_rotation
        self.shape_rotation_qq = MQuaternion.fromEulerAngles(math.degrees(shape_rotation.x()), math.degrees(shape_rotation.y()), math.degrees(shape_rotation.z()))
        self.bone_pos = bone_pos
        self.h_sign = 1 if is_arm_left else -1
        self.v_sign = -1 if is_arm_upper and is_small else 1
        self.is_aliginment = is_aliginment
        self.is_arm_upper = is_arm_upper
        self.is_small = is_small
        self.is_arm_left = is_arm_left

        # 回転なし行列
        self.matrix = bone_matrix[bone_name].copy()
        # 回転あり行列
        self.rotated_matrix = bone_matrix[bone_name].copy()

        # 剛体自体の位置
        self.matrix.translate(self.shape_position - bone_pos)
        self.rotated_matrix.translate(self.shape_position - bone_pos)
        # 剛体自体の回転(回転用行列だけ保持)
        self.rotated_matrix.rotate(self.shape_rotation_qq)

        # 剛体自体の原点
        self.origin = self.matrix * MVector3D(0, 0, 0)

        self.origin_xyz = {"x": self.origin.x(), "y": self.origin.y(), "z": self.origin.z()}
        self.shape_size_xyz = {"x": self.shape_size.x(), "y": self.shape_size.y(), "z": self.shape_size.z()}

    # OBBとの衝突判定
    cpdef tuple get_collistion(self, MVector3D point, MVector3D root_global_pos, float max_length, float base_size):
        cdef MMatrix4x4 arm_matrix
        cdef bint collision, near_collision
        cdef double d, sin_x_theta, sin_y_theta, sin_z_plus_theta, x, x_theta, y, y_theta, z_plus, z_minus, z_plus_theta, x_distance, z_plus_distance, new_y, sin_z_minus_theta, z_minus_theta, z_minus_distance
        cdef MVector3D local_point, new_x_local, new_z_plus_local, rep_x_collision_vec, rep_z_plus_collision_vec, x_arm_local, z_plus_arm_local, new_z_minus_local, rep_z_minus_collision_vec, z_minus_arm_local

        cdef float min_ratio = base_size - 0.02
        cdef float max_ratio = base_size + 0.02

        # 原点との距離が半径未満なら衝突
        d = point.distanceToPoint(self.origin)
        collision = 0 < d < self.shape_size.x() * min_ratio
        near_collision = 0 <= d <= self.shape_size.x() * max_ratio

        x_distance = 0
        z_plus_distance = 0
        z_minus_distance = 0
        rep_x_collision_vec = MVector3D()
        rep_z_plus_collision_vec = MVector3D()
        rep_z_minus_collision_vec = MVector3D()

        if collision or near_collision:
            # 剛体のローカル座標系に基づく点の位置
            local_point = self.matrix.inverted() * point

            x = self.shape_size.x() * max_ratio * self.h_sign
            y = self.shape_size.x() * max_ratio * self.v_sign
            z_plus = self.shape_size.x() * max_ratio * 1
            z_minus = self.shape_size.x() * max_ratio * -1

            # 各軸方向の離れ具合
            x_theta = math.acos(max(-1, min(1, local_point.x() / x)))
            y_theta = math.acos(max(-1, min(1, local_point.y() / y)))
            z_plus_theta = math.acos(max(-1, min(1, local_point.z() / z_plus)))
            z_minus_theta = math.acos(max(-1, min(1, local_point.z() / z_minus)))
            # 離れ具合から見た円周の位置
            sin_y_theta = math.sin(y_theta) * max_ratio
            sin_x_theta = math.sin(x_theta) * max_ratio
            sin_z_plus_theta = math.sin(z_plus_theta) * max_ratio
            sin_z_minus_theta = math.sin(z_minus_theta) * max_ratio

            new_y = local_point.y()

            new_x_local = MVector3D(y_theta * x, new_y, local_point.z())
            new_z_plus_local = MVector3D(local_point.x(), new_y, y_theta * z_plus)
            new_z_minus_local = MVector3D(local_point.x(), new_y, y_theta * z_minus)

            x_distance = new_x_local.distanceToPoint(local_point)
            z_plus_distance = new_z_plus_local.distanceToPoint(local_point)
            z_minus_distance = new_z_minus_local.distanceToPoint(local_point)

            rep_x_collision_vec = self.matrix * new_x_local
            rep_z_plus_collision_vec = self.matrix * new_z_plus_local
            rep_z_minus_collision_vec = self.matrix * new_z_minus_local

            # 腕の位置を起点とする行列（移動量だけ見る）
            arm_matrix = MMatrix4x4()
            arm_matrix.setToIdentity()
            arm_matrix.translate(root_global_pos)

            # 腕から見た回避位置
            x_arm_local = arm_matrix.inverted() * rep_x_collision_vec
            z_plus_arm_local = arm_matrix.inverted() * rep_z_plus_collision_vec
            z_minus_arm_local = arm_matrix.inverted() * rep_z_minus_collision_vec

            if x_arm_local.length() >= max_length:
                # 最大可能距離より長い場合、縮める
                x_arm_local *= (max_length / x_arm_local.length()) * min_ratio
                rep_x_collision_vec = arm_matrix * x_arm_local
                new_x_local = self.matrix.inverted() * rep_x_collision_vec
                x_distance = new_x_local.distanceToPoint(local_point)

            if z_plus_arm_local.length() >= max_length:
                # 最大可能距離より長い場合、縮める
                z_plus_arm_local *= (max_length / z_plus_arm_local.length()) * min_ratio
                rep_z_plus_collision_vec = arm_matrix * z_plus_arm_local
                new_z_plus_local = self.matrix.inverted() * rep_z_plus_collision_vec
                z_plus_distance = new_z_plus_local.distanceToPoint(local_point)

            if z_minus_arm_local.length() >= max_length:
                # 最大可能距離より長い場合、縮める
                z_minus_arm_local *= (max_length / z_minus_arm_local.length()) * min_ratio
                rep_z_minus_collision_vec = arm_matrix * z_minus_arm_local
                new_z_minus_local = self.matrix.inverted() * rep_z_minus_collision_vec
                z_minus_distance = new_z_minus_local.distanceToPoint(local_point)

            logger.debug("f: %s, y: %s, yt: %s, sy: %s, xt: %s, sx: %s, zt: %s, sz: %s, xd: %s, zd: %s, l: %s, d: %s, xl: %s, zl: %s, xr: %s, zrp: %s, zrm: %s", \
                         self.fno, local_point.y() / y, y_theta, sin_y_theta, x_theta, sin_x_theta, z_plus_theta, sin_z_plus_theta, x_distance, z_plus_distance, local_point.to_log(), d, \
                         new_x_local.to_log(), new_z_plus_local.to_log(), rep_x_collision_vec, rep_z_plus_collision_vec, rep_z_minus_collision_vec)

        return (collision, near_collision, x_distance, z_plus_distance, z_minus_distance, rep_x_collision_vec, rep_z_plus_collision_vec, rep_z_minus_collision_vec)

# PMX模型
cdef class PmxModel:
    def __init__(self):
        self.path = ""
        self.json_data = {}
        self.buffers = []
        self.buffer_views = []
        self.accessors = []
        self.meshes = []
        self.extended_uv = 0
        self.name = ""
        self.english_name = ""
        self.comment = ""
        self.english_comment = ""
        self.vertices = {}
        self.vertex_dict = {}
        self.indices = {}
        self.textures = []
        self.materials = {}
        self.material_indices = {}
        self.material_vertices = {}
        self.bones = {}
        self.bone_indexes = {}
        self.morphs = {}
        self.morph_indexes = {}
        self.org_morphs = {}
        self.display_slots = {}
        self.rigidbodies = {}
        self.rigidbody_indexes = {}
        self.joints = {}
        self.digest = ""
        self.can_upper_sizing = False
        self.can_arm_sizing = False
        self.head_top_vertex = None
        self.left_sole_vertex = None
        self.right_sole_vertex = None
        self.left_toe_vertex = None
        self.right_toe_vertex = None
        self.left_ik_sole_vertex = None
        self.right_ik_sole_vertex = None
        self.finger_tail_vertex = None
        self.wrist_entity_vertex = {}
        self.elbow_entity_vertex = {}
        self.elbow_middle_entity_vertex = {}

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<PmxModel name:{0}, english_name:{1}, vertices(len):{2}, indices(len):{3}, textures(len):{4}, materials(len):{5}, bones(len):{6}, morphs(len):{7}, display_slots(len):{8}, rigidbodies(len):{9}, joints(len):{10}".format(
            self.name, self.english_name, len(self.vertices), len(self.indices), len(self.textures),
            len(self.materials), len(self.bones), len(self.morphs), len(self.display_slots), len(self.rigidbodies), len(self.joints))

    cpdef create_link_2_top_one(self, str bone_name, bint is_defined=False):
        # 创建从指定骨骼到顶部的链接
        if bone_name not in self.bone_indexes:
            return None

        bone_index = self.bone_indexes[bone_name]
        bone = self.bones[bone_index]
        if not bone:
            return None

        # 创建链接
        link = []
        current_bone = bone
        while current_bone and current_bone.parent_index >= 0:
            link.append(current_bone)
            current_bone = self.bones[current_bone.parent_index]

        # 如果找到了链接，返回第一个骨骼
        if link:
            return link[0]
        return None

cdef class Bone:
    def __init__(self):
        self.name = ""
        self.english_name = ""
        self.position = MVector3D()
        self.parent_index = -1
        self.layer = 0
        self.flag = 0
        self.tail_position = MVector3D()
        self.tail_index = 0
        self.effect_index = 0
        self.effect_factor = 0
        self.fixed_axis = MVector3D()
        self.local_x_vector = MVector3D()
        self.local_z_vector = MVector3D()
        self.external_key = 0
        self.ik = None
        self.index = -1
        self.display = True
        self.is_sizing = False
        self.matrix = MMatrix4x4()
        self.len_1d = 0.0

    def getConnectionFlag(self):
        """获取骨骼的连接标志（0x0001）"""
        return bool(self.flag & 0x0001)  # 第0位

    def getRotationFlag(self):
        """获取骨骼的旋转可用标志（0x0002）"""
        return bool(self.flag & 0x0002)  # 第1位

    def getTranslationFlag(self):
        """获取骨骼的移动可用标志（0x0004）"""
        return bool(self.flag & 0x0004)  # 第2位

    def getVisibleFlag(self):
        """获取骨骼的显示标志（0x0008）"""
        return bool(self.flag & 0x0008)  # 第3位

    def getOperationFlag(self):
        """获取骨骼的操作可用标志（0x0010）"""
        return bool(self.flag & 0x0010)  # 第4位

    def getIkFlag(self):
        """获取骨骼的IK标志（0x0020）"""
        return bool(self.flag & 0x0020)  # 第5位

    def getFixedAxisFlag(self):
        """获取骨骼的固定轴标志（0x0080）"""
        return bool(self.flag & 0x0080)  # 第7位

    def getLocalAxisFlag(self):
        """获取骨骼的本地轴标志（0x0040）"""
        return bool(self.flag & 0x0040)  # 第6位

    def getLocalCoordinateFlag(self):
        """获取骨骼的本地坐标标志（0x0040），与getLocalAxisFlag相同"""
        return bool(self.flag & 0x0040)  # 第6位

    def getExternalTranslationFlag(self):
        """获取骨骼的外部移动标志（0x0100）"""
        return bool(self.flag & 0x0100)  # 第8位

    def getExternalRotationFlag(self):
        """获取骨骼的外部旋转标志（0x0200）"""
        return bool(self.flag & 0x0200)  # 第9位

    def getExternalParentDeformFlag(self):
        """获取骨骼的外部父骨骼变形标志（0x0400）"""
        return bool(self.flag & 0x0400)  # 第10位

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Bone name:{0}, english_name:{1}, position:{2}, parent_index:{3}, layer:{4}, flag:{5}>".format(
            self.name, self.english_name, self.position, self.parent_index, self.layer, self.flag)

cdef class RigidBody:
    def __init__(self, name, english_name, bone_index, collision_group, no_collision_group, shape_type, shape_size, shape_position, shape_rotation, param, physics_calc_type):
        self.name = name
        self.english_name = english_name
        self.bone_index = bone_index
        self.collision_group = collision_group
        self.no_collision_group = no_collision_group
        self.shape_type = shape_type
        self.shape_size = shape_size
        self.shape_position = shape_position
        self.shape_rotation = shape_rotation
        # 确保param是RigidBodyParam对象
        if isinstance(param, (int, float)):
            self.param = RigidBodyParam(float(param), 0.5, 0.5, 0.5, 0.5)  # 使用默认值
        elif isinstance(param, dict):
            self.param = RigidBodyParam(
                float(param.get('mass', 1.0)),
                float(param.get('linear_damping', 0.5)),
                float(param.get('angular_damping', 0.5)),
                float(param.get('restitution', 0.5)),
                float(param.get('friction', 0.5))
            )
        elif not isinstance(param, RigidBodyParam):
            self.param = RigidBodyParam(1.0, 0.5, 0.5, 0.5, 0.5)  # 使用默认值
        else:
            self.param = param
        self.mode = physics_calc_type  # 保持mode名称以兼容PMX格式
        self.index = -1  # 初始化index属性

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<RigidBody name:{0}, english_name:{1}, bone_index:{2}, collision_group:{3}, shape_type:{4}, mode:{5}>".format(
            self.name, self.english_name, self.bone_index, self.collision_group, self.shape_type, self.mode)

cdef class RigidBodyParam:
    def __init__(self, mass, linear_damping, angular_damping, restitution, friction):
        self.mass = mass
        self.linear_damping = linear_damping
        self.angular_damping = angular_damping
        self.restitution = restitution
        self.friction = friction

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<RigidBodyParam mass:{0}, linear_damping:{1}, angular_damping:{2}, restitution:{3}, friction:{4}>".format(
            self.mass, self.linear_damping, self.angular_damping, self.restitution, self.friction)

# 顶点
cdef class Vertex:
    def __init__(self, position=None, normal=None, uv=None):
        self.index = -1
        self.position = position if position else MVector3D()
        self.normal = normal if normal else MVector3D()
        self.uv = uv if uv else MVector2D()
        self.extended_uvs = []
        self.deform = None
        self.edge_factor = 0.0

    def copy(self):
        return cPickle.loads(cPickle.dumps(self, -1))

    def __str__(self):
        return "<Vertex index:{0}, position:{1}, normal:{2}, uv:{3}, deform:{4}, edge_factor:{5}>".format(
            self.index, self.position, self.normal, self.uv, self.deform, self.edge_factor) 