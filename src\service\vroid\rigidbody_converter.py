"""
Rigidbody conversion functionality for VRoid to PMX
"""

from typing import Dict, List, Any
from module.MMath import MVector3D
from mmd.PmxData import PmxModel, RigidBody
from utils.MLogger import MLogger
from .constants import RIGIDBODY_PAIRS
from .vertex_service import VertexService
from .rigidbody_param_service import RigidBodyParamService

logger = MLogger(__name__, level=1)

class RigidBodyConverter:
    # 刚体参数对
    RIGIDBODY_PAIRS = {
        "頭": {
            "english": "Head",
            "bone": "頭",
            "shape": 0,
            "group": 1,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
        },
        "後頭部": {
            "english": "Back Head",
            "bone": "頭",
            "shape": 0,
            "group": 1,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
        },
        "左胸": {
            "english": "Left Bust",
            "bone": "左胸",
            "shape": 0,
            "group": 2,
            "no_collision_group": [0, 1, 12, 13, 14],
            "direction": "vertical",
        },
        "右胸": {
            "english": "Right Bust",
            "bone": "右胸",
            "shape": 0,
            "group": 2,
            "no_collision_group": [0, 1, 12, 13, 14],
            "direction": "vertical",
        },
        "上半身2": {
            "english": "Upper Body2",
            "bone": "上半身2",
            "shape": 2,
            "group": 0,
            "no_collision_group": [0, 9, 10, 11, 12, 13, 14],
            "direction": "vertical",
        },
        "上半身3": {
            "english": "Upper Body3",
            "bone": "上半身3",
            "shape": 2,
            "group": 0,
            "no_collision_group": [0, 9, 10, 11, 12, 13, 14],
            "direction": "vertical",
        },
        "左尻": {
            "english": "Left Hip",
            "bone": "左足",
            "shape": 0,
            "group": 3,
            "no_collision_group": [0, 4, 5, 6, 7, 8, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "右尻": {
            "english": "Right Hip",
            "bone": "右足",
            "shape": 0,
            "group": 3,
            "no_collision_group": [0, 4, 5, 6, 7, 8, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "左太もも": {
            "english": "Left Thigh",
            "bone": "左ひざ",
            "shape": 2,
            "group": 4,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "右太もも": {
            "english": "Right Thigh",
            "bone": "右ひざ",
            "shape": 2,
            "group": 4,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "左ひざ": {
            "english": "Left Knee",
            "bone": "左ひざ",
            "shape": 2,
            "group": 5,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
            "range": "lower",
        },
        "右ひざ": {
            "english": "Right Knee",
            "bone": "右ひざ",
            "shape": 2,
            "group": 5,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
            "range": "lower",
        },
        "左足首": {
            "english": "Left Ankle",
            "bone": "左足首",
            "shape": 2,
            "group": 6,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
        },
        "右足首": {
            "english": "Right Ankle",
            "bone": "右足首",
            "shape": 2,
            "group": 6,
            "no_collision_group": [0, 3, 12, 13, 14],
            "direction": "vertical",
        },
        "左腕": {
            "english": "Left Arm",
            "bone": "左腕",
            "shape": 2,
            "group": 7,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "右腕": {
            "english": "Right Arm",
            "bone": "右腕",
            "shape": 2,
            "group": 7,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
            "range": "upper",
        },
        "左ひじ": {
            "english": "Left Elbow",
            "bone": "左ひじ",
            "shape": 2,
            "group": 8,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
            "range": "lower",
        },
        "右ひじ": {
            "english": "Right Elbow",
            "bone": "右ひじ",
            "shape": 2,
            "group": 8,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
            "range": "lower",
        },
        "左手首": {
            "english": "Left Wrist",
            "bone": "左手首",
            "shape": 2,
            "group": 9,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
        },
        "右手首": {
            "english": "Right Wrist",
            "bone": "右手首",
            "shape": 2,
            "group": 9,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
        },
        "首": {
            "english": "Neck",
            "bone": "首",
            "shape": 2,
            "group": 10,
            "no_collision_group": [0, 12, 13, 14],
            "direction": "vertical",
        },
    }

    @staticmethod
    def create_body_rigidbody(model: PmxModel) -> PmxModel:
        """创建身体刚体

        Args:
            model (PmxModel): PMX模型

        Returns:
            PmxModel: 处理后的模型
        """
        # 获取材质对应的顶点
        skin_vidxs, cloth_vidxs = VertexService.get_material_vertices(model)

        # 处理骨骼对应的顶点
        bone_vertices, bone_weights = VertexService.process_bone_vertices(model, skin_vidxs, cloth_vidxs)

        # 处理刚体参数
        model = RigidBodyParamService.process_rigidbody_params(
            model, bone_vertices, bone_weights, RigidBodyConverter.RIGIDBODY_PAIRS
        )

        return model 