
Deleted file - I:\AIV\vroid2pmx-main\src\build\mmd\PmxReader.c        
Deleted file - I:\AIV\vroid2pmx-main\src\build\mmd\PmxWriter.c        
Deleted file - I:\AIV\vroid2pmx-main\src\build\mmd\VmdReader.c        
Deleted file - I:\AIV\vroid2pmx-main\src\build\utils\MLogger.c        
Deleted file - I:\AIV\vroid2pmx-main\src\build\mmd\PmxData.cpp        
Deleted file - I:\AIV\vroid2pmx-main\src\build\mmd\VmdData.cpp        
Deleted file - I:\AIV\vroid2pmx-main\src\build\module\MMath.cpp       
Deleted file - I:\AIV\vroid2pmx-main\src\build\module\MParams.cpp     
Deleted file - I:\AIV\vroid2pmx-main\src\build\utils\MBezierUtils.cpp 
Deleted file - I:\AIV\vroid2pmx-main\src\build\utils\MServiceUtils.cpp
Deleted file - I:\AIV\vroid2pmx-main\src\mmd\PmxData.cp312-win_amd64.pyd  
Deleted file - I:\AIV\vroid2pmx-main\src\mmd\PmxReader.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\mmd\PmxWriter.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\mmd\VmdData.cp312-win_amd64.pyd  
Deleted file - I:\AIV\vroid2pmx-main\src\mmd\VmdReader.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\module\MMath.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\module\MParams.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\utils\MBezierUtils.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\utils\MLogger.cp312-win_amd64.pyd
Deleted file - I:\AIV\vroid2pmx-main\src\utils\MServiceUtils.cp312-win_amd64.pyd
Could Not Find I:\AIV\vroid2pmx-main\src\*.so
The system cannot find the file specified.
The filename, directory name, or volume label syntax is incorrect.
Compiling module/MMath.pyx because it changed.
Compiling module/MParams.pyx because it changed.
Compiling utils/MBezierUtils.pyx because it changed.
Compiling utils/MServiceUtils.pyx because it changed.
Compiling mmd/VmdData.pyx because it changed.
Compiling mmd/PmxData.pyx because it changed.
Compiling utils/MLogger.py because it changed.
Compiling mmd/VmdReader.py because it changed.
Compiling mmd/PmxReader.py because it changed.
Compiling mmd/PmxWriter.py because it changed.
[ 1/10] Cythonizing mmd/PmxData.pyx
[ 2/10] Cythonizing mmd/PmxReader.py
[ 3/10] Cythonizing mmd/PmxWriter.py
[ 4/10] Cythonizing mmd/VmdData.pyx
warning: mmd\VmdData.pyx:41:30: Unknown type declaration 'double' in annotation, ignoring
warning: mmd\VmdData.pyx:89:26: Unknown type declaration 'double' in annotation, ignoring
warning: mmd\VmdData.pyx:438:66: Unknown type declaration 'bint' in annotation, ignoring
warning: mmd\VmdData.pyx:438:80: Unknown type declaration 'bint' in annotation, ignoring
warning: mmd\VmdData.pyx:489:73: Unknown type declaration 'bint' in annotation, ignoring
warning: mmd\VmdData.pyx:489:87: Unknown type declaration 'bint' in annotation, ignoring
warning: mmd\VmdData.pyx:575:78: Unknown type declaration 'bint' in annotation, ignoring
warning: mmd\VmdData.pyx:575:92: Unknown type declaration 'bint' in annotation, ignoring
[ 5/10] Cythonizing mmd/VmdReader.py
[ 6/10] Cythonizing module/MMath.pyx
[ 7/10] Cythonizing module/MParams.pyx
[ 8/10] Cythonizing utils/MBezierUtils.pyx
[ 9/10] Cythonizing utils/MLogger.py
[10/10] Cythonizing utils/MServiceUtils.pyx
running clean
'build\lib.win-amd64-cpython-312' does not exist -- can't clean it
'build\bdist.win-amd64' does not exist -- can't clean it
'build\scripts-3.12' does not exist -- can't clean it
running build_ext
building 'module.MMath' extension
creating build\temp.win-amd64-cpython-312
creating build\temp.win-amd64-cpython-312\Release
creating build\temp.win-amd64-cpython-312\Release\build
creating build\temp.win-amd64-cpython-312\Release\build\module
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\module/MMath.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\module/MMath.obj /EHsc
MMath.cpp
build\module/MMath.cpp(59424): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\module/MMath.cpp(59438): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\module/MMath.cpp(59452): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\module/MMath.cpp(100235): warning C4551: 缺少参数列表的函数调用
build\module/MMath.cpp(100242): warning C4551: 缺少参数列表的函数调用
build\module/MMath.cpp(101244): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\module/MMath.cpp(101407): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\module/MMath.cpp(102150): warning C4551: 缺少参数列表的函数调用
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_MMath build\temp.win-amd64-cpython-312\Release\build\module/MMath.obj /OUT:I:\AIV\vroid2pmx-main\src\module\MMath.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\module\MMath.cp312-win_amd64.lib  正在创建库 build\temp.win-amd64-cpython-312\Release\build\module\MMath.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\module\MMath.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'module.MParams' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\module/MParams.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\module/MParams.obj /EHsc
MParams.cpp
build\module/MParams.cpp(10693): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\module/MParams.cpp(10866): warning C4551: 缺少参数列表的函数调用
build\module/MParams.cpp(10867): warning C4551: 缺少参数列表的函数调用
build\module/MParams.cpp(10868): warning C4551: 缺少参数列表的函数调用
build\module/MParams.cpp(11572): warning C4551: 缺少参数列表的函数调用
build\module/MParams.cpp(13643): warning C4551: 缺少参数列表的函数调用
build\module/MParams.cpp(13650): warning C4551: 缺少参数列表的函数调用
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_MParams build\temp.win-amd64-cpython-312\Release\build\module/MParams.obj /OUT:I:\AIV\vroid2pmx-main\src\module\MParams.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\module\MParams.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\module\MParams.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\module\MParams.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'utils.MBezierUtils' extension
creating build\temp.win-amd64-cpython-312\Release\build\utils
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\utils/MBezierUtils.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\utils/MBezierUtils.obj /EHsc
MBezierUtils.cpp
build\utils/MBezierUtils.cpp(22763): warning C4551: 缺少参数列表的函数调用
build\utils/MBezierUtils.cpp(22770): warning C4551: 缺少参数列表的函数调用
build\utils/MBezierUtils.cpp(23039): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\utils/MBezierUtils.cpp(23168): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\utils/MBezierUtils.cpp(23264): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\utils/MBezierUtils.cpp(23461): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_MBezierUtils build\temp.win-amd64-cpython-312\Release\build\utils/MBezierUtils.obj /OUT:I:\AIV\vroid2pmx-main\src\utils\MBezierUtils.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\utils\MBezierUtils.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\utils\MBezierUtils.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\utils\MBezierUtils.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'utils.MServiceUtils' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\utils/MServiceUtils.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\utils/MServiceUtils.obj /EHsc
MServiceUtils.cpp
build\utils/MServiceUtils.cpp(20777): warning C4551: 缺少参数列表的函数调用
build\utils/MServiceUtils.cpp(20784): warning C4551: 缺少参数列表的函数调用
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_MServiceUtils build\temp.win-amd64-cpython-312\Release\build\utils/MServiceUtils.obj /OUT:I:\AIV\vroid2pmx-main\src\utils\MServiceUtils.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\utils\MServiceUtils.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\utils\MServiceUtils.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\utils\MServiceUtils.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'mmd.VmdData' extension
creating build\temp.win-amd64-cpython-312\Release\build\mmd
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\mmd/VmdData.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\mmd/VmdData.obj /EHsc
VmdData.cpp
build\mmd/VmdData.cpp(26202): warning C4244: “=”: 从“Py_ssize_t”转换到“int”，可能丢失数据
build\mmd/VmdData.cpp(26796): warning C4244: “=”: 从“Py_ssize_t”转换到“int”，可能丢失数据
build\mmd/VmdData.cpp(46239): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\mmd/VmdData.cpp(48019): warning C4305: “参数”: 从“double”到“float”截断
build\mmd/VmdData.cpp(48583): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\mmd/VmdData.cpp(67630): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(67637): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(68192): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/VmdData.cpp(68328): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/VmdData.cpp(69170): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/VmdData.cpp(69299): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/VmdData.cpp(70324): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(70325): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(70326): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(70724): warning C4551: 缺少参数列表的函数调用
build\mmd/VmdData.cpp(74596): warning C4551: 缺少参数列表的函数调用
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_VmdData build\temp.win-amd64-cpython-312\Release\build\mmd/VmdData.obj /OUT:I:\AIV\vroid2pmx-main\src\mmd\VmdData.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\mmd\VmdData.cp312-win_amd64.lib 
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\mmd\VmdData.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\mmd\VmdData.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'mmd.PmxData' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /EHsc /Tpbuild\mmd/PmxData.cpp /Fobuild\temp.win-amd64-cpython-312\Release\build\mmd/PmxData.obj /EHsc
PmxData.cpp
build\mmd/PmxData.cpp(34237): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\mmd/PmxData.cpp(34247): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
build\mmd/PmxData.cpp(92039): warning C4551: 缺少参数列表的函数调用
build\mmd/PmxData.cpp(92579): warning C4551: 缺少参数列表的函数调用
build\mmd/PmxData.cpp(92586): warning C4551: 缺少参数列表的函数调用
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_PmxData build\temp.win-amd64-cpython-312\Release\build\mmd/PmxData.obj /OUT:I:\AIV\vroid2pmx-main\src\mmd\PmxData.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\mmd\PmxData.cp312-win_amd64.lib 
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxData.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxData.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'utils.MLogger' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /Tcbuild\utils/MLogger.c /Fobuild\temp.win-amd64-cpython-312\Release\build\utils/MLogger.obj
MLogger.c
build\utils/MLogger.c(17100): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\utils/MLogger.c(17609): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_MLogger build\temp.win-amd64-cpython-312\Release\build\utils/MLogger.obj /OUT:I:\AIV\vroid2pmx-main\src\utils\MLogger.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\utils\MLogger.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\utils\MLogger.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\utils\MLogger.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'mmd.VmdReader' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /Tcbuild\mmd/VmdReader.c /Fobuild\temp.win-amd64-cpython-312\Release\build\mmd/VmdReader.obj
VmdReader.c
build\mmd/VmdReader.c(16853): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/VmdReader.c(17611): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_VmdReader build\temp.win-amd64-cpython-312\Release\build\mmd/VmdReader.obj /OUT:I:\AIV\vroid2pmx-main\src\mmd\VmdReader.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\mmd\VmdReader.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\mmd\VmdReader.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\mmd\VmdReader.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'mmd.PmxReader' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /Tcbuild\mmd/PmxReader.c /Fobuild\temp.win-amd64-cpython-312\Release\build\mmd/PmxReader.obj
PmxReader.c
build\mmd/PmxReader.c(41859): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxReader.c(42095): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxReader.c(42191): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxReader.c(43076): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxReader.c(43205): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_PmxReader build\temp.win-amd64-cpython-312\Release\build\mmd/PmxReader.obj /OUT:I:\AIV\vroid2pmx-main\src\mmd\PmxReader.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\mmd\PmxReader.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxReader.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxReader.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
building 'mmd.PmxWriter' extension
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\cl.exe" /c /nologo 
/O2 /W3 /GL /DNDEBUG /MD -I. -IC:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\include -IH:\Python312\include -IH:\Python312\Include "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" /Tcbuild\mmd/PmxWriter.c /Fobuild\temp.win-amd64-cpython-312\Release\build\mmd/PmxWriter.obj
PmxWriter.c
build\mmd/PmxWriter.c(18562): warning C4146: 一元负运算符应用于无符号类型，结果仍为无符号类型
build\mmd/PmxWriter.c(23144): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxWriter.c(23412): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
build\mmd/PmxWriter.c(23592): warning C4244: “=”: 从“Py_ssize_t”转换到“long”，可能丢失数据
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\bin\HostX86\x64\link.exe" /nologo /INCREMENTAL:NO /LTCG /DLL /MANIFEST:EMBED,ID=2 /MANIFESTUAC:NO /LIBPATH:H:\Python312\libs /LIBPATH:H:\Python312 /LIBPATH:H:\Python312\PCbuild\amd64 "/LIBPATH:C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.36.32532\lib\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64" "/LIBPATH:C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64" 
/EXPORT:PyInit_PmxWriter build\temp.win-amd64-cpython-312\Release\build\mmd/PmxWriter.obj /OUT:I:\AIV\vroid2pmx-main\src\mmd\PmxWriter.cp312-win_amd64.pyd /IMPLIB:build\temp.win-amd64-cpython-312\Release\build\mmd\PmxWriter.cp312-win_amd64.lib
  正在创建库 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxWriter.cp312-win_amd64.lib 和对象 build\temp.win-amd64-cpython-312\Release\build\mmd\PmxWriter.cp312-win_amd64.exp
正在生成代码
已完成代码的生成
running develop
H:\Python312\Lib\site-packages\setuptools\command\develop.py:41: EasyInstallDeprecationWarning: easy_install command is deprecated.
!!

        ********************************************************************************
        Please avoid running ``setup.py`` and ``easy_install``.
        Instead, use pypa/build, pypa/installer or other
        standards-based tools.

        See https://github.com/pypa/setuptools/issues/917 for details.
        ********************************************************************************

!!
  easy_install.initialize_options(self)
H:\Python312\Lib\site-packages\setuptools\_distutils\cmd.py:66: SetuptoolsDeprecationWarning: setup.py install is deprecated.
!!

        ********************************************************************************
        Please avoid running ``setup.py`` directly.
        Instead, use pypa/build, pypa/installer or other
        standards-based tools.

        See https://blog.ganssle.io/articles/2021/10/setup-py-deprecated.html for details.
        ********************************************************************************

!!
  self.initialize_options()
running egg_info
writing vroid2pmx.egg-info\PKG-INFO
writing dependency_links to vroid2pmx.egg-info\dependency_links.txt
writing top-level names to vroid2pmx.egg-info\top_level.txt
reading manifest file 'vroid2pmx.egg-info\SOURCES.txt'
writing manifest file 'vroid2pmx.egg-info\SOURCES.txt'
running build_ext
Creating h:\python312\lib\site-packages\vroid2pmx.egg-link (link to .)
Adding vroid2pmx 0.0.0 to easy-install.pth file

Installed i:\aiv\vroid2pmx-main\src
Starting automatic VRM to PMX conversion
Input VRM file: F:\BaiduNetdiskDownload\Kuronyam-weiyi.vrm
Output PMX file will be: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx
Loading VRM model...
-- VRoid骨構造をPMX用に変換
VRM model loaded successfully
Starting conversion process...

| ------------------------------------------------------------ |
|                                                          |
| Starting Vroid2Pmx conversion                            |
| ------------------------                                 |
| Version: 2.01.06（ログあり版）                                  |
| 　Source model: Kuronyam-weiyi.vrm                        |
| 　Output path: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx |
|                                                          |
| ------------------------------------------------------------ |


| ------------------------------------------------------------ |
|                                                          |
| Starting Vroid2Pmx conversion                            |
| ------------------------                                 |
| Version: 2.01.06（ログあり版）                                  |
| 　Source model: Kuronyam-weiyi.vrm                        |
| 　Output path: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx |
|                                                          |
| ------------------------------------------------------------ |


| ------------------------------------------------------------ |
|                                                          |
| Starting Vroid2Pmx conversion                            |
| ------------------------                                 |
| Version: 2.01.06（ログあり版）                                  |
| 　Source model: Kuronyam-weiyi.vrm                        |
| 　Output path: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx |
|                                                          |
| ------------------------------------------------------------ |
 [service.vroid.export_service]
Output file already exists, it will be overwritten: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx
Output file already exists, it will be overwritten: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx
Output file already exists, it will be overwritten: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx [service.vroid.export_service]

------------------------------

Starting VRM to PMX conversion

------------------------------


------------------------------

Starting VRM to PMX conversion

------------------------------


------------------------------

Starting VRM to PMX conversion

------------------------------
 [service.vroid.export_service]
Creating base model
Creating base model
Creating base model [service.vroid.export_service]
Converting bones
Converting bones
Converting bones [service.vroid.export_service]
-- VRoid骨構造をPMX用に変換
-- VRoid骨構造をPMX用に変換 [service.vroid.bone_converter]
Converting mesh
Converting mesh
Converting mesh [service.vroid.export_service]
-- メッシュを変換
-- メッシュを変換 [service.vroid.mesh_converter]
Reconverting bones
Reconverting bones
Reconverting bones [service.vroid.export_service]
-- ボーンの再変換
-- ボーンの再変換 [service.vroid.bone_converter]
Converting morphs
Converting morphs
Converting morphs [service.vroid.export_service]
-- モーフを作成
-- モーフを作成 [service.vroid.morph_converter]
Transferring stance
Transferring stance
Transferring stance [service.vroid.export_service]
-- 姿勢変更開始
-- 姿勢変更開始 [service.vroid.stance_service]
No bone links found for bone: 頭
No bone links found for bone: 頭 [service.vroid.stance_service]
Bone not found: 右親指先
Bone not found: 右親指先 [service.vroid.stance_service]
Bone not found: 右人指先
Bone not found: 右人指先 [service.vroid.stance_service]
Bone not found: 右中指先
Bone not found: 右中指先 [service.vroid.stance_service]
Bone not found: 右薬指先
Bone not found: 右薬指先 [service.vroid.stance_service]
Bone not found: 右小指先
Bone not found: 右小指先 [service.vroid.stance_service]
No bone links found for bone: 右胸先
No bone links found for bone: 右胸先 [service.vroid.stance_service]
No bone links found for bone: 右腕捩1
No bone links found for bone: 右腕捩1 [service.vroid.stance_service]
No bone links found for bone: 右腕捩2
No bone links found for bone: 右腕捩2 [service.vroid.stance_service]
No bone links found for bone: 右腕捩3
No bone links found for bone: 右腕捩3 [service.vroid.stance_service]
No bone links found for bone: 右手捩1
No bone links found for bone: 右手捩1 [service.vroid.stance_service]
No bone links found for bone: 右手捩2
No bone links found for bone: 右手捩2 [service.vroid.stance_service]
No bone links found for bone: 右手捩3
No bone links found for bone: 右手捩3 [service.vroid.stance_service]
Bone not found: 左親指先
Bone not found: 左親指先 [service.vroid.stance_service]
Bone not found: 左人指先
Bone not found: 左人指先 [service.vroid.stance_service]
Bone not found: 左中指先
Bone not found: 左中指先 [service.vroid.stance_service]
Bone not found: 左薬指先
Bone not found: 左薬指先 [service.vroid.stance_service]
Bone not found: 左小指先
Bone not found: 左小指先 [service.vroid.stance_service]
No bone links found for bone: 左胸先
No bone links found for bone: 左胸先 [service.vroid.stance_service]
No bone links found for bone: 左腕捩1
No bone links found for bone: 左腕捩1 [service.vroid.stance_service]
No bone links found for bone: 左腕捩2
No bone links found for bone: 左腕捩2 [service.vroid.stance_service]
No bone links found for bone: 左腕捩3
No bone links found for bone: 左腕捩3 [service.vroid.stance_service]
No bone links found for bone: 左手捩1
No bone links found for bone: 左手捩1 [service.vroid.stance_service]
No bone links found for bone: 左手捩2
No bone links found for bone: 左手捩2 [service.vroid.stance_service]
No bone links found for bone: 左手捩3
No bone links found for bone: 左手捩3 [service.vroid.stance_service]
-- 姿勢変更終了
-- 姿勢変更終了 [service.vroid.stance_service]
Creating rigid bodies
Creating rigid bodies
Creating rigid bodies [service.vroid.export_service]
-- 身体剛体準備開始
-- 身体剛体準備開始 [service.vroid.body_rigidbody_service]
-- 身体剛体準備終了
-- 身体剛体準備終了 [service.vroid.body_rigidbody_service]
-- 身体剛体設定開始
-- 身体剛体設定開始 [service.vroid.body_rigidbody_service]
No vertices found for bone 頭, using default values
No vertices found for bone 頭, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [頭]
-- -- Created rigidbody [頭] [service.vroid.body_rigidbody_service]
No vertices found for bone 頭, using default values
No vertices found for bone 頭, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [後頭部]
-- -- Created rigidbody [後頭部] [service.vroid.body_rigidbody_service]
No vertices found for bone 首, using default values
No vertices found for bone 首, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [首]
-- -- Created rigidbody [首] [service.vroid.body_rigidbody_service]
No vertices found for bone 上半身3, using default values
No vertices found for bone 上半身3, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [上半身3]
-- -- Created rigidbody [上半身3] [service.vroid.body_rigidbody_service]
No vertices found for bone 上半身2, using default values
No vertices found for bone 上半身2, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [上半身2]
-- -- Created rigidbody [上半身2] [service.vroid.body_rigidbody_service]
No vertices found for bone 上半身, using default values
No vertices found for bone 上半身, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [上半身]
-- -- Created rigidbody [上半身] [service.vroid.body_rigidbody_service]
No vertices found for bone 下半身, using default values
No vertices found for bone 下半身, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [下半身]
-- -- Created rigidbody [下半身] [service.vroid.body_rigidbody_service]
No vertices found for bone 左胸, using default values
No vertices found for bone 左胸, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [左胸]
-- -- Created rigidbody [左胸] [service.vroid.body_rigidbody_service]
No vertices found for bone 右胸, using default values
No vertices found for bone 右胸, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [右胸]
-- -- Created rigidbody [右胸] [service.vroid.body_rigidbody_service]
No vertices found for bone 下半身, using default values
No vertices found for bone 下半身, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [左尻]
-- -- Created rigidbody [左尻] [service.vroid.body_rigidbody_service]
No vertices found for bone 下半身, using default values
No vertices found for bone 下半身, using default values [service.vroid.body_rigidbody_service]
-- -- Created rigidbody [右尻]
-- -- Created rigidbody [右尻] [service.vroid.body_rigidbody_service]
-- Finished creating rigidbodies
-- Finished creating rigidbodies [service.vroid.body_rigidbody_service]
Validating model before export
Validating model before export
Validating model before export [service.vroid.export_service]
模型验证过程中发生错误: 'module.MMath.MVector3D' object is not iterable
模型验证过程中发生错误: 'module.MMath.MVector3D' object is not iterable [ValidationService]
Model validation found issues, but continuing with export
Model validation found issues, but continuing with export
Model validation found issues, but continuing with export [service.vroid.export_service]
Exporting PMX Tailor settings
Exporting PMX Tailor settings
Exporting PMX Tailor settings [service.vroid.export_service]
-- PmxTailor用設定ファイル出力準備1
-- PmxTailor用設定ファイル出力準備1 [service.vroid.pmx_tailor_service]
-- PmxTailor用設定ファイル出力準備2
-- PmxTailor用設定ファイル出力準備2 [service.vroid.pmx_tailor_service]
-- PmxTailor用設定ファイル出力終了
-- PmxTailor用設定ファイル出力終了 [service.vroid.pmx_tailor_service]

----------------

Writing PMX file

----------------


----------------

Writing PMX file

----------------


----------------

Writing PMX file

----------------
 [service.vroid.export_service]
Failed to write number #1:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #1:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #2:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #2:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #3:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #3:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #4:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #4:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #5:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #5:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #6:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #6:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #7:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #7:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #8:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #8:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #9:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #9:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Failed to write number #10:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module>
Failed to write number #10:---写入默认值-- required argument is not an integer (type: <b, value: 0.0, value_type: float) at I:\AIV\vroid2pmx-main\src\executor.py:115 in <module> [mmd.PmxWriter]
Too many write_number errors, suppressing further detailed logs. Total errors so far: 11
Too many write_number errors, suppressing further detailed logs. Total errors so far: 11 [mmd.PmxWriter]
-- 頂点データ出力終了(216750)
-- 頂点データ出力終了(216750) [mmd.PmxWriter]
-- 面データ出力終了(131824)
-- 面データ出力終了(131824) [mmd.PmxWriter]
-- テクスチャデータ出力終了(0)
-- テクスチャデータ出力終了(0) [mmd.PmxWriter]
-- 材質データ出力終了(19)
-- 材質データ出力終了(19) [mmd.PmxWriter]
-- ボーンデータ出力終了(52)
-- ボーンデータ出力終了(52) [mmd.PmxWriter]
-- モーフデータ出力終了(0)
-- モーフデータ出力終了(0) [mmd.PmxWriter]
-- 表示枠データ出力終了(0)
-- 表示枠データ出力終了(0) [mmd.PmxWriter]
-- 剛体データ出力終了(11)
-- 剛体データ出力終了(11) [mmd.PmxWriter]
-- ジョイントデータ出力終了(0)
-- ジョイントデータ出力終了(0) [mmd.PmxWriter]

| ----------------------------------------- |
|                                       |
| Conversion completed successfully: Kuronyam-weiyi.pmx |
|                                       |
| ----------------------------------------- |


| ----------------------------------------- |
|                                       |
| Conversion completed successfully: Kuronyam-weiyi.pmx |
|                                       |
| ----------------------------------------- |


| ----------------------------------------- |
|                                       |
| Conversion completed successfully: Kuronyam-weiyi.pmx |
|                                       |
| ----------------------------------------- |
 [service.vroid.export_service]
Conversion completed successfully
Conversion completed successfully [__main__]
Output file: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx
Output file: F:\BaiduNetdiskDownload\Kuronyam-weiyi.pmx [__main__]

I:\AIV\vroid2pmx-main>