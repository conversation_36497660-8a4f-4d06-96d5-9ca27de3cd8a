# -*- coding: utf-8 -*-
#
import os
import json
import logging
from typing import Dict, List, Tuple, Any
from mmd.PmxData import PmxModel
from utils.MLogger import MLogger
from utils import MFileUtils

logger = MLogger(__name__, level=1)

class PmxTailorService:
    def __init__(self, model: PmxModel):
        self.model = model
        self.bone_materials = {}
        self.material_bones = {}
        self.settings = {}

    def export_settings(self, setting_dir_path: str) -> None:
        """导出PMX Tailor设置到文件

        Args:
            setting_dir_path (str): 设置文件输出目录
        """
        try:
            # 准备骨骼和材质的映射关系
            self.prepare_bone_material_mapping()

            # 获取节点和骨骼名称的映射
            node_bone_names = self.get_node_bone_names()

            # 创建PMX Tailor设置
            self.settings = self.create_pmx_tailor_settings(node_bone_names)

            # 导出设置文件
            self._export_settings(self.settings, setting_dir_path)

            logger.info("-- PmxTailor用設定ファイル出力終了")

        except Exception as e:
            logger.error(f"导出PMX Tailor设置时发生错误: {str(e)}")

    def _export_settings(self, pmx_tailor_settings: Dict[str, Any], setting_dir_path: str) -> None:
        """导出设置到文件

        Args:
            pmx_tailor_settings (Dict[str, Any]): PMX Tailor设置
            setting_dir_path (str): 设置文件输出目录
        """
        # 确保输出目录存在
        os.makedirs(setting_dir_path, exist_ok=True)

        # 导出每个设置
        for setting_name, pmx_tailor_setting in pmx_tailor_settings.items():
            if pmx_tailor_setting["material_name"] and pmx_tailor_setting["target_bones"]:
                # 创建设置文件路径
                setting_file_path = os.path.join(
                    setting_dir_path,
                    f"{pmx_tailor_setting['abb_name']}.json"
                )

                # 写入设置文件
                with open(setting_file_path, "w", encoding="utf-8") as jf:
                    json.dump(
                        pmx_tailor_setting,
                        jf,
                        ensure_ascii=False,
                        indent=4,
                        separators=(",", ": ")
                    )
            else:
                logger.warning(
                    "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s",
                    setting_name,
                    pmx_tailor_setting["material_name"],
                    pmx_tailor_setting["target_bones"]
                )

    def prepare_bone_material_mapping(self):
        """材質・ボーン・頂点INDEXの対応表を作成"""
        logger.info("-- PmxTailor用設定ファイル出力準備1")
        
        # 初始化映射字典
        self.bone_materials = {}
        self.material_bones = {}
        
        # 遍历所有顶点，建立骨骼和材质的映射关系
        for vidx, vertex in enumerate(self.model.vertex_dict):
            if not vertex or not hasattr(vertex, 'deform'):
                continue
                
            # 获取权重大于0.3的骨骼索引
            weighted_bones = vertex.deform.get_idx_list(0.3)
            if not weighted_bones:
                continue
                
            # 遍历每个材质
            for material_name, material_vidxs in self.model.material_vertices.items():
                if vidx not in material_vidxs:
                    continue
                    
                # 遍历每个权重大于0.3的骨骼
                for bone_idx in weighted_bones:
                    bone_name = self.model.bone_indexes.get(bone_idx)
                    if not bone_name:
                        continue
                        
                    # 更新bone_materials映射
                    if bone_name not in self.bone_materials:
                        self.bone_materials[bone_name] = []
                    if material_name not in [m[1] for m in self.bone_materials[bone_name]]:
                        # 计算该骨骼影响该材质的顶点数量
                        vertex_count = sum(1 for v in material_vidxs if bone_idx in self.model.vertex_dict[v].deform.get_idx_list(0.3))
                        self.bone_materials[bone_name].append((vertex_count, material_name))
                    
                    # 更新material_bones映射
                    if material_name not in self.material_bones:
                        self.material_bones[material_name] = []
                    if bone_idx not in self.material_bones[material_name]:
                        self.material_bones[material_name].append(bone_idx)
            
            if bone_name:
                logger.info("-- -- PmxTailor用設定ファイル出力準備1 (%s)", bone_name)

    def get_node_bone_names(self) -> Dict[int, str]:
        """ノードとボーン名の対応表を作成"""
        node_bone_names = {}
        for nidx, node in enumerate(self.model.json_data["nodes"]):
            for bone in self.model.bones.values():
                if bone.english_name == node["name"]:
                    node_bone_names[nidx] = bone.name
                    break
        return node_bone_names

    def create_pmx_tailor_settings(self, node_bone_names: Dict[int, str]) -> Dict[str, Any]:
        """PMX Tailorの設定を作成"""
        logger.info("-- PmxTailor用設定ファイル出力準備2")
        
        bone_cnt = {"Bust": 1, "CatEar": 1, "RabbitEar": 1, "Sleeve": 1, "HoodString": 1, "Hood": 1}
        pmx_tailor_settings = {}

        if (
            "extensions" not in self.model.json_data
            or "VRM" not in self.model.json_data["extensions"]
            or "secondaryAnimation" not in self.model.json_data["extensions"]["VRM"]
            or "boneGroups" not in self.model.json_data["extensions"]["VRM"]["secondaryAnimation"]
        ):
            return pmx_tailor_settings

        for bone_group in self.model.json_data["extensions"]["VRM"]["secondaryAnimation"]["boneGroups"]:
            # 检查comment字段是否存在
            group_type = bone_group.get("comment", "")
            if not group_type or group_type not in ("Bust", "CatEar", "RabbitEar", "Sleeve", "HoodString", "Hood"):
                continue

            # 检查bones字段是否存在
            if "bones" not in bone_group:
                continue

            for bone_bidx in bone_group["bones"]:
                # 检查bone_bidx是否在node_bone_names中
                if bone_bidx not in node_bone_names:
                    continue

                bone_name = node_bone_names[bone_bidx]
                if bone_name not in self.model.bones:
                    continue

                bone = self.model.bones[bone_name]
                settings = self._create_bone_group_settings(group_type, bone, bone_cnt)
                
                if settings:
                    pmx_tailor_settings[bone.name] = settings
                    bone_cnt[group_type] += 1
                    logger.info("-- -- PmxTailor用設定ファイル出力準備2 (%s)", settings["abb_name"])

        return pmx_tailor_settings

    def _create_bone_group_settings(self, group_type: str, bone: Any, bone_cnt: Dict[str, int]) -> Dict[str, Any]:
        """各ボーングループの設定を作成"""
        settings = {}
        
        if group_type == "Bust":
            settings = self._create_bust_settings(bone)
        elif group_type == "CatEar":
            settings = self._create_cat_ear_settings(bone)
        elif group_type == "RabbitEar":
            settings = self._create_rabbit_ear_settings(bone)
        elif group_type == "Sleeve":
            settings = self._create_sleeve_settings(bone, bone_cnt)
        elif group_type == "HoodString":
            settings = self._create_hood_string_settings(bone, bone_cnt)
        elif group_type == "Hood":
            settings = self._create_hood_settings(bone, bone_cnt)

        if settings:
            weighted_material_name = self._get_weighted_material_name(bone, settings.get("target_names", []))
            back_material_names = self._get_back_material_names(weighted_material_name)
            
            settings.update({
                "material_name": weighted_material_name,
                "back_extend_material_names": back_material_names,
            })

        return settings

    def _get_weighted_material_name(self, bone: Any, target_names: List[str]) -> str:
        """対象の材質名を取得"""
        weighted_material_name = None
        for target_name in target_names:
            for _, material_name in reversed(sorted(self.bone_materials.get(bone.name, []))):
                if target_name in material_name:
                    weighted_material_name = self.model.materials[material_name].name
                    break
            if weighted_material_name:
                break
        return weighted_material_name

    def _get_back_material_names(self, material_name: str) -> List[str]:
        """裏面材質名リストを取得"""
        back_material_names = []
        if not material_name:
            return back_material_names
            
        if f"{material_name}_エッジ" in self.model.materials:
            back_material_names.append(f"{material_name}_エッジ")
        if f"{material_name}_裏" in self.model.materials:
            back_material_names.append(f"{material_name}_裏")
        return back_material_names

    def _create_bust_settings(self, bone: Any) -> Dict[str, Any]:
        """胸の設定を作成"""
        primitive_name = (
            logger.transtext("胸(小)")
            if bone.position.distanceToPoint(self.model.bones[self.model.bone_indexes[bone.tail_index]].position) < 0.7
            else logger.transtext("胸(大)")
        )

        return {
            "target_names": ["CLOTH", "SKIN"],
            "abb_name": bone.name,
            "parent_bone_name": "上半身3",
            "group": "1",
            "direction": logger.transtext("下"),
            "primitive": primitive_name,
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [[bone.name, f"{bone.name}先"]],
            "rigidbody_root_thick": 0.5,
            "rigidbody_end_thick": 0.5,
        }

    def _create_cat_ear_settings(self, bone: Any) -> Dict[str, Any]:
        """猫耳の設定を作成"""
        return {
            "target_names": ["CatEar"],
            "abb_name": f"左猫耳" if "_L_" in bone.name else f"右猫耳",
            "parent_bone_name": "頭",
            "group": "1",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("髪(ショート)"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [self._get_cat_ear_bones(bone)],
            "rigidbody_root_thick": 0.5,
            "rigidbody_end_thick": 0.5,
        }

    def _create_rabbit_ear_settings(self, bone: Any) -> Dict[str, Any]:
        """兎耳の設定を作成"""
        return {
            "target_names": ["RabbitEar"],
            "abb_name": f"左兎耳" if "_L_" in bone.name else f"右兎耳",
            "parent_bone_name": "頭",
            "group": "1",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("髪(ショート)"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [self._get_rabbit_ear_bones(bone)],
            "rigidbody_root_thick": 0.5,
            "rigidbody_end_thick": 0.5,
        }

    def _create_sleeve_settings(self, bone: Any, bone_cnt: Dict[str, int]) -> Dict[str, Any]:
        """袖の設定を作成"""
        if "LowerSleeve" in bone.name:
            abb_name = f"左袖{bone_cnt['Sleeve']}" if "_L_" in bone.name else f"右袖{bone_cnt['Sleeve']}"
            parent_bone_name = "左ひじ" if "_L_" in bone.name else "右ひじ"
        elif "TipSleeve" in bone.name:
            abb_name = f"左袖口{bone_cnt['Sleeve']}" if "_L_" in bone.name else f"右袖口{bone_cnt['Sleeve']}"
            parent_bone_name = "左手首" if "_L_" in bone.name else "左手首"
        else:
            return {}

        return {
            "target_names": ["CLOTH"],
            "abb_name": abb_name,
            "parent_bone_name": parent_bone_name,
            "group": "3",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("単一揺れ物"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [[bone.name, self.model.bone_indexes[bone.tail_index]]],
            "rigidbody_root_thick": 0.5,
            "rigidbody_end_thick": 0.5,
        }

    def _create_hood_string_settings(self, bone: Any, bone_cnt: Dict[str, int]) -> Dict[str, Any]:
        """フード紐の設定を作成"""
        return {
            "target_names": ["CLOTH"],
            "abb_name": f"左紐{bone_cnt['HoodString']}" if "_L_" in bone.name else f"右紐{bone_cnt['HoodString']}",
            "parent_bone_name": "上半身3",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("単一揺れ物"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [[self.model.bone_indexes[bone.parent_index], bone.name, self.model.bone_indexes[bone.tail_index]]],
            "rigidbody_root_thick": 0.3,
            "rigidbody_end_thick": 0.5,
        }

    def _create_hood_settings(self, bone: Any, bone_cnt: Dict[str, int]) -> Dict[str, Any]:
        """フードの設定を作成"""
        return {
            "target_names": ["CLOTH"],
            "abb_name": f"フード{bone_cnt['Hood']}",
            "parent_bone_name": "首",
            "group": "4",
            "direction": logger.transtext("下"),
            "primitive": logger.transtext("単一揺れ物"),
            "exist_physics_clear": logger.transtext("再利用"),
            "target_bones": [[bone.name, self.model.bone_indexes[bone.tail_index]]],
            "rigidbody_root_thick": 0.5,
            "rigidbody_end_thick": 0.5,
        }

    def _get_cat_ear_bones(self, bone: Any) -> List[str]:
        """猫耳のボーンリストを取得"""
        cat_ear_bones = {}
        CAT_EAR_NAME = "CatEar2"
        
        for bname in self.model.bones.keys():
            if CAT_EAR_NAME in bname:
                bkey = bname[bname.find(CAT_EAR_NAME) - 3 : bname.find(CAT_EAR_NAME) + len(CAT_EAR_NAME)]
                if bkey not in cat_ear_bones:
                    cat_ear_bones[bkey] = []
                if 2 > len(cat_ear_bones[bkey]):
                    cat_ear_bones[bkey].append(bname)
                    
        return cat_ear_bones[("_L_CatEar2" if "_L_" in bone.name else "_R_CatEar2")]

    def _get_rabbit_ear_bones(self, bone: Any) -> List[str]:
        """兎耳のボーンリストを取得"""
        rabbit_ear_bones = {}
        RABBIT_EAR_NAME = "RabbitEar2"
        
        for bname in self.model.bones.keys():
            if RABBIT_EAR_NAME in bname:
                bkey = bname[bname.find(RABBIT_EAR_NAME) - 3 : bname.find(RABBIT_EAR_NAME) + len(RABBIT_EAR_NAME)]
                if bkey not in rabbit_ear_bones:
                    rabbit_ear_bones[bkey] = []
                if 2 > len(rabbit_ear_bones[bkey]):
                    rabbit_ear_bones[bkey].append(bname)
                    
        return rabbit_ear_bones[("_L_RabbitEar2" if "_L_" in bone.name else "_R_RabbitEar2")] 